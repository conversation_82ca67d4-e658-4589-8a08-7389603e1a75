import React, { useEffect, useState } from 'react';
import { useLanguage } from '@/context/SimpleLanguageContext';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

/**
 * Debug component to test and display language detection information
 * This component can be temporarily added to any page to verify language detection
 */
export default function LanguageDetectionDebug() {
  const { currentLanguage, changeLanguage } = useLanguage();
  const { t, i18n } = useTranslation();
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    // Gather debug information
    const info = {
      currentLanguage,
      i18nLanguage: i18n.language,
      storedLanguage: localStorage.getItem('i18nextLng'),
      browserLanguage: navigator.language,
      browserLanguages: navigator.languages,
      hasTurkishInBrowser: navigator.languages?.some(lang => 
        lang && lang.toLowerCase().startsWith('tr')
      ),
      timestamp: new Date().toISOString()
    };
    
    setDebugInfo(info);
    console.log('Language Detection Debug Info:', info);
  }, [currentLanguage, i18n.language]);

  const clearStoredLanguage = () => {
    localStorage.removeItem('i18nextLng');
    window.location.reload();
  };

  const simulateTurkishBrowser = () => {
    // This is just for demonstration - in real scenario, user would change browser language
    console.log('To test Turkish detection:');
    console.log('1. Clear localStorage');
    console.log('2. Change browser language to Turkish');
    console.log('3. Reload the page');
  };

  return (
    <Card className="w-full max-w-2xl mx-auto mt-4">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🌐 Language Detection Debug
          <Badge variant={currentLanguage === 'tr' ? 'default' : 'secondary'}>
            {currentLanguage.toUpperCase()}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-semibold mb-2">Current State</h4>
            <div className="space-y-1 text-sm">
              <div>App Language: <Badge>{debugInfo.currentLanguage}</Badge></div>
              <div>i18n Language: <Badge>{debugInfo.i18nLanguage}</Badge></div>
              <div>Stored Language: <Badge>{debugInfo.storedLanguage || 'None'}</Badge></div>
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Browser Detection</h4>
            <div className="space-y-1 text-sm">
              <div>Primary Language: <Badge>{debugInfo.browserLanguage}</Badge></div>
              <div>Has Turkish: <Badge variant={debugInfo.hasTurkishInBrowser ? 'default' : 'secondary'}>
                {debugInfo.hasTurkishInBrowser ? 'Yes' : 'No'}
              </Badge></div>
              <div>All Languages: 
                <div className="flex flex-wrap gap-1 mt-1">
                  {debugInfo.browserLanguages?.map((lang: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {lang}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-semibold mb-2">Test Translation</h4>
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm">
              {t('common.welcome')} - {t('common.language')}: {currentLanguage === 'tr' ? 'Türkçe' : 'English'}
            </p>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button 
            onClick={() => changeLanguage('en')} 
            variant={currentLanguage === 'en' ? 'default' : 'outline'}
            size="sm"
          >
            Switch to English
          </Button>
          <Button 
            onClick={() => changeLanguage('tr')} 
            variant={currentLanguage === 'tr' ? 'default' : 'outline'}
            size="sm"
          >
            Switch to Turkish
          </Button>
          <Button 
            onClick={clearStoredLanguage} 
            variant="destructive"
            size="sm"
          >
            Clear & Reload
          </Button>
          <Button 
            onClick={simulateTurkishBrowser} 
            variant="secondary"
            size="sm"
          >
            Test Instructions
          </Button>
        </div>

        <div className="text-xs text-muted-foreground">
          <p><strong>How to test:</strong></p>
          <ol className="list-decimal list-inside space-y-1 mt-1">
            <li>Click "Clear & Reload" to simulate first-time user</li>
            <li>Change your browser language to Turkish (tr-TR)</li>
            <li>Reload the page - should automatically detect Turkish</li>
            <li>Change browser language to English - should detect English</li>
            <li>Change to any other language - should default to English</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}
