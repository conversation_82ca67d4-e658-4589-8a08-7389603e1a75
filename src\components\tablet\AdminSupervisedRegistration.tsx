import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Fingerprint,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  User,
  ArrowRight,
  ArrowLeft,
  RotateCcw,
  Users,
  Play,
  Pause,
  Square
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { startBulkRegistration, getDeviceInfo } from "@/lib/webauthn";

interface Student {
  id: string;
  email: string;
  name: string;
  status: 'pending' | 'registered' | 'failed' | 'skipped';
  error?: string;
  registeredAt?: string;
  order: number;
}

interface RegistrationSession {
  id: string;
  sessionName: string;
  totalStudents: number;
  registeredStudents: number;
  failedRegistrations: number;
  sessionStatus: 'active' | 'completed' | 'cancelled';
  students: Student[];
}

interface AdminSupervisedRegistrationProps {
  sessionId: string;
  onComplete: () => void;
  onCancel: () => void;
}

export default function AdminSupervisedRegistration({
  sessionId,
  onComplete,
  onCancel
}: AdminSupervisedRegistrationProps) {
  const [session, setSession] = useState<RegistrationSession | null>(null);
  const [currentStudentIndex, setCurrentStudentIndex] = useState(0);
  const [isRegistering, setIsRegistering] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const { t } = useTranslation();

  useEffect(() => {
    const info = getDeviceInfo();
    setDeviceInfo(info);
    loadSession();
  }, [sessionId]);

  const loadSession = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('get_registration_session', {
        session_id_param: sessionId
      });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error || 'Failed to load session');
      }

      const sessionData = data.session;
      const studentsData = data.students || [];

      const formattedSession: RegistrationSession = {
        id: sessionData.id,
        sessionName: sessionData.session_name,
        totalStudents: sessionData.total_students,
        registeredStudents: sessionData.registered_students,
        failedRegistrations: sessionData.failed_registrations,
        sessionStatus: sessionData.session_status,
        students: studentsData.map((s: any) => ({
          id: s.student_id,
          email: s.student_email,
          name: s.student_name,
          status: s.registration_status,
          error: s.error_message,
          registeredAt: s.registered_at,
          order: s.registration_order
        })).sort((a: any, b: any) => a.order - b.order)
      };

      setSession(formattedSession);

      // Find the first pending student
      const firstPendingIndex = formattedSession.students.findIndex(s => s.status === 'pending');
      if (firstPendingIndex !== -1) {
        setCurrentStudentIndex(firstPendingIndex);
      }
    } catch (error) {
      console.error('Error loading session:', error);
      setError(error instanceof Error ? error.message : 'Failed to load session');
    } finally {
      setLoading(false);
    }
  };

  const registerCurrentStudent = async () => {
    if (!session || !deviceInfo) return;

    const currentStudent = session.students[currentStudentIndex];
    if (!currentStudent || currentStudent.status !== 'pending') return;

    try {
      setIsRegistering(true);
      setError(null);

      // Start biometric registration for the current student
      await startBulkRegistration(
        currentStudent.id,
        currentStudent.email,
        session.id,
        deviceInfo.deviceId
      );

      // Reload session to get updated status
      await loadSession();

      // Move to next pending student
      moveToNextPendingStudent();

    } catch (error) {
      console.error('Registration error:', error);
      setError(error instanceof Error ? error.message : 'Registration failed');
      
      // Mark student as failed in the session
      try {
        await supabase
          .from('registration_session_students')
          .update({
            registration_status: 'failed',
            error_message: error instanceof Error ? error.message : 'Registration failed'
          })
          .eq('session_id', session.id)
          .eq('student_id', currentStudent.id);

        await loadSession();
      } catch (updateError) {
        console.error('Error updating student status:', updateError);
      }
    } finally {
      setIsRegistering(false);
    }
  };

  const skipCurrentStudent = async () => {
    if (!session) return;

    const currentStudent = session.students[currentStudentIndex];
    if (!currentStudent) return;

    try {
      await supabase
        .from('registration_session_students')
        .update({
          registration_status: 'skipped',
          error_message: 'Skipped by administrator'
        })
        .eq('session_id', session.id)
        .eq('student_id', currentStudent.id);

      await loadSession();
      moveToNextPendingStudent();
    } catch (error) {
      console.error('Error skipping student:', error);
      setError('Failed to skip student');
    }
  };

  const moveToNextPendingStudent = () => {
    if (!session) return;

    const nextPendingIndex = session.students.findIndex(
      (s, index) => index > currentStudentIndex && s.status === 'pending'
    );

    if (nextPendingIndex !== -1) {
      setCurrentStudentIndex(nextPendingIndex);
    } else {
      // No more pending students, session is complete
      completeSession();
    }
  };

  const completeSession = async () => {
    if (!session) return;

    try {
      await supabase
        .from('registration_sessions')
        .update({
          session_status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', session.id);

      onComplete();
    } catch (error) {
      console.error('Error completing session:', error);
    }
  };

  const cancelSession = async () => {
    if (!session) return;

    try {
      await supabase
        .from('registration_sessions')
        .update({
          session_status: 'cancelled',
          completed_at: new Date().toISOString()
        })
        .eq('id', session.id);

      onCancel();
    } catch (error) {
      console.error('Error cancelling session:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'registered':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'skipped':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'registered': 'default',
      'failed': 'destructive',
      'skipped': 'secondary',
      'pending': 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'} className="text-xs">
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Loading registration session...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !session) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="py-12">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || 'Failed to load registration session'}
            </AlertDescription>
          </Alert>
          <div className="mt-4 text-center">
            <Button onClick={onCancel} variant="outline">
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentStudent = session.students[currentStudentIndex];
  const progress = (session.registeredStudents / session.totalStudents) * 100;
  const pendingStudents = session.students.filter(s => s.status === 'pending');

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Session Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                {session.sessionName}
              </CardTitle>
              <CardDescription>
                Admin-supervised biometric registration session
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => setShowConfirmDialog(true)}>
                <Square className="w-4 h-4 mr-2" />
                Cancel Session
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{session.totalStudents}</div>
              <div className="text-sm text-muted-foreground">Total Students</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{session.registeredStudents}</div>
              <div className="text-sm text-muted-foreground">Registered</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{session.failedRegistrations}</div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{pendingStudents.length}</div>
              <div className="text-sm text-muted-foreground">Remaining</div>
            </div>
          </div>
          
          <Progress value={progress} className="h-3" />
          <div className="text-sm text-muted-foreground mt-2 text-center">
            {session.registeredStudents} of {session.totalStudents} students registered ({Math.round(progress)}%)
          </div>
        </CardContent>
      </Card>

      {/* Current Student Registration */}
      {currentStudent && pendingStudents.length > 0 && (
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Current Student: {currentStudent.name}
            </CardTitle>
            <CardDescription>
              Student {currentStudentIndex + 1} of {session.totalStudents} • {currentStudent.email}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Student Info */}
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-blue-900">Ready for Registration</h3>
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">
                    Position {currentStudentIndex + 1}
                  </Badge>
                </div>
                <p className="text-blue-700 text-sm mb-4">
                  Please call <strong>{currentStudent.name}</strong> to the kiosk for biometric registration.
                </p>

                <div className="bg-white p-3 rounded border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2">Instructions for Student:</h4>
                  <ol className="text-sm text-blue-700 space-y-1">
                    <li>1. Approach the kiosk device</li>
                    <li>2. When prompted, place your finger on the sensor or look at the camera</li>
                    <li>3. Follow the on-screen biometric registration prompts</li>
                    <li>4. Wait for confirmation before moving away</li>
                  </ol>
                </div>
              </div>

              {/* Registration Controls */}
              <div className="flex items-center justify-center gap-4">
                <Button
                  onClick={registerCurrentStudent}
                  disabled={isRegistering || isPaused}
                  size="lg"
                  className="bg-green-600 hover:bg-green-700 text-white px-8"
                >
                  {isRegistering ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Registering...
                    </>
                  ) : (
                    <>
                      <Fingerprint className="w-5 h-5 mr-2" />
                      Start Registration
                    </>
                  )}
                </Button>

                <Button
                  onClick={skipCurrentStudent}
                  disabled={isRegistering}
                  variant="outline"
                  size="lg"
                >
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Skip Student
                </Button>
              </div>

              {/* Error Display */}
              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Registration Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Navigation */}
              <div className="flex items-center justify-between pt-4 border-t">
                <Button
                  onClick={() => setCurrentStudentIndex(Math.max(0, currentStudentIndex - 1))}
                  disabled={currentStudentIndex === 0 || isRegistering}
                  variant="outline"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Previous
                </Button>

                <span className="text-sm text-muted-foreground">
                  Student {currentStudentIndex + 1} of {session.totalStudents}
                </span>

                <Button
                  onClick={() => setCurrentStudentIndex(Math.min(session.students.length - 1, currentStudentIndex + 1))}
                  disabled={currentStudentIndex === session.students.length - 1 || isRegistering}
                  variant="outline"
                >
                  Next
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Session Complete */}
      {pendingStudents.length === 0 && (
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="py-12">
            <div className="text-center">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-green-900 mb-2">Registration Session Complete!</h3>
              <p className="text-green-700 mb-6">
                All students have been processed. {session.registeredStudents} successfully registered.
              </p>
              <Button onClick={completeSession} size="lg" className="bg-green-600 hover:bg-green-700">
                <CheckCircle className="w-5 h-5 mr-2" />
                Complete Session
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Students List */}
      <Card>
        <CardHeader>
          <CardTitle>Students Progress</CardTitle>
          <CardDescription>
            Track the registration status of all students in this session
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 max-h-64 overflow-y-auto">
            {session.students.map((student, index) => (
              <div
                key={student.id}
                className={`flex items-center justify-between p-3 rounded-lg border ${
                  index === currentStudentIndex
                    ? 'bg-blue-50 border-blue-200'
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium">{student.name}</div>
                    <div className="text-sm text-muted-foreground">{student.email}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(student.status)}
                  {getStatusBadge(student.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Registration Session?</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this registration session? This action cannot be undone.
              Students who have already registered will keep their biometric credentials.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Continue Session
            </Button>
            <Button variant="destructive" onClick={cancelSession}>
              Cancel Session
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
