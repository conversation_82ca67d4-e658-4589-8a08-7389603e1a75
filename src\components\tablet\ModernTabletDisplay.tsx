/**
 * 🎨 Modern Tablet Display
 * A stunning, responsive tablet interface with authentication and visual feedback
 */

import { useState, useEffect, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import QRCode from "react-qr-code";
import { supabase } from "@/lib/supabase";
import {
  QrCode,
  Settings,
  RefreshCw,
  Users,
  Clock,
  Wifi,
  WifiOff,
  Battery,
  Monitor,
  Shield,
  CheckCircle,
  AlertCircle,
  Smartphone,
  MapPin,
  Calendar,
  Maximize2,
  Minimize2,
  X
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useAttendanceSettings } from "@/hooks/useAttendanceSettings";
import { validateAttendanceTime } from "@/lib/services/server-time-validation";
import { tabletAuthService } from "@/lib/services/tablet-auth";
import { useResponsiveQR, useAdaptiveQRStyles, useQRAnimations } from "@/hooks/useResponsiveQR";
import { ScanFeedback, type ScanEvent } from "@/components/tablet/ScanFeedback";
import StudentKioskMode from "@/components/tablet/StudentKioskMode";
import BiometricFirstCheckIn from "@/components/tablet/BiometricFirstCheckIn";
import AdminSupervisedRegistration from "@/components/tablet/AdminSupervisedRegistration";
import KioskModeToggle from "@/components/tablet/KioskModeToggle";
import type {
  QRUpdate,
  AttendanceUpdate,
} from "@/lib/services/websocket-service";

interface ModernTabletDisplayProps {
  roomId?: string;
  schoolId?: string;
}

interface RoomInfo {
  id: string;
  name: string;
  building?: string;
  floor?: number;
  block_name?: string;
}

interface TabletStatus {
  isAuthenticated: boolean;
  deviceName: string;
  lastSeen: Date;
  batteryLevel?: number;
  connectionQuality: "excellent" | "good" | "poor" | "offline";
}

export default function ModernTabletDisplay({
  roomId: propRoomId,
  schoolId: propSchoolId
}: ModernTabletDisplayProps) {
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Configuration from URL or props
  const roomId = propRoomId || searchParams.get("room") || "";
  const schoolId = propSchoolId || searchParams.get("school") || "";
  const blockId = searchParams.get("block") || "";
  const isAutoSetup = searchParams.get("setup") === "auto";
  const deviceName = searchParams.get("name") || "";

  // Authentication state
  const [tabletStatus, setTabletStatus] = useState<TabletStatus>({
    isAuthenticated: false,
    deviceName: "",
    lastSeen: new Date(),
    connectionQuality: "offline"
  });

  // QR Code state
  const [qrData, setQrData] = useState<string>("");
  const [qrExpiry, setQrExpiry] = useState<Date | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isActive, setIsActive] = useState(false);

  // Room and attendance state
  const [roomInfo, setRoomInfo] = useState<RoomInfo | null>(null);

  // Kiosk mode state
  const [isKioskMode, setIsKioskMode] = useState(false);
  const [isBiometricFirstMode, setIsBiometricFirstMode] = useState(false);
  const [isAdminRegistrationMode, setIsAdminRegistrationMode] = useState(false);
  const [currentRegistrationSession, setCurrentRegistrationSession] = useState<string | null>(null);
  const [availableRegistrationSessions, setAvailableRegistrationSessions] = useState<any[]>([]);
  const [loadingRegistrationSessions, setLoadingRegistrationSessions] = useState(false);
  const [attendanceCount, setAttendanceCount] = useState<number>(0);
  const [recentScans, setRecentScans] = useState<ScanEvent[]>([]);

  // Debug recentScans changes
  useEffect(() => {
    console.log("🔄 recentScans state changed:", recentScans);
  }, [recentScans]);

  // Connection state
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [websocketService, setWebsocketService] = useState<any>(null);
  
  // Responsive state
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  // QR Code maximization state
  const [isQRMaximized, setIsQRMaximized] = useState(false);

  // Manual refresh state
  const [isManualRefreshing, setIsManualRefreshing] = useState(false);

  // Attendance time settings
  const { settings: attendanceSettings, isWithinRecordingHours } = useAttendanceSettings();
  const [isWithinAttendanceTime, setIsWithinAttendanceTime] = useState<boolean>(false);

  // Responsive QR hooks
  const qrConfig = useResponsiveQR({
    minSize: 250,
    maxSize: 500,
    padding: 60,
    aspectRatio: 0.45
  });
  const qrStyles = useAdaptiveQRStyles(qrConfig);
  const { isVisible, triggerScanAnimation, animationStyles } = useQRAnimations(qrConfig);

  // Auto-refresh timer
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Handle window resize for responsive QR code
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // Format time remaining
  const formatTimeLeft = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Initialize tablet authentication
  const initializeTablet = async () => {
    try {
      console.log("🔧 Initializing tablet with params:", {
        isAutoSetup,
        schoolId,
        roomId,
        blockId,
        deviceName
      });

      // Auto-setup if parameters provided
      if (isAutoSetup && schoolId && roomId) {
        console.log("🚀 Starting auto-setup process...");

        const result = await tabletAuthService.registerTablet(
          schoolId,
          roomId,
          blockId || undefined,
          deviceName || undefined
        );

        console.log("📋 Registration result:", result);

        if (result.success) {
          setTabletStatus({
            isAuthenticated: true,
            deviceName: result.device?.deviceName || t("admin.tablets.display.unknownTablet", "Unknown Tablet"),
            lastSeen: new Date(),
            connectionQuality: "good"
          });

          toast({
            title: t("admin.tablets.display.tabletConfigured", "Tablet Configured! 🎉"),
            description: t("admin.tablets.display.successfullyRegistered", "Successfully registered as {{deviceName}}", { deviceName: result.device?.deviceName }),
          });

          console.log("✅ Tablet setup completed successfully");
        } else {
          throw new Error(result.error || "Registration failed");
        }
      } else {
        console.log("🔍 Trying to authenticate existing device...");

        // Try to authenticate existing device
        const result = await tabletAuthService.authenticateTablet();

        console.log("🔐 Authentication result:", result);

        if (result.success) {
          setTabletStatus({
            isAuthenticated: true,
            deviceName: result.device?.deviceName || t("admin.tablets.display.unknownTablet", "Unknown Tablet"),
            lastSeen: new Date(),
            connectionQuality: "good"
          });

          console.log("✅ Existing tablet authenticated successfully");
        } else {
          console.log("❌ No existing authentication found");
        }
      }
    } catch (error) {
      console.error("❌ Tablet initialization failed:", error);
      toast({
        title: t("admin.tablets.display.setupRequired"),
        description: t("admin.tablets.display.setupRequiredDescription"),
        variant: "destructive",
      });
    }
  };

  // Load available registration sessions for this device
  const loadRegistrationSessions = async () => {
    if (!tabletStatus.isAuthenticated) return;

    setLoadingRegistrationSessions(true);
    try {
      const { deviceId } = tabletAuthService.getCurrentDevice();
      if (!deviceId) {
        console.error('No device ID available');
        setAvailableRegistrationSessions([]);
        setLoadingRegistrationSessions(false);
        return;
      }

      const response = await fetch('/api/admin/biometric-registration/sessions/active', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ deviceId }),
      });

      if (response.ok) {
        const sessions = await response.json();
        setAvailableRegistrationSessions(sessions);

        // If there's only one active session, auto-select it
        if (sessions.length === 1) {
          setCurrentRegistrationSession(sessions[0].id);
        }
      } else {
        console.error('Failed to load registration sessions');
        setAvailableRegistrationSessions([]);
      }
    } catch (error) {
      console.error('Error loading registration sessions:', error);
      setAvailableRegistrationSessions([]);
    } finally {
      setLoadingRegistrationSessions(false);
    }
  };

  // Initialize tablet authentication
  useEffect(() => {
    initializeTablet();
  }, []);

  // Load registration sessions when entering admin registration mode
  useEffect(() => {
    if (isAdminRegistrationMode && tabletStatus.isAuthenticated) {
      loadRegistrationSessions();
    }
  }, [isAdminRegistrationMode, tabletStatus.isAuthenticated]);

  // Monitor attendance time settings using server-side validation
  useEffect(() => {
    const checkAttendanceTime = async () => {
      if (tabletStatus.schoolId) {
        const timeValidation = await validateAttendanceTime(tabletStatus.schoolId);

        if (timeValidation) {
          const wasWithinTime = isWithinAttendanceTime;
          const nowWithinTime = timeValidation.is_valid;
          setIsWithinAttendanceTime(nowWithinTime);

          // If attendance time just started (transition from outside to inside)
          if (!wasWithinTime && nowWithinTime) {
            fetchLatestQRCode();
          }

          // If outside attendance time and QR is active, clear it
          if (!nowWithinTime && isActive) {
            console.log("🚫 Outside attendance time, clearing QR code");
            setIsActive(false);
            setQrData("");
            setQrExpiry(null);
          }
        }
      }
    };

    // Check immediately
    checkAttendanceTime();

    // Check every 30 seconds for server-side validation (less frequent than client-side)
    const interval = setInterval(checkAttendanceTime, 30000);

    return () => clearInterval(interval);
  }, [tabletStatus.schoolId, isActive, isWithinAttendanceTime]);

  // Fetch room information and today's attendance count
  useEffect(() => {
    if (tabletStatus.isAuthenticated && roomId) {
      fetchRoomInfo();
      fetchTodayAttendanceCount();
      fetchRecentScans();
    }
  }, [tabletStatus.isAuthenticated, roomId]);

  const updateConnectionQuality = (quality: TabletStatus["connectionQuality"]) => {
    setTabletStatus(prev => ({ ...prev, connectionQuality: quality }));
  };

  // Manual QR code refresh
  const handleManualRefresh = async () => {
    if (isManualRefreshing || isRefreshing) {
      return; // Prevent multiple simultaneous refreshes
    }

    try {
      setIsManualRefreshing(true);
      console.log("🔄 Manual QR refresh requested");

      // Force fetch even if outside attendance time for manual refresh
      await fetchLatestQRCode(true);

      // Show success feedback
      toast({
        title: "✅ QR Code Refreshed",
        description: "QR code has been updated successfully",
      });
    } catch (error) {
      console.error("❌ Manual refresh failed:", error);
      toast({
        title: "❌ Refresh Failed",
        description: "Failed to refresh QR code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsManualRefreshing(false);
    }
  };

  const fetchRoomInfo = async () => {
    try {
      const { data: room, error } = await supabase
        .from("rooms")
        .select(`
          id,
          name,
          building,
          floor,
          current_qr_code,
          qr_expiry,
          blocks (
            name
          )
        `)
        .eq("id", roomId)
        .single();

      if (error) throw error;

      setRoomInfo({
        id: room.id,
        name: room.name,
        building: room.building,
        floor: room.floor,
        block_name: room.blocks?.name,
      });

      // Load existing QR code if available and within attendance time (use current state)
      if (room.current_qr_code && room.qr_expiry && isWithinAttendanceTime) {
        const expiry = new Date(room.qr_expiry);
        if (expiry.getTime() > Date.now()) {
          setQrData(room.current_qr_code);
          setQrExpiry(expiry);
          setIsActive(true);
        }
      }
    } catch (error) {
      console.error("Error fetching room info:", error);
      updateConnectionQuality("poor");
    }
  };

  const fetchTodayAttendanceCount = async () => {
    try {
      // Get today's date range
      const today = new Date();
      const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);



      const { data: todayAttendance, error } = await supabase
        .from("attendance_records")
        .select("id")
        .eq("room_id", roomId)
        .gte("created_at", startOfToday.toISOString())
        .lt("created_at", endOfToday.toISOString());

      if (error) {
        console.error("Error fetching today's attendance:", error);
        return;
      }

      const todayCount = todayAttendance?.length || 0;
      setAttendanceCount(todayCount);
    } catch (error) {
      console.error("Error fetching today's attendance count:", error);
    }
  };

  const fetchRecentScans = async () => {
    try {


      const { data: recentAttendance, error } = await supabase
        .from("attendance_records")
        .select(`
          *,
          profiles!inner(
            id,
            name,
            course,
            student_id
          ),
          rooms!inner(
            id,
            name
          )
        `)
        .eq("room_id", roomId)
        .order("created_at", { ascending: false })
        .limit(5);

      if (error) {
        console.error("Error fetching recent scans:", error);
        return;
      }



      if (recentAttendance && recentAttendance.length > 0) {
        const scanEvents: ScanEvent[] = recentAttendance.map((record) => ({
          id: record.id,
          studentName: record.profiles?.name || `Student ${record.student_id.substring(0, 8)}`,
          studentId: record.profiles?.student_id || record.student_id,
          timestamp: new Date(record.created_at),
          status: "success",
          grade: record.profiles?.course || "N/A",
          className: record.rooms?.name || "N/A",
        }));

        setRecentScans(scanEvents);
      }
    } catch (error) {
      console.error("Error fetching recent scans:", error);
    }
  };

  const fetchLatestQRCode = async (forceRefresh = false) => {
    if (isRefreshing) {
      console.log("⏭️ QR fetch already in progress, skipping");
      return; // Prevent multiple simultaneous requests
    }

    // Check if we're within attendance time before fetching (unless forced)
    if (!forceRefresh && tabletStatus.schoolId) {
      const timeValidation = await validateAttendanceTime(tabletStatus.schoolId);
      if (timeValidation && !timeValidation.is_valid) {
        console.log("⏰ Outside attendance time, skipping QR fetch");
        return;
      }
    }

    try {
      setIsRefreshing(true);
  

      const { data: room, error } = await supabase
        .from("rooms")
        .select("current_qr_code, qr_expiry")
        .eq("id", roomId)
        .single();

      if (error) throw error;

      if (room.current_qr_code && room.qr_expiry) {
        const newExpiry = new Date(room.qr_expiry);
        const now = Date.now();
        const timeUntilNewExpiry = newExpiry.getTime() - now;



        if (timeUntilNewExpiry > 0) {
          // Check if this is actually a newer QR code than what we currently have
          const currentExpiryTime = qrExpiry?.getTime() || 0;
          const newExpiryTime = newExpiry.getTime();

          if (newExpiryTime > currentExpiryTime || !isActive) {
            console.log("✅ Updating to newer QR code");
            setQrData(room.current_qr_code);
            setQrExpiry(newExpiry);
            setIsActive(true);
            setTimeLeft(Math.floor(timeUntilNewExpiry / 1000));
          } else {
            console.log("⏭️ QR code is not newer than current, keeping existing");
          }
        } else {

          setIsActive(false);
          setQrData("");
          setQrExpiry(null);
        }
      } else {
        console.log("❌ No QR code found in database");
        setIsActive(false);
        setQrData("");
        setQrExpiry(null);
      }

      updateConnectionQuality("good");
    } catch (error) {
      console.error("❌ Error fetching QR code:", error);
      updateConnectionQuality("poor");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Timer for countdown with proactive refresh
  useEffect(() => {
    if (qrExpiry && isActive) {
      timerRef.current = setInterval(() => {
        const remaining = Math.max(
          0,
          Math.floor((qrExpiry.getTime() - Date.now()) / 1000)
        );
        setTimeLeft(remaining);

        // Fetch new QR code when 1 second remaining for seamless transition
        if (remaining === 1) {
          console.log(`🔄 QR expiring in 1 second, fetching replacement for seamless transition`);
          // Only fetch if within attendance time (use current state)
          if (isWithinAttendanceTime) {
            fetchLatestQRCode();
          }
        } else if (remaining <= 0) {
          // Only set inactive if no new QR code was received
          // Only fetch if within attendance time (use current state)
          if (isWithinAttendanceTime) {
            fetchLatestQRCode();
          }

          // Give a brief moment for new QR code to arrive before showing inactive state
          setTimeout(() => {
            const currentRemaining = Math.max(
              0,
              Math.floor((qrExpiry.getTime() - Date.now()) / 1000)
            );
            if (currentRemaining <= 0) {
              setIsActive(false);
              setQrData("");
              setQrExpiry(null);
            }
          }, 2000); // 2 second grace period
        }
      }, 1000);

      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }
  }, [qrExpiry, isActive]);

  // Initialize WebSocket service
  useEffect(() => {
    const initWebSocket = async () => {
      try {
        console.log("🔌 Initializing WebSocket service...");
        const wsModule = await import("@/lib/services/websocket-service");
        console.log("🔌 WebSocket module loaded:", wsModule);
        setWebsocketService(wsModule.websocketService);
        setIsConnected(true);
        updateConnectionQuality("excellent");
        console.log("✅ WebSocket service initialized successfully");
      } catch (error) {
        console.warn("❌ WebSocket service not available, using polling fallback:", error);
        setIsConnected(false);
        updateConnectionQuality("poor");
      }
    };

    if (tabletStatus.isAuthenticated) {
      console.log("🔐 Tablet authenticated, initializing WebSocket...");
      initWebSocket();
    }
  }, [tabletStatus.isAuthenticated]);

  // Subscribe to real-time updates with proper cleanup
  useEffect(() => {
    console.log("🔗 Checking subscription conditions:", {
      schoolId,
      roomId,
      hasWebsocketService: !!websocketService,
      isAuthenticated: tabletStatus.isAuthenticated
    });

    if (!schoolId || !roomId || !websocketService || !tabletStatus.isAuthenticated) {
      console.log("⏸️ Skipping subscription - missing requirements");
      return;
    }

    let unsubscribeQR: (() => void) | undefined;
    let unsubscribeAttendance: (() => void) | undefined;
    let isSubscribed = false;

    const setupSubscriptions = async () => {
      try {
        console.log("📡 Setting up real-time subscriptions...");

        // Add a small delay to ensure any previous subscriptions are cleaned up
        await new Promise(resolve => setTimeout(resolve, 100));

        if (isSubscribed) {
          console.log("⚠️ Already subscribed, skipping");
          return;
        }

        // QR Updates
        console.log("🔔 Subscribing to QR updates for room:", roomId);
        unsubscribeQR = websocketService.subscribeToQRUpdates(
          schoolId,
          (update: QRUpdate) => {
            console.log("📡 QR Update received for room check:", update.data.room_id, "vs", roomId);
            if (update.data.room_id === roomId) {
              handleQRUpdate(update);
            }
          },
          roomId
        );

        // Attendance Updates
        console.log("🔔 Subscribing to attendance updates for room:", roomId);
        unsubscribeAttendance = websocketService.subscribeToAttendance(
          schoolId,
          (update: AttendanceUpdate) => {
            console.log("📊 Raw attendance update received:", update);
            console.log("📊 Room check:", update.data.room_id, "vs", roomId);
            if (update.data.room_id === roomId) {
              console.log("✅ Processing attendance update for our room");
              handleAttendanceUpdate(update);
            } else {
              console.log("❌ Ignoring attendance update for different room");
            }
          },
          roomId
        );

        isSubscribed = true;
        console.log("✅ Successfully subscribed to real-time updates for room:", roomId);
        updateConnectionQuality("excellent");
      } catch (error) {
        console.error("❌ Failed to subscribe to real-time updates:", error);
        updateConnectionQuality("poor");
      }
    };

    setupSubscriptions();

    return () => {
      console.log("🔌 Cleaning up subscriptions");
      isSubscribed = false;
      unsubscribeQR?.();
      unsubscribeAttendance?.();
    };
  }, [schoolId, roomId, websocketService, tabletStatus.isAuthenticated]);

  // Polling fallback when websocket is not available
  useEffect(() => {
    if (!websocketService && roomId && tabletStatus.isAuthenticated) {
      const pollInterval = setInterval(() => {
        // Only fetch QR codes if within attendance time (use current state)
        if (isWithinAttendanceTime) {
          fetchLatestQRCode();
        }
        fetchTodayAttendanceCount();
        fetchRecentScans();
      }, 5000); // Poll every 5 seconds for faster response

      return () => clearInterval(pollInterval);
    }
  }, [websocketService, roomId, tabletStatus.isAuthenticated, isWithinAttendanceTime]);

  // Periodic refresh of attendance data even with websocket
  useEffect(() => {
    if (roomId && tabletStatus.isAuthenticated) {
      const refreshInterval = setInterval(() => {
        fetchTodayAttendanceCount();
        fetchRecentScans();
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(refreshInterval);
    }
  }, [roomId, tabletStatus.isAuthenticated]);

  const handleQRUpdate = (update: QRUpdate) => {
    console.log("📡 QR Update received:", update);

    // Check if we're within attendance time before processing QR updates (use current state)
    if (!isWithinAttendanceTime) {
      console.log("⏰ Outside attendance time, ignoring QR update");
      return;
    }

    if (update.type === "qr_generated" || update.type === "qr_refreshed") {
      // Only apply QR updates if current QR is about to expire or doesn't exist
      const currentExpiryTime = qrExpiry?.getTime() || 0;
      const now = Date.now();
      const timeUntilExpiry = currentExpiryTime - now;

      // Apply update if:
      // 1. No current QR code (tablet just started)
      // 2. Current QR expires in 3 seconds or less
      // 3. Current QR has already expired
      if (!isActive || timeUntilExpiry <= 3000) {
        console.log(`🔄 Applying QR update (current expires in ${Math.floor(timeUntilExpiry / 1000)}s)`);
        fetchLatestQRCode();
      } else {
        console.log(`⏳ Deferring QR update (current has ${Math.floor(timeUntilExpiry / 1000)}s remaining)`);
        // The timer will handle fetching the new QR when current one expires
      }
    } else if (update.type === "qr_expired") {

      // Only clear if our current QR has actually expired
      if (timeLeft <= 0) {
        setIsActive(false);
        setQrData("");
        setQrExpiry(null);
      }
    }

    setLastUpdate(new Date());
    updateConnectionQuality("excellent");
  };

  const handleAttendanceUpdate = (update: AttendanceUpdate) => {


    // Convert to ScanEvent format with better fallbacks
    const scanEvent: ScanEvent = {
      id: `${update.data.student_id || 'unknown'}-${Date.now()}`,
      studentName: update.data.student_name || `Student ${update.data.student_id?.substring(0, 8) || 'Unknown'}`,
      studentId: update.data.student_id || "",
      timestamp: new Date(update.data.timestamp || Date.now()),
      status: "success",
      grade: update.data.grade || "N/A",
      className: update.data.class_name || update.data.room_name || "N/A",
    };

    console.log("📊 Created scanEvent:", scanEvent);
    console.log("📊 Current recentScans before update:", recentScans);

    // Force update even if data might be incomplete
    setRecentScans(prev => {
      const newScans = [scanEvent, ...prev.slice(0, 4)];
      console.log("📊 New recentScans after update:", newScans);
      return newScans;
    });

    setAttendanceCount(prev => prev + 1);
    triggerScanAnimation();

    // Show toast notification
    toast({
      title: "✅ Student Checked In",
      description: `${scanEvent.studentName} marked present`,
    });
  };

  // If not authenticated, show enhanced setup screen
  if (!tabletStatus.isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden flex items-center justify-center p-4 sm:p-6 lg:p-8">
        {/* Responsive Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-20 -right-20 sm:-top-40 sm:-right-40 w-40 h-40 sm:w-80 sm:h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-20 -left-20 sm:-bottom-40 sm:-left-40 w-48 h-48 sm:w-96 sm:h-96 bg-gradient-to-tr from-pink-400/20 to-indigo-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 sm:w-64 sm:h-64 bg-gradient-to-r from-cyan-400/10 to-blue-600/10 rounded-full blur-2xl animate-pulse delay-500"></div>
        </div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-sm sm:max-w-md lg:max-w-lg w-full relative z-10"
        >
          <Card className="text-center shadow-2xl border-0 bg-white/10 backdrop-blur-xl border border-white/20 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-white/10 to-transparent"></div>
            <CardContent className="relative z-10 p-6 sm:p-8 lg:p-12">
              <motion.div 
                className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 mx-auto mb-6 sm:mb-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-2xl"
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              >
                <Smartphone className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-white" />
              </motion.div>
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-black text-white mb-4 sm:mb-6 tracking-tight">
                {t("admin.tablets.display.setupRequired")}
              </h1>
              <p className="text-white/80 mb-6 sm:mb-8 text-sm sm:text-base lg:text-lg leading-relaxed px-2">
                {t("admin.tablets.display.setupRequiredDescription")}
              </p>
              <Button
                onClick={initializeTablet}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg py-3 sm:py-4 text-base sm:text-lg font-semibold"
                size="lg"
              >
                <RefreshCw className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3" />
                {t("admin.tablets.display.retrySetup")}
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  // QR Maximization Modal
  if (isQRMaximized) {
    return (
      <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-20 -right-20 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-gradient-to-tr from-pink-400/10 to-indigo-600/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        {/* Close Button - Smaller */}
        <button
          onClick={() => setIsQRMaximized(false)}
          className="absolute top-4 right-4 z-[60] p-2 bg-white/10 backdrop-blur-xl rounded-full border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
        >
          <X className="w-4 h-4" />
        </button>

        {/* Content Container with proper spacing for close button */}
        <div className="h-full w-full flex items-center justify-center pt-20 pb-16 px-8">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="relative z-50 flex flex-col items-center justify-center max-h-full"
          >
            {isActive && qrData ? (
              <div className="flex flex-col items-center justify-center space-y-8 max-h-full">
                {/* Calculate QR code size for consistent width */}
                {(() => {
                  const qrSize = Math.min(450, Math.max(350, Math.min(windowWidth - 150, window.innerHeight - 300)));
                  const containerWidth = qrSize + 64; // QR size + padding (32px each side)

                  return (
                    <>
                      {/* Styled Title at Top - Matching QR Container Width */}
                      <div
                        className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm p-4 rounded-2xl border border-white/30"
                        style={{ width: `${containerWidth}px` }}
                      >
                        <div className="flex items-center justify-center gap-3">
                          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                            <Smartphone className="w-5 h-5 text-white" />
                          </div>
                          <span className="text-xl font-bold text-white">
                            {t("admin.tablets.display.scanToMarkAttendance")}
                          </span>
                        </div>
                      </div>
                    </>
                  );
                })()}

                {/* Large QR Code - Consistent with Title Width */}
                <div className="flex items-center justify-center flex-shrink-0">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-3xl blur-2xl scale-110"></div>
                    <div className="relative bg-white/95 backdrop-blur-sm p-8 rounded-3xl border-4 border-white/50 shadow-2xl flex items-center justify-center">
                      <QRCode
                        value={qrData}
                        size={Math.min(450, Math.max(350, Math.min(windowWidth - 150, window.innerHeight - 300)))} // Consistent size calculation
                        style={{
                          height: "auto",
                          maxWidth: "100%",
                          width: "100%",
                          display: "block"
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Timer with bottom padding to prevent cutoff */}
                <motion.div
                  className={`text-2xl font-black transition-all duration-500 flex-shrink-0 pb-4 ${
                    timeLeft < 60
                      ? "text-red-400 animate-pulse drop-shadow-[0_0_15px_rgba(239,68,68,0.8)]"
                      : timeLeft < 120
                      ? "text-orange-400 drop-shadow-[0_0_15px_rgba(251,146,60,0.8)]"
                      : "text-white drop-shadow-[0_0_15px_rgba(59,130,246,0.8)]"
                  }`}
                  animate={{ scale: timeLeft < 10 ? [1, 1.05, 1] : 1 }}
                  transition={{ duration: 1, repeat: timeLeft < 10 ? Infinity : 0 }}
                >
                  {formatTimeLeft(timeLeft)}
                </motion.div>


              </div>
            ) : (
              <div className="flex flex-col items-center justify-center space-y-6">
                <div className="w-24 h-24 rounded-full flex items-center justify-center bg-gray-500/20">
                  <QrCode className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-2xl font-bold text-white text-center">
                  {isWithinAttendanceTime ? t("admin.tablets.display.preparingQR") : t("admin.tablets.display.attendanceClosed")}
                </h3>
                <p className="text-white/80 text-base text-center max-w-md">
                  {isWithinAttendanceTime ? (
                    t("admin.tablets.display.qrWillAppear")
                  ) : attendanceSettings ? (
                    `${t("admin.tablets.display.opensAt")} ${attendanceSettings.recording_start_time.substring(0, 5)}`
                  ) : (
                    t("admin.tablets.display.waitingConfig")
                  )}
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800 relative overflow-hidden flex flex-col">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-40 h-40 sm:w-80 sm:h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-20 -left-20 w-48 h-48 sm:w-96 sm:h-96 bg-gradient-to-tr from-pink-400/20 to-indigo-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 sm:w-64 sm:h-64 bg-gradient-to-r from-cyan-400/10 to-blue-600/10 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>

      {/* Mode Selection */}
      <div className="absolute top-4 right-4 z-30 space-y-2">
        <div className="flex flex-col gap-2">
          <Button
            variant={!isKioskMode && !isBiometricFirstMode && !isAdminRegistrationMode ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setIsKioskMode(false);
              setIsBiometricFirstMode(false);
              setIsAdminRegistrationMode(false);
            }}
            className="shadow-lg"
          >
            <QrCode className="w-4 h-4 mr-2" />
            QR Mode
          </Button>

          <Button
            variant={isKioskMode ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setIsKioskMode(true);
              setIsBiometricFirstMode(false);
              setIsAdminRegistrationMode(false);
            }}
            className="shadow-lg"
          >
            <Smartphone className="w-4 h-4 mr-2" />
            Email + Bio
          </Button>

          <Button
            variant={isBiometricFirstMode ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setIsKioskMode(false);
              setIsBiometricFirstMode(true);
              setIsAdminRegistrationMode(false);
            }}
            className="shadow-lg"
          >
            <Shield className="w-4 h-4 mr-2" />
            Bio First
          </Button>

          <Button
            variant={isAdminRegistrationMode ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setIsKioskMode(false);
              setIsBiometricFirstMode(false);
              setIsAdminRegistrationMode(true);
            }}
            className="shadow-lg"
          >
            <Users className="w-4 h-4 mr-2" />
            Admin Reg
          </Button>
        </div>
      </div>

      {/* Compact Status Bar */}
      <div className="relative z-10 bg-white/10 backdrop-blur-xl border-b border-white/20 px-4 py-2 shadow-2xl flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="p-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg">
                <Monitor className="w-4 h-4 text-white" />
              </div>
              <div>
                <span className="font-bold text-white text-sm">{tabletStatus.deviceName}</span>
                <Badge className="bg-green-500/20 text-green-300 border-green-400/30 text-xs backdrop-blur-sm ml-2">
                  <Shield className="w-2 h-2 mr-1" />
                  {t("admin.tablets.display.authenticated")}
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg px-2 py-1 border border-white/20">
              <Wifi className="w-4 h-4 text-green-400" />
              <span className="text-xs text-white/90 font-medium hidden sm:inline">
                {tabletStatus.connectionQuality === "excellent" && t("admin.tablets.display.excellent")}
                {tabletStatus.connectionQuality === "good" && t("admin.tablets.display.good")}
                {tabletStatus.connectionQuality === "poor" && t("admin.tablets.display.poor")}
                {tabletStatus.connectionQuality === "offline" && t("admin.tablets.display.offline")}
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg px-2 py-1 border border-white/20">
              <Clock className="w-4 h-4 text-blue-400" />
              <span className="text-white/90 font-mono text-sm font-medium">
                {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="relative z-10 flex-1 overflow-y-auto overflow-x-hidden">
        <div className="p-3 min-h-full">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 min-h-full">
          {/* Main Content Area */}
          <div className="lg:col-span-2 flex flex-col gap-2">
            {/* Compact Room Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="flex-shrink-0"
            >
              <Card className="border-0 shadow-2xl bg-white/10 backdrop-blur-xl border border-white/20 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-white/10 to-transparent"></div>
                <CardContent className="relative z-10 p-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <h1 className="text-lg sm:text-xl font-black text-white mb-1 tracking-tight">
                        {roomInfo?.name || t("admin.tablets.display.loading")}
                      </h1>
                      <div className="flex items-center gap-2 text-white/80">
                        {roomInfo?.block_name && (
                          <div className="flex items-center gap-1 bg-white/10 backdrop-blur-sm rounded-full px-2 py-0.5 border border-white/20">
                            <MapPin className="w-3 h-3 text-blue-300" />
                            <span className="font-medium text-xs">{t("admin.tablets.display.block")} {roomInfo.block_name}</span>
                          </div>
                        )}
                        {roomInfo?.building && (
                          <div className="flex items-center gap-1 bg-white/10 backdrop-blur-sm rounded-full px-2 py-0.5 border border-white/20">
                            <span className="font-medium text-xs">{roomInfo.building}</span>
                          </div>
                        )}
                        {roomInfo?.floor && (
                          <div className="flex items-center gap-1 bg-white/10 backdrop-blur-sm rounded-full px-2 py-0.5 border border-white/20">
                            <span className="font-medium text-xs">{t("admin.tablets.display.floor")} {roomInfo.floor}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    {/* Attendance Status */}
                    <div className="text-right">
                      <Badge
                        className={`text-xs px-3 py-1 font-bold border-2 ${
                          isWithinAttendanceTime
                            ? "bg-green-500/20 text-green-300 border-green-400/50 shadow-green-500/25 shadow-lg"
                            : "bg-red-500/20 text-red-300 border-red-400/50 shadow-red-500/25 shadow-lg"
                        }`}
                      >
                        {isWithinAttendanceTime ? `🟢 ${t("admin.tablets.display.liveAttendance")}` : `🔴 ${t("admin.tablets.display.closed")}`}
                      </Badge>
                      <p className="text-xs text-white/70 mt-1 font-mono">
                        {attendanceSettings ? (
                          `${attendanceSettings.recording_start_time.substring(0, 5)} - ${attendanceSettings.recording_end_time.substring(0, 5)}`
                        ) : (
                          t("admin.tablets.display.loading")
                        )}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            {/* Main Content - QR Code Display or Kiosk Mode */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="flex-1"
            >
              <Card className="border-0 shadow-2xl bg-white/10 backdrop-blur-xl border border-white/20 overflow-hidden min-h-[400px]">
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-white/10 to-transparent"></div>

                {isKioskMode ? (
                  /* Student Kiosk Mode - Email + Biometric */
                  <div className="relative z-10 p-6 flex items-center justify-center min-h-[400px]">
                    <StudentKioskMode
                      roomId={roomId}
                      schoolId={schoolId}
                      onSuccess={(studentId, studentName) => {
                        // Handle successful check-in
                        console.log(`Student ${studentName} (${studentId}) checked in successfully`);
                        // Refresh attendance count
                        fetchTodayAttendanceCount();
                        fetchRecentScans();
                      }}
                      onCancel={() => setIsKioskMode(false)}
                    />
                  </div>
                ) : isBiometricFirstMode ? (
                  /* Biometric-First Mode */
                  <div className="relative z-10 p-6 flex items-center justify-center min-h-[400px]">
                    <BiometricFirstCheckIn
                      roomId={roomId}
                      schoolId={schoolId}
                      onSuccess={(studentId, studentName) => {
                        // Handle successful check-in
                        console.log(`Student ${studentName} (${studentId}) checked in successfully via biometric-first`);
                        // Refresh attendance count
                        fetchTodayAttendanceCount();
                        fetchRecentScans();
                      }}
                      onError={(error) => {
                        console.error('Biometric-first check-in error:', error);
                      }}
                    />
                  </div>
                ) : isAdminRegistrationMode ? (
                  /* Admin Registration Mode */
                  <div className="relative z-10 p-6 min-h-[400px]">
                    {currentRegistrationSession ? (
                      <AdminSupervisedRegistration
                        sessionId={currentRegistrationSession}
                        onComplete={() => {
                          setCurrentRegistrationSession(null);
                          setIsAdminRegistrationMode(false);
                        }}
                        onCancel={() => {
                          setCurrentRegistrationSession(null);
                          setIsAdminRegistrationMode(false);
                        }}
                      />
                    ) : (
                      <div className="py-8">
                        {loadingRegistrationSessions ? (
                          <div className="text-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                            <p className="text-muted-foreground">Loading registration sessions...</p>
                          </div>
                        ) : availableRegistrationSessions.length === 0 ? (
                          <div className="text-center py-12">
                            <Users className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-xl font-semibold mb-2">Admin Registration Mode</h3>
                            <p className="text-muted-foreground mb-6">
                              No active registration session. Please create a session from the admin dashboard.
                            </p>
                            <div className="space-y-3">
                              <Button onClick={loadRegistrationSessions} variant="outline">
                                🔄 Refresh Sessions
                              </Button>
                              <Button onClick={() => setIsAdminRegistrationMode(false)}>
                                Switch to QR Mode
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-6">
                            <div className="text-center">
                              <Users className="w-12 h-12 text-primary mx-auto mb-3" />
                              <h3 className="text-xl font-semibold mb-2">Select Registration Session</h3>
                              <p className="text-muted-foreground">
                                Choose an active registration session to begin biometric registration
                              </p>
                            </div>

                            <div className="grid gap-4 max-w-2xl mx-auto">
                              {availableRegistrationSessions.map((session) => (
                                <div
                                  key={session.id}
                                  className="border rounded-lg p-4 hover:bg-muted/50 cursor-pointer transition-colors"
                                  onClick={() => setCurrentRegistrationSession(session.id)}
                                >
                                  <div className="flex items-center justify-between mb-3">
                                    <h4 className="font-semibold">{session.session_name}</h4>
                                    <Badge variant="secondary">
                                      {session.progress.percentage}% Complete
                                    </Badge>
                                  </div>

                                  <div className="space-y-2 text-sm text-muted-foreground">
                                    <div className="flex justify-between">
                                      <span>Total Students:</span>
                                      <span>{session.progress.total}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span>Registered:</span>
                                      <span className="text-green-600">{session.progress.registered}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span>Pending:</span>
                                      <span className="text-yellow-600">{session.progress.pending}</span>
                                    </div>
                                    {session.progress.failed > 0 && (
                                      <div className="flex justify-between">
                                        <span>Failed:</span>
                                        <span className="text-red-600">{session.progress.failed}</span>
                                      </div>
                                    )}
                                  </div>

                                  <div className="mt-3">
                                    <div className="w-full bg-muted rounded-full h-2">
                                      <div
                                        className="bg-primary h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${session.progress.percentage}%` }}
                                      />
                                    </div>
                                  </div>

                                  <div className="mt-3 text-xs text-muted-foreground">
                                    Created: {new Date(session.created_at).toLocaleString()}
                                  </div>
                                </div>
                              ))}
                            </div>

                            <div className="text-center space-y-3">
                              <Button onClick={loadRegistrationSessions} variant="outline">
                                🔄 Refresh Sessions
                              </Button>
                              <Button onClick={() => setIsAdminRegistrationMode(false)} variant="ghost">
                                Switch to QR Mode
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  /* Regular QR Code Display */
                  <>
                    {/* QR Header with Maximize Button */}
                    <div className="relative z-10 p-3 border-b border-white/20 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <div className="p-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                          <QrCode className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-white font-bold text-sm">{t("admin.tablets.display.attendanceQRCode")}</span>
                      </div>

                      <div className="flex items-center gap-2">
                        {/* Refresh Button */}
                        <button
                          onClick={handleManualRefresh}
                          disabled={isManualRefreshing || isRefreshing}
                          className="p-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 text-white hover:bg-white/20 transition-all duration-300 group disabled:opacity-50 disabled:cursor-not-allowed"
                          title={t("admin.tablets.display.refreshQRCode", "Refresh QR Code")}
                        >
                          <RefreshCw className={`w-4 h-4 group-hover:scale-110 transition-transform ${isManualRefreshing || isRefreshing ? 'animate-spin' : ''}`} />
                        </button>

                        {/* Maximize Button */}
                        {isActive && qrData && (
                          <button
                            onClick={() => setIsQRMaximized(true)}
                            className="p-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 text-white hover:bg-white/20 transition-all duration-300 group"
                            title={t("admin.tablets.display.maximizeQRCode", "Maximize QR Code")}
                          >
                            <Maximize2 className="w-4 h-4 group-hover:scale-110 transition-transform" />
                          </button>
                        )}
                      </div>
                    </div>

                    <CardContent className="relative z-10 p-3 flex flex-col justify-center items-center min-h-[320px]">
                      {isActive && qrData ? (
                        <div className="text-center space-y-3 flex flex-col items-center justify-center flex-1">
                          {/* Compact QR Code - Properly Centered */}
                          <motion.div
                            style={{...animationStyles.scanEffect}}
                            className="relative flex items-center justify-center"
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.5 }}
                          >
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-xl blur-lg scale-110"></div>
                            <div className="relative bg-white/95 backdrop-blur-sm p-3 rounded-xl border-2 border-white/50 shadow-2xl flex items-center justify-center">
                              <QRCode
                                value={qrData}
                                size={Math.min(180, Math.max(120, windowWidth * 0.2))} // Better responsive sizing
                                style={{
                                  height: "auto",
                                  maxWidth: "100%",
                                  width: "100%",
                                  display: "block"
                                }}
                              />
                            </div>
                          </motion.div>

                          {/* Compact Timer */}
                          <motion.div
                            className={`text-2xl sm:text-3xl font-black transition-all duration-500 ${
                              timeLeft < 60
                                ? "text-red-400 animate-pulse drop-shadow-[0_0_15px_rgba(239,68,68,0.5)]"
                                : timeLeft < 120
                                ? "text-orange-400 drop-shadow-[0_0_15px_rgba(251,146,60,0.5)]"
                                : "text-white drop-shadow-[0_0_15px_rgba(59,130,246,0.5)]"
                            }`}
                            animate={{ scale: timeLeft < 10 ? [1, 1.05, 1] : 1 }}
                            transition={{ duration: 1, repeat: timeLeft < 10 ? Infinity : 0 }}
                          >
                            {formatTimeLeft(timeLeft)}
                          </motion.div>

                          <div className="flex justify-center">
                            <Badge
                              className={`text-xs px-3 py-1 font-bold border-2 ${
                                timeLeft < 60
                                  ? "bg-red-500/20 text-red-300 border-red-400/50"
                                  : timeLeft < 120
                                  ? "bg-orange-500/20 text-orange-300 border-orange-400/50"
                                  : "bg-green-500/20 text-green-300 border-green-400/50"
                              }`}
                            >
                              {timeLeft < 60 ? `⚠️ ${t("admin.tablets.display.expiringSoon")}` : timeLeft < 120 ? `⏰ ${t("admin.tablets.display.expiringSoon")}` : `✅ ${t("admin.tablets.display.active")}`}
                            </Badge>
                          </div>

                          {/* Compact Instructions */}
                          <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm p-3 rounded-xl border border-white/30">
                            <div className="flex items-center justify-center gap-2 mb-2">
                              <Smartphone className="w-4 h-4 text-white" />
                              <span className="text-sm font-bold text-white">{t("admin.tablets.display.scanToMarkAttendance")}</span>
                            </div>
                            <p className="text-white/90 text-center text-xs mb-1">
                              {t("admin.tablets.display.openCameraApp")}
                            </p>
                            <p className="text-white/70 text-center text-xs">
                              {t("admin.tablets.display.clickMaximize")}
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center space-y-4">
                          <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
                            isWithinAttendanceTime ? "bg-blue-500/20" : "bg-gray-500/20"
                          }`}>
                            {isWithinAttendanceTime ? (
                              <Clock className="w-8 h-8 text-blue-300" />
                            ) : (
                              <QrCode className="w-8 h-8 text-gray-400" />
                            )}
                          </div>
                          <div>
                            <h3 className="text-lg font-bold text-white mb-2">
                              {isWithinAttendanceTime ? t("admin.tablets.display.preparingQR") : t("admin.tablets.display.attendanceClosed")}
                            </h3>
                            <p className="text-white/80 text-sm">
                              {isWithinAttendanceTime ? (
                                t("admin.tablets.display.qrWillAppear")
                              ) : attendanceSettings ? (
                                `${t("admin.tablets.display.opensAt")} ${attendanceSettings.recording_start_time.substring(0, 5)}`
                              ) : (
                                t("admin.tablets.display.waitingConfig")
                              )}
                            </p>
                          </div>
                          {isWithinAttendanceTime && (
                            <Button
                              onClick={fetchLatestQRCode}
                              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg px-4 py-2 text-sm font-semibold"
                              size="sm"
                              disabled={isRefreshing}
                            >
                              <RefreshCw className={`w-3 h-3 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                              {t("admin.tablets.display.checkUpdates")}
                            </Button>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </>
                )}
              </Card>
            </motion.div>
          </div>

          {/* Compact Sidebar */}
          <div className="flex flex-col gap-2">
            {/* Live Attendance */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex-1"
            >
              <Card className="border-0 shadow-2xl bg-white/10 backdrop-blur-xl border border-white/20 overflow-hidden min-h-[300px]">
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-white/10 to-transparent"></div>
                <CardHeader className="relative z-10 p-3 border-b border-white/20">
                  <CardTitle className="flex items-center gap-2 text-sm text-white">
                    <div className="p-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    {t("admin.tablets.display.liveAttendance")}
                  </CardTitle>
                </CardHeader>
                <CardContent className="relative z-10 p-3 min-h-[240px] overflow-y-auto overflow-x-hidden">
                  <ScanFeedback
                    recentScans={recentScans}
                    totalScansToday={attendanceCount}
                  />


                </CardContent>
              </Card>
            </motion.div>

            {/* Compact Quick Stats - Reduced Height */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="flex-shrink-0"
            >
              <Card className="border-0 shadow-2xl bg-white/10 backdrop-blur-xl border border-white/20 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-white/10 to-transparent"></div>
                <CardContent className="relative z-10 p-2">
                  {/* Single compact row for scans today */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-1 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
                        <Users className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-white/80 text-xs font-medium">{t("admin.tablets.display.todaysScans")}</span>
                    </div>
                    <span className="text-lg font-black text-white">{attendanceCount}</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
