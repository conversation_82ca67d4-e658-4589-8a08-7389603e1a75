{"app": {"name": "{{APP_NAME}}", "shortName": "{{APP_SHORT_NAME}}", "description": "{{APP_DESCRIPTION}}", "tagline": "Attendance System"}, "common": {"languageCode": "en", "locale": "en", "direction": "ltr", "language": "Language", "english": "English", "turkish": "Türkçe", "save": "Save", "cancel": "Cancel", "loading": "Loading...", "success": "Success", "error": "Error", "saving": "Saving...", "saveChanges": "Save Changes", "processing": "Processing...", "deleting": "Deleting...", "notSet": "Not Set", "ofTotal": "of total", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "signup": "Sign Up", "verifying": "Verifying...", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "name": "Name", "role": "Role", "selectRole": "Select a role", "student": "Student", "teacher": "Teacher", "admin": "Admin", "roles": {"student": "Student", "teacher": "Teacher", "admin": "Admin"}, "school": "School", "selectSchool": "Select School", "invitationCode": "Invitation Code", "enterInvitationCode": "Enter invitation code", "required": "Required", "optional": "Optional", "submit": "Submit", "edit": "Edit", "delete": "Delete", "view": "View", "create": "Create", "update": "Update", "search": "Search", "actions": "Actions", "photo": "Photo", "id": "ID", "filter": "Filter", "all": "All", "room": "Room", "rooms": "Rooms", "time": "Time", "confirmDelete": "Confirm Delete", "none": "None", "yes": "Yes", "no": "No", "confirm": "Confirm", "back": "Back", "backToHome": "Back to Home", "next": "Next", "unknown": "Unknown", "finish": "Finish", "continue": "Continue", "close": "Close", "phone": "Phone", "welcome": "Welcome", "showRooms": "Show Rooms", "hideRooms": "Hide Rooms", "dashboard": "Dashboard", "profileSettings": "Profile Settings", "or": "or go to the", "students": "Students", "block": "Block", "warning": "Warning", "unknownError": "Unknown error occurred", "note": "Note", "status": "Status", "hello": "Hello", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused", "total": "Total", "cleaning": "Cleaning...", "exporting": "Exporting..."}, "maintenance": {"title": "Maintenance Mode", "subtitle": "System Under Maintenance", "message": "We're currently performing some maintenance to improve your experience.", "defaultMessage": "The system is currently undergoing maintenance for improvements.", "estimatedTime": "Estimated completion time", "defaultEstimatedTime": "a few hours", "checkAgain": "Check Again", "contactAdmin": "If you need immediate assistance, please contact your system administrator.", "workingToImprove": "We're working to improve your experience. Please check back later.", "thankYou": "Thank you for your patience.", "refreshPage": "Refresh Page", "systemMaintenance": "System Maintenance", "maintenanceInProgress": "Maintenance in Progress"}, "blockedAccount": {"title": "Account Blocked", "subtitle": "Access Restricted", "message": "Your account has been temporarily blocked.", "personalizedMessage": "Hello {{userName}}, your account has been temporarily blocked.", "reason": "This action was taken for security or policy reasons.", "contactAdmin": "If you believe this is a mistake, please contact your administrator for assistance.", "supportInfo": "For immediate support, please reach out to your system administrator.", "signOut": "Sign Out", "accountSuspended": "Account Suspended", "temporaryRestriction": "Temporary Access Restriction"}, "offline": {"title": "You're Offline", "subtitle": "No Internet Connection", "description": "We can't connect to the server right now", "connectionRestored": "Connection Restored", "connectionRestoredDescription": "Your internet connection has been restored. The app is now updating.", "offlineDescription": "Please check your internet connection and try again. Some features may be unavailable while offline.", "troubleshootingTips": "Troubleshooting tips:", "checkConnection": "Check your internet connection", "tryDifferentNetwork": "Try connecting to a different network", "restartRouter": "Restart your router if possible", "checkOtherApps": "Check if other apps can connect to the internet", "offlineMode": "Offline Mode", "tryAgain": "Try again", "refresh": "Refresh", "retry": "Retry", "goBack": "Go back", "checkingConnection": "Checking connection...", "retryCount": "Retry count", "connectionStatus": "Connection status", "online": "Online", "offline": "Offline", "persistentIssues": "If you continue to experience issues, some features may still be available in offline mode. You can also try again later when your connection improves.", "noInternetAccess": "No Internet Access", "limitedFunctionality": "Limited functionality available while offline"}, "biometrics": {"title": "Biometric Authentication", "description": "Set up biometric authentication for quick and secure access to your account.", "authentication": "Biometric Authentication", "registered": "Use fingerprint or face recognition to authenticate", "notRegistered": "Not registered - click to set up biometric authentication", "active": "Active", "register": "Register", "remove": "Remove", "loading": "Loading biometric settings...", "notAvailable": "Biometric Authentication Not Available", "notAvailableDesc": "Your device or browser doesn't support biometric authentication, or you need to use HTTPS.", "requirements": "Requirements:", "modernBrowser": "Modern browser (Chrome, Firefox, Safari, Edge)", "httpsConnection": "HTTPS connection (or localhost for development)", "biometricDevice": "Device with fingerprint sensor or face recognition", "webauthnSupport": "WebAuthn support enabled in browser", "securityPrivacy": "Security & Privacy", "dataStaysLocal": "Your biometric data stays on your device", "encryptedStorage": "Only encrypted templates are stored securely", "removeAnytime": "You can remove biometric access anytime", "pinBackup": "PIN backup is always available", "benefits": "Benefits", "quickAttendance": "Lightning-fast attendance marking", "enhancedSecurity": "Enhanced security with hardware-backed authentication", "noPasswords": "No passwords to remember", "multipleDevices": "Works with fingerprint sensors and face recognition", "registerTitle": "Register Biometric Authentication", "registerDescription": "Set up fingerprint or face recognition for quick and secure access", "confirmDelete": "Remove Biometric Authentication", "confirmDeleteDesc": "Are you sure you want to remove your biometric authentication? You will need to use your PIN for attendance verification.", "registrationSuccess": "Biometric registration completed successfully!", "registrationFailed": "Biometric Registration Failed", "removalSuccess": "Biometric authentication removed successfully", "removalError": "Failed to remove biometric registration", "loadError": "Failed to load biometric settings", "registerBiometric": "Register Biometric", "registerYourBiometric": "Register Your Biometric", "authenticateWithBiometric": "Authenticate with Biometric", "setupFingerprintOrFace": "Set up fingerprint or face recognition for quick access", "useRegisteredBiometric": "Use your registered biometric to authenticate", "instructions": "Instructions:", "clickButtonToStart": "Click the button above to start", "followBrowserPrompt": "Follow your browser's biometric prompt", "useFingerprintOrFace": "Use fingerprint sensor or face recognition", "completeWhenPrompted": "Complete the process when prompted", "dataStaysSecure": "Your biometric data stays secure on your device", "touchSensorMessage": "Touch your fingerprint sensor or use face recognition...", "registrationSuccessMessage": "Biometric registration completed successfully!", "authenticationSuccessMessage": "Biometric authentication successful!", "biometricAuthentication": "Biometric Authentication", "loadingSettings": "Loading biometric settings", "deviceNotSupported": "Your device or browser doesn't support biometric authentication, or you need to use HTTPS", "deviceWithSensor": "Device with fingerprint sensor or face recognition", "setupBiometric": "Set up biometric authentication for quick and secure access to your account", "useFingerprintOrFaceAuth": "Use fingerprint or face recognition to authenticate", "notRegisteredSetup": "Not registered - click to set up biometric authentication", "securityAndPrivacy": "Security & Privacy", "biometricDataNeverLeaves": "Your biometric data never leaves your device", "onlyEncryptedTemplates": "Only encrypted templates are stored securely", "pinBackupAvailable": "PIN backup is always available", "benefitsOfBiometric": "Benefits of Biometric Authentication", "quickAttendanceMarking": "Quick attendance marking with just a touch", "noNeedToRemember": "No need to remember PINs or passwords", "worksWithSensors": "Works with fingerprint sensors and face recognition", "registerBiometricAuth": "Register Biometric Authentication", "followPromptsToSetup": "Follow the prompts to set up fingerprint or face recognition", "cancel": "Cancel", "preparingAuthentication": "Preparing biometric authentication...", "biometricSignInSuccessful": "✨ Biometric Sign-In Successful", "welcomeBackSecure": "Welcome back! You've been signed in securely.", "authenticationFailed": "Authentication failed", "biometricSignInFailed": "Biometric Sign-In Failed", "tryAgainOrUseEmail": "Please try again or use email/password.", "notSupported": "Biometric authentication not supported", "useEmailPassword": "Please use email and password to sign in", "biometricSignIn": "Biometric Sign-In", "signInSecurely": "Sign in securely with your fingerprint or face recognition", "authenticationAvailable": "Biometric authentication available", "ready": "Ready", "checkingAvailability": "Checking biometric availability...", "noBiometricSetup": "No biometric authentication set up", "signInWithBiometrics": "Sign In with Biometrics", "fast": "Fast", "secure": "Secure", "private": "Private", "mustRegisterFirst": "You must first register biometrics in your profile settings to use this feature.", "defaultDeletionReason": "Student requesting biometric credential deletion for security purposes", "deletionRequestSubmitted": "Deletion Request Submitted", "deletionRequestSubmittedMessage": "Your biometric deletion request has been submitted to your school admin for approval.", "deletionRequestSubmissionError": "Failed to submit deletion request. Please try again.", "requestDeletion": "Request Deletion", "deletionPendingTitle": "Deletion Request Pending", "deletionPendingMessage": "Your biometric deletion request is waiting for approval from your school admin.", "deletionApprovedTitle": "Deletion Request Approved", "deletionApprovedMessage": "Your school admin has approved your biometric deletion request. Your credentials have been removed.", "deletionPending": "Deletion Pending", "registrationError": "An error occurred during biometric registration", "registering": "Registering...", "requestSubmittedOn": "Submitted on", "requestApprovedOn": "Approved on", "biometricOnlyTitle": "Biometric-Only Authentication", "biometricOnlyDescription": "For security, only fingerprint, face recognition, or other biometric methods are allowed. PIN, pattern, and password authentication are disabled to prevent unauthorized access.", "biometricOnlyRequired": "Biometric-Only Required", "biometricOnlyNotSupported": "This device does not support biometric-only authentication. Please use a device with fingerprint, face recognition, or other biometric sensors.", "biometricOnlyInstructions": "Use only fingerprint, face recognition, or other biometric methods. PIN/pattern/password are not allowed.", "biometricOnlyFailed": "Biometric authentication failed. Please ensure you use only biometric methods (fingerprint, face recognition, etc.).", "chooseHardwareBound": "Choose hardware-bound options (like Samsung Pass) instead of cloud-based storage that allows PIN/pattern fallbacks.", "avoidCloudStorage": "Avoid Google Account or cloud storage options that allow PIN/pattern as fallbacks.", "hardwareBoundOnly": "Use only hardware-bound biometric authentication for maximum security.", "strictBiometricOnlyRegister": "IMPORTANT: Use ONLY fingerprint, face recognition, or other biometric methods. If you see options for Gmail, Samsung Pass with PIN/pattern, or password - DO NOT use them. Close and try again with pure biometric authentication only.", "strictBiometricOnlyAuth": "IMPORTANT: Use ONLY fingerprint, face recognition, or other biometric methods. If you see options for PIN/pattern/password - DO NOT use them. Use only biometric authentication."}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInHere": "Sign in here", "signUpHere": "Sign up here", "contactAdminForCode": "Contact your school administrator for the invitation code", "enterCodeExactly": "Enter the code exactly as provided, including any dashes or special characters", "invalidInvitationCode": "The invitation code you entered is incorrect. Please check the code and try again.", "expiredInvitationCode": "This invitation code has expired. Please contact your school administrator for a new code.", "noInvitationCode": "This school doesn't have an active invitation code. Please contact the school administrator.", "invalidSchool": "The selected school is invalid or no longer available. Please select a different school.", "emailAlreadyRegistered": "This email address is already registered. Please use a different email or try logging in.", "passwordRequirements": "Password must be at least 8 characters long", "passwordsDoNotMatch": "Passwords do not match", "invalidEmail": "Invalid email address", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "verifyEmail": "Please verify your email address", "resetPasswordEmailSent": "Password reset email sent", "passwordResetSuccess": "Password reset successfully", "loginSuccess": "Logged in successfully", "logoutSuccess": "Logged out successfully", "logoutError": "Failed to log out. Please try again.", "sessionExpired": "Your session has expired. Please log in again.", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "networkError": "Network error. Please check your internet connection and try again.", "refreshPageConfirm": "Would you like to refresh the page and try again?", "emailAndPassword": "Email & Password", "biometric": "Biometric", "welcomeBack": "Welcome back", "enterCredentials": "Enter your credentials to access your account", "emailPlaceholder": "<EMAIL>", "yourEmail": "Your email", "loggingIn": "Logging in...", "login": "<PERSON><PERSON>", "signedInSuccessfully": "You've been signed in successfully.", "noAccount": "Don't have an account?", "systemAdminSetup": "System Administrator Setup", "createSystemAdminDescription": "Create a system administrator account with full system access", "noInvitationCodeRequired": "No invitation code required", "allFieldsRequired": "All fields are required", "passwordTooShort": "Password must be at least 8 characters long", "fullNamePlaceholder": "<PERSON>", "adminEmailPlaceholder": "<EMAIL>", "systemAdminCode": "System Admin Code", "systemAdminCodePlaceholder": "Enter the system admin code", "systemAdminCodeDescription": "This code is required to create a system administrator account", "creatingAccount": "Creating Account...", "createSystemAdminAccount": "Create System Admin Account", "alreadyHaveAccountLogin": "Already have an account? <PERSON>gin", "authenticating": "Authenticating...", "placeholders": {"yourName": "Your name", "yourEmail": "Your email", "password": "Password", "confirmPassword": "Confirm password", "adminEmail": "Administrator email", "systemAdminCode": "System admin code", "invitationCode": "Invitation code"}, "validationError": "Validation Error", "pleaseSelectSchool": "Please select a school from the list", "invitationCodeRequired": "Invitation code is required for registration", "checkInvitationCodeMessage": "Please check your invitation code and try again", "pleaseSelectValidSchool": "Please select a valid school from the list", "trySigningInInstead": "This email is already registered. Try signing in instead", "pleaseEnterValidEmail": "Please enter a valid email address", "biometricFailed": "Biometric Authentication Failed", "signupFailed": "Sign Up Failed"}, "settings": {"languageSettings": "Language Settings", "languageDescription": "Choose your preferred language for the interface and all content.", "currentLanguage": "Current Language", "active": "Active", "selectLanguage": "Select Language", "languageInfo": "Language Information", "interfaceLanguage": "Changes interface language immediately", "exportLanguage": "Affects PDF and CSV export content", "notificationLanguage": "Changes notification language", "instantChange": "No restart required - changes apply instantly", "testLanguage": "Test Language", "supportedLanguages": "Supported Languages"}, "attendance": {"attendance": "Attendance", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused", "attendanceRecordingSettings": "Attendance Recording Settings", "controlAttendanceRecording": "Control when students can record their attendance", "attendanceTimeRestrictionInfo": "Setting time restrictions for attendance recording helps ensure that students only record attendance during appropriate class hours and prevents unauthorized attendance recording outside of class times.", "attendanceTimeRestrictionsDescription": "Students will only be able to record attendance during the time window you specify below. Outside of these hours, the attendance recording will be disabled.", "attendanceRecordingSettingsUpdated": "Attendance recording time settings have been updated successfully.", "recordingTimeRestrictions": "Recording Time Restrictions", "recordingStartTime": "Recording Start Time", "recordingEndTime": "Recording End Time", "saving": "Saving...", "saveSettings": "Save Settings", "settingsSaved": "Settings Saved", "status": {"unknown": "UNKNOWN", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused", "allStatuses": "All Statuses", "presentOnly": "Present Only", "absentOnly": "Absent Only", "lateOnly": "Late Only", "excusedOnly": "Excused Only"}, "actions": {"markAsPresent": "<PERSON> as Present", "markAsAbsent": "<PERSON> as Absent"}, "date": "Date", "time": "Time", "block": "Block", "room": "Room", "scan": "<PERSON><PERSON>", "record": "Record", "verify": "Verify", "verifyAttendance": "Verify Attendance", "submit": "Submit", "history": "History", "report": "Report", "export": {"title": "Export Attendance Records", "description": "Export attendance records for the selected date range", "dateRange": "Date Range", "filterByStatus": "Filter by Status", "recordsFound": "{{count}} records found", "withStatus": "with status", "noRecordsForPeriod": "No records found for the selected date range. Try selecting a different period.", "noStatusRecordsForPeriod": "No {{status}} records found for the selected date range. Try selecting a different status or date range.", "noRecordsAvailable": "No attendance records available. Records will appear here once attendance is taken.", "exportRecords": "Export Records", "selectDateRangeAndFormat": "Select date range and export format", "start": "Start", "end": "End", "filter": "Filter", "dateRangeRequired": "Date Range Required", "selectDateRange": "Please select a start and end date", "selectDate": "Select Date", "noRecordsFound": "No Records Found", "noRecordsDescription": "No attendance records found for the selected date", "noRecordsForDateRange": "No records found for the selected date range", "noStatusRecordsForDateRange": "No {{status}} records found for the selected date range", "chooseFormat": "Choose Export Format", "downloadCSV": "Download as CSV", "downloadPDF": "Download as PDF", "downloadHTML": "Download as HTML", "selectForExport": "Select for Export", "exportSuccessful": "Export Successful", "reportExportedAs": "Report exported as {{format}}", "exportFailed": "Export Failed", "failedToGenerate": "Failed to generate the report", "attendanceReport": "ATTENDANCE REPORT", "to": "to", "generatedOn": "Generated on", "totalRecords": "Total Records", "attendanceSummary": "ATTENDANCE SUMMARY", "reportInformation": "REPORT INFORMATION", "allDates": "All dates", "reportDescription": "This report provides a summary of student attendance records for the selected date range.", "systemName": "Campus Guardian - Attendance Management System", "notAvailable": "N/A", "unknownStudent": "Unknown Student", "unknownRoom": "Unknown Room", "by": "by", "page": "Page", "of": "of", "generated": "Generated", "headers": {"dateTime": "Date & Time", "studentName": "Student Name", "studentId": "Student ID", "room": "Room", "status": "Status", "verification": "Verification"}, "export": "Export", "exportAsHTML": "Export as HTML", "exportAsPDF": "Export as PDF", "exportAsCSV": "Export as CSV", "exportComplete": "Export Complete", "exportSuccess": "Successfully exported {{count}} records to {{format}} format", "comprehensiveReport": "Comprehensive Attendance Management Report", "reportContains": "This report contains {{count}} attendance records as of {{date}}", "manualByTeacher": "Manual by Teacher"}, "filter": "Filter", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "apply": "Apply", "reset": "Reset", "noData": "No attendance data found", "recordSuccess": "Attendance recorded successfully", "recordError": "Failed to record attendance", "verificationRequired": "Verification required", "locationVerification": "Location Verification", "biometricVerification": "Biometric Verification", "pinVerification": "PIN Verification", "manualVerification": "Manual Verification", "excuses": "Excuses", "attendanceHistory": "Attendance History", "yourRecordsForAllClasses": "Your attendance records for all classes", "noRecordsAvailable": "No Records Available", "historyWillAppear": "Your attendance history will appear here once you've checked in to a class", "unknownRoom": "Unknown Room", "unknownCourse": "Unknown Course", "unknownBuilding": "Unknown Building", "allTime": "All Time", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "filterBy": "Filter by", "noAttendanceData": "No attendance data", "yourAttendanceHistoryWillAppearHere": "Your attendance history will appear here", "errorLoadingData": "Error loading data", "locationVerified": "Location Verified", "deviceWeb": "Device: Web"}, "feedback": {"feedback": "<PERSON><PERSON><PERSON>", "sendFeedback": "Send Feedback", "feedbackDescription": "Share your thoughts, suggestions, or report issues.", "yourName": "Your name", "yourEmail": "Your email address", "yourPhone": "Your phone number", "message": "Message", "yourFeedback": "Your feedback, suggestions, or issues...", "emailDescription": "We'll use this to follow up if needed.", "submitting": "Submitting...", "submitFeedback": "Submit <PERSON>", "thankYou": "Thank You!", "feedbackSubmitted": "Your feedback has been submitted successfully.", "feedbackSubmissionError": "Failed to submit feedback. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again."}, "dashboard": {"studentDashboard": "Student Dashboard"}, "students": {"directory": {"title": "Student Directory", "description": "View and manage all students in the system", "searchPlaceholder": "Search by name, ID, or email...", "switchToGridView": "Switch to Grid View", "switchToListView": "Switch to List View", "selectBlock": "Select Block", "allBlocks": "All Blocks", "editBlock": "Edit Block", "deleteBlock": "Delete Block", "addNewBlock": "Add New Block", "selectRoom": "Select Room", "allRooms": "All Rooms", "editRoom": "Edit Room", "deleteRoom": "Delete Room", "addNewRoom": "Add New Room", "profile": "Profile", "name": "Name", "studentId": "Student ID", "course": "Course", "block": "Block", "room": "Room", "status": "Status", "actions": "Actions", "notSet": "Not set", "notAssigned": "Not Assigned", "view": "View", "viewDetails": "View Details", "noMatchingStudents": "No students match your search criteria", "trySearchingBy": "Try searching by name, ID, or email", "noStudentsAssignedTo": "No students assigned to this {{type}}", "noStudentsInSystem": "No students found in the system", "fetchBlocksRoomsError": "Failed to fetch blocks and rooms", "fetchRoomError": "Failed to fetch room information", "loadError": "Could not load student directory", "loadErrorTryAgain": "Could not load student directory. Please try again.", "editBlockTitle": "Edit Block", "editBlockDescription": "Update the block name. This will affect all students assigned to this block.", "blockName": "Block Name", "editRoomTitle": "Edit Room", "editRoomDescription": "Update the room name. This will affect all students assigned to this room.", "roomName": "Room Name", "addNewBlockTitle": "Add New Block", "addNewBlockDescription": "Create a new block to organize rooms and students.", "enterBlockName": "Enter block name...", "addBlock": "Add Block", "addNewRoomTitle": "Add New Room", "addNewRoomDescription": "Create a new room in the selected block.", "enterRoomName": "Enter room name...", "addRoom": "Add Room", "failedToLoadInitialData": "Failed to load initial data. Please refresh the page.", "dataLoaded": "Data Loaded", "loadedStudentsFromCache": "Loaded {{count}} students from cache", "cannotDeleteBlock": "Cannot Delete Block", "blockHasStudents": "This block has students assigned to it. Please reassign them first.", "blockHasRooms": "This block has rooms assigned to it. Please delete or reassign the rooms first.", "blockDeleted": "Block Deleted", "blockDeletedSuccess": "The block has been deleted successfully.", "failedToDeleteBlock": "Failed to delete block. Make sure there are no rooms or students assigned to it.", "cannotDeleteRoom": "Cannot Delete Room", "roomHasStudents": "This room has students assigned to it. Please reassign them first.", "roomHasAttendanceRecords": "This room has attendance records. Please archive or reassign them first.", "roomDeleted": "Room Deleted", "roomDeletedSuccess": "The room has been deleted successfully.", "failedToDeleteRoom": "Failed to delete room. Make sure there are no attendance records or students assigned to it.", "blockUpdated": "Block Updated", "blockUpdatedSuccess": "The block has been updated successfully.", "failedToUpdateBlock": "Failed to update block. Please try again.", "roomUpdated": "Room Updated", "roomUpdatedSuccess": "The room has been updated successfully.", "failedToUpdateRoom": "Failed to update room. Please try again.", "blockNameCannotBeEmpty": "Block name cannot be empty", "blockAddedSuccess": "Block added successfully", "failedToAddBlock": "Failed to add block. Please try again.", "roomNameCannotBeEmpty": "Room name cannot be empty", "selectBlockToAddRoom": "Please select a block to add the room to", "roomAddedSuccess": "Room added successfully", "failedToAddRoom": "Failed to add room. Please try again."}, "profile": {"title": "Student Profile", "detailedInfo": "Detailed information about {{name}}", "studentId": "Student ID", "email": "Email", "course": "Course", "block": "Block", "room": "Room", "biometric": "Biometric", "registered": "Registered", "notRegistered": "Not Registered", "todaysAttendance": "Today's Attendance"}}, "profile": {"completeYourProfile": "Complete Your Profile", "completeProfileMessage": "Please complete your student profile before continuing. Fill in all required information below.", "registerBiometrics": "Register your biometrics", "forFasterVerification": "for faster attendance verification", "updateProfile": "Update Profile", "studentProfile": "Student Profile", "updateYourInformation": "Update your profile information", "viewAndManage": "View and manage your student profile", "fillRequiredFields": "Please fill in all required fields to complete your profile setup.", "tabs": {"profile": "Profile", "biometrics": "Biometrics", "language": "Language"}, "uploading": "Uploading...", "changePhoto": "Change Photo", "fullName": "Full Name", "enterFullName": "Enter your full name", "studentId": "Student ID", "enterStudentId": "Enter your student ID", "course": "Class/Grade", "enterCourse": "Enter your class or grade", "block": "Block", "selectBlock": "Select a block", "room": "Room", "selectRoom": "Select a room", "pin": "PIN", "enterPin": "Enter a 6-digit PIN", "pinDescription": "This PIN will be used as a backup verification method.", "school": "School", "selectSchool": "Select your school", "schoolDescription": "Select the school you are attending.", "save": "Save Changes", "edit": "Edit Profile", "registerBiometricAuth": "Register Biometric Authentication", "biometricRegistered": "Biometric authentication registered", "setupComplete": "Profile setup complete", "setupCompleteMessage": "Your profile has been successfully set up. You can now access all features.", "email": "Email", "biometricStatus": "Biometric Status", "registered": "Registered", "notRegistered": "Not registered", "registering": "Registering...", "saving": "Saving...", "completeSetup": "Complete Setup", "loadingSchools": "Loading schools...", "noSchoolsAvailable": "No schools available", "fileTooLarge": "File too large", "selectSmallerImage": "Please select an image under 5MB", "invalidFileType": "Invalid file type", "selectImageFile": "Please select an image file", "noSchoolsFound": "No schools found", "noSchoolsFoundDesc": "No schools have been added by administrators yet. Please contact your administrator to add your school.", "failedToFetchSchools": "Failed to fetch schools", "failedToFetchBlocks": "Failed to fetch blocks", "failedToFetchRooms": "Failed to fetch rooms", "photoUpdated": "Photo updated", "photoUpdatedDesc": "Your profile photo has been updated", "uploadFailed": "Upload failed", "failedToUploadPhoto": "Failed to upload profile photo", "profileUpdated": "Profile Updated", "profileUpdatedDesc": "Your profile has been updated successfully.", "setupCompleteDesc": "Your profile has been successfully set up. You can now access all features.", "failedToUpdateProfile": "Failed to update profile. Please try again.", "editProfile": "Edit Profile", "profileInformation": "Profile Information"}, "notifications": {"alerts": "<PERSON><PERSON><PERSON>", "title": "Notifications", "description": "Stay updated with your attendance status", "new": "New", "clearAll": "Clear All", "dismiss": "<PERSON><PERSON><PERSON>", "dismissing": "Dismissing...", "noNotificationsYet": "No Notifications Yet", "emptyStateMessage": "When your attendance status changes, you'll see notifications here", "attendanceStatusChanged": "Attendance Status Changed", "attendanceStatusChangedDescription": "You changed the attendance status of {{studentName}} to {{status}}", "error": "Error", "failedToLoad": "Failed to load notifications", "failedToDelete": "Failed to delete notification", "failedToClear": "Failed to clear notifications", "dismissed": "Notification Dismissed", "deleted": "The notification has been deleted", "allClear": "All Clear", "allDeleted": "All notifications have been deleted", "markedPresent": "✅ Marked Present", "markedAbsent": "❌ Marked Absent", "markedLate": "⏰ Marked Late", "markedExcused": "📝 Marked Excused", "presentMessage": "You were marked present in Room {{room}} by {{teacher}}", "absentMessage": "You were marked absent in Room {{room}} by {{teacher}}. If this is incorrect, please contact your teacher.", "lateMessage": "You were marked late for Room {{room}} by {{teacher}}", "excusedMessage": "Your absence in Room {{room}} has been excused by {{teacher}}", "showUnread": "Show Unread Only", "soundOn": "Sound On", "soundOff": "Sound Off", "audioEnabled": "Audio Notifications Enabled", "audioEnabledDescription": "You will now hear sounds for new notifications", "audioDisabled": "Audio Notifications Disabled", "audioDisabledDescription": "Notification sounds have been turned off", "pushNotSupported": "Push Notifications Not Supported", "pushNotSupportedDescription": "Your browser doesn't support push notifications", "pushPermissionDenied": "Permission Denied", "pushPermissionDeniedDescription": "Please allow notifications in your browser settings", "timeAgo": {"seconds": "{{count}} seconds ago", "minutes": "{{count}} minutes ago", "hours": "{{count}} hours ago", "days": "{{count}} days ago", "months": "{{count}} months ago", "years": "{{count}} years ago"}}, "carousel": {"previousSlide": "Previous slide", "nextSlide": "Next slide", "swipe": "Swipe"}, "footer": {"legal": "Legal", "home": "Home", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>", "contactUs": "Contact Us", "visitOurWebsite": "Visit Our Website", "connectWithUs": "Connect With Us", "noSocialMediaLinks": "No social media links configured.", "allRightsReserved": "All rights reserved.", "developedWith": "Developed with", "by": "by"}, "excuses": {"attendanceExcuses": "Attendance Excuses", "requestExcusesDescription": "Request excuses for your absences and view their status.", "newRequest": "New Request", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "submissionNotAvailable": "Submission Not Available", "submissionTimeRestriction": "Excuse requests can only be submitted between {{startTime}} and {{endTime}}. The current time is {{currentTime}}.", "pendingExcuseExists": "Pending Excuse Exists", "pendingExcuseMessage": "You already have a pending excuse request. You must delete it before submitting a new one. Go to the \"Pending\" tab to manage your existing request.", "room": "Room", "selectRoom": "Select a room", "selectRoomDescription": "Select the room you need an excuse for", "startDate": "Start Date", "endDate": "End Date", "startTime": "Start Time", "endTime": "End Time", "reason": "Reason", "submit": "Submit", "delete": "Delete", "cancel": "Cancel", "confirmDelete": "Confirm Delete", "confirmDeleteDescription": "Are you sure you want to delete this excuse request? This action cannot be undone.", "dateTooFarInAdvance": "Date Too Far in Advance", "maxDaysInAdvance": "Excuse requests can only be submitted up to {{days}} days in advance.", "durationTooLong": "Excuse Duration Too Long", "maxExcuseDuration": "Excuse requests cannot exceed {{days}} days in duration.", "failedToSubmit": "Failed to submit excuse request.", "noPendingExcuses": "No pending excuses", "noPendingExcusesDescription": "You don't have any pending excuse requests", "noApprovedExcuses": "No approved excuses", "noApprovedExcusesDescription": "You don't have any approved excuse requests", "noRejectedExcuses": "No rejected excuses", "noRejectedExcusesDescription": "You don't have any rejected excuse requests", "duration": "Duration", "teacherNotes": "Teacher Notes", "unknown": "Unknown", "deletePendingFirst": "Delete Pending Excuse First", "submitRequest": "Submit Excuse Request", "submitting": "Submitting...", "submitted": "Request Submitted", "submittedDescription": "Your excuse request has been submitted successfully and is now pending review", "submitError": "Failed to submit excuse request", "deleted": "Request Deleted", "deletedDescription": "Your excuse request has been deleted successfully", "deleteError": "Failed to delete excuse request", "pickADate": "Pick a date", "firstDayOfAbsence": "First day of absence", "lastDayOfAbsence": "Last day of absence", "startTimePlaceholder": "Start time", "endTimePlaceholder": "End time", "reasonPlaceholder": "Please explain the reason for your absence...", "reasonDescription": "Provide a detailed explanation for your absence", "roomLabel": "Room", "pleaseSelectRoom": "Please select a room", "pleaseSelectStartDate": "Please select a start date", "pleaseSelectEndDate": "Please select an end date", "pleaseEnterStartTime": "Please enter a start time", "pleaseEnterEndTime": "Please enter an end time", "pleaseProvideReason": "Please provide a reason", "reasonMinLength": "Reason must be at least 10 characters", "reasonMaxLength": "Reason must not exceed 500 characters", "endDateBeforeStartDate": "End date cannot be before start date", "errorFetchingRooms": "Error", "failedToLoadRooms": "Failed to load rooms. Please try again."}, "qrScanner": {"noRoomAvailable": "No Room Available", "noRoomsSetup": "No rooms have been set up for your school yet. You can still record attendance.", "attendanceRecordingNotAvailable": "Attendance Recording Not Available", "recordingTimeRestriction": "Attendance can only be recorded between {{startTime}} and {{endTime}}. The current time is {{currentTime}}.", "biometricVerification": "Biometric Verification", "verifyIdentity": "Please verify your identity using your fingerprint or face", "userProfileNotFound": "User profile not found", "enterPin": "Please enter your PIN", "incorrectPin": "Incorrect PIN. Please try again.", "biometricNotSupported": "Biometric authentication is not supported on this device", "pleaseWait": "Please Wait", "loadingRoomData": "Loading your room information...", "scanQrCode": "QR Code Attendance", "scanQrDescription": "Scan the QR code displayed in your room", "scanQrDescriptionFull": "Scan the QR code displayed in your room to mark your attendance", "startScan": "Start Scanning", "checkingCameraPermissions": "Checking camera permissions...", "readyToScanFor": "Ready to scan for:", "scanningQrCode": "Scanning QR code...", "verifyingLocation": "Verifying your location...", "processingAttendance": "Processing attendance...", "allowLocationAccess": "Please allow location access if prompted", "verifyToRecord": "Please verify your identity to record attendance", "enterYourPin": "Enter your PIN", "verifyWithPin": "Verify with PIN", "useBiometricVerification": "Use Biometric Verification", "setupPinRequired": "You need to set up a PIN before you can record attendance. Please update your profile to add a PIN.", "noRoomInfo": "No room information found. Please make sure your profile is complete with block and room number.", "biometricVerificationFailed": "Biometric verification failed. Please try again or use PIN.", "geolocationNotSupported": "Geolocation is not supported by your browser", "useBiometric": "Use Biometric", "attendanceRecorded": "Attendance Recorded", "attendanceSuccess": "Your attendance has been successfully recorded", "alreadyPresent": "Already Present", "alreadyRecorded": "Your attendance has already been recorded for this class", "viewHistory": "View History", "locationVerification": "Location Verification", "locationVerificationDisabled": "Location verification is disabled for this room.", "blockVerificationDisabled": "Block-level verification is disabled for this room.", "verifyingRoomLevel": "Verifying you are in the correct room...", "verifyingBlockLevel": "Verifying you are in the correct building...", "locationAccessFailed": "Failed to access your location. Please check your device settings.", "locationVerificationFailed": "Location Verification Failed", "mustBeInRoom": "You must be in or near your room to record attendance.", "userNotAuthenticated": "User not authenticated. Please log in again.", "failedToCheckAttendance": "Failed to check attendance status. Please try again.", "attendanceRecordedSuccessfully": "Your attendance has been successfully recorded.", "failedToRecordAttendance": "Failed to record attendance. Please try again.", "alreadyRecordedToday": "Your attendance for {{room}} was already recorded today at {{time}}.", "alreadyMarkedPresent": "You've already been marked present for today's class at {{time}}", "attendanceSuccessfullyRecorded": "Your attendance has been successfully recorded", "earlierToday": "earlier today", "scanAnotherCode": "Scan Another Code", "cameraPermissionRequired": "Camera Permission Required", "cameraPermissionDescription": "To scan QR codes, we need access to your camera. Please click the button below to grant permission.", "allowCameraAccess": "Allow Camera Access", "cameraAccessGranted": "Camera access granted. Ready to scan QR codes!", "pointCameraAtQr": "Point camera at QR code", "recordingAttendance": "Recording your attendance...", "requestingCameraPermission": "Requesting Permission...", "startingCamera": "Starting camera...", "requestingPermission": "Requesting permission...", "chooseVerificationMethod": "Choose verification method:", "loadingVerificationSettings": "Loading verification settings...", "locationPermissionRequired": "Location Access Required", "locationPermissionRequiredDescription": "Location verification is enabled for this room. You must allow location access to record attendance.", "enableLocation": "Enable Location Access", "biometricAuthenticationRequired": "Biometric Authentication Required", "biometricOnlyRequiredDescription": "This school requires biometric authentication only. You cannot record attendance without completing biometric verification.", "biometricRequiredForBothDescription": "This school requires both biometric and PIN verification. You must complete biometric authentication first.", "retryBiometric": "Retry Biometric Authentication", "authenticating": "Authenticating...", "biometric": "Biometric", "pin": "PIN", "back": "Back", "verify": "Verify", "tryAgain": "Try Again", "qr": {"pinNotSet": "PIN not set", "pinNotSetDescription": "Please set your PIN in your profile settings", "invalidPin": "Invalid PIN", "invalidPinDescription": "The PIN you entered is incorrect", "alreadyRecorded": "Already Recorded", "alreadyRecordedDescription": "You've already recorded attendance for {{roomName}} today at {{time}}", "processing": "Processing", "processingDescription": "Recording your attendance...", "pinProcessingError": "An error occurred while processing your PIN. Please try again.", "locationWarning": "Location Warning", "locationWarningDescription": "You are {{distance}} away from your room, which is {{excessDistance}}m outside the {{allowedRadius}}m allowed radius. Your teacher will be notified.", "recordAttendanceError": "Failed to record attendance. Please try again.", "attendanceRecorded": "Your attendance has been recorded successfully", "checkingLocation": "Checking Location", "checkingLocationDescription": "Getting your location... This may take a few seconds", "locationFound": "Location Found", "concurrentSessionWarning": "We detected another active session. Your teacher will be notified of this unusual activity.", "deviceChangeWarning": "You're using a different device than earlier today. Your teacher will be notified.", "attendanceError": "Attendance Error"}, "roomValidation": {"perfectMatch": "Welcome! You're scanning from your assigned room ({{roomName}}).", "blockMatch": "You're in {{blockName}} but not your assigned room. Attendance will still be recorded.", "wrongRoom": "This QR code is for {{blockName}} - {{roomName}}. Please scan the QR code in your assigned room."}, "errors": {"cameraPermissionRequired": "Camera permission is required to scan QR codes. Please allow camera access and try again.", "videoElementNotFound": "Video element not found. Please try again.", "cameraPermissionDenied": "Camera permission denied. Please allow camera access in your browser settings and try again.", "noCameraFound": "No camera found. Please ensure your device has a camera.", "cameraAccessDenied": "Camera access was denied. Please allow camera permissions and try again.", "noCameraDevice": "No camera device found on this device.", "cameraInUse": "Camera is already in use by another application.", "emptyQrCode": "Empty QR code data", "urlQrCode": "This appears to be a URL QR code, not an attendance QR code.", "wifiQrCode": "This appears to be a WiFi QR code, not an attendance QR code.", "qrCodeTooShort": "QR code data too short. Please scan a valid attendance QR code.", "qrCodeTooLong": "QR code data too long. Please scan a valid attendance QR code.", "invalidQrFormat": "Invalid QR code format. Please scan a valid attendance QR code generated by the system.", "invalidQrStructure": "Invalid QR code structure", "missingQrFields": "Invalid QR code: missing {{fields}}", "qrCodeExpired": "This QR code has expired. Please scan a fresh code.", "qrCodeTooOld": "This QR code is too old and is no longer valid. Please scan a fresh code.", "possibleScreenshotAttack": "This QR code appears to be from a screenshot or photo. Please scan the QR code directly from the display screen.", "qrCodeFutureTimestamp": "This QR code has an invalid timestamp. Please scan a fresh code.", "userAuthRequired": "User authentication required for QR validation", "qrCodeReplaced": "This QR code is no longer active. A new QR code may have been generated. Please ensure you're scanning the most recent QR code displayed on the tablet.", "biometricRequired": "Biometric authentication required", "pinRequired": "PIN verification required", "bothMethodsRequired": "Both biometric and PIN verification required", "bothMethodsDescription": "You must complete both biometric authentication and PIN verification to record attendance.", "startWithBiometric": "Start with biometric authentication", "biometricCompleted": "Biometric authentication completed", "nowEnterPin": "Now enter your PIN to complete verification", "completeVerification": "Complete Verification", "differentSchool": "This QR code is for a different school.", "roomNotFound": "Room not found", "roomValidationFailed": "Failed to validate room assignment", "rateLimited": "You have made too many attendance attempts. Please try again in a few minutes.", "locationTooFar": "You are {{distance}} away from your room, which is {{excessDistance}}m outside the {{allowedRadius}}m allowed radius. Please move closer to the room and try again.", "locationTimeout": "The request timed out. Please check your internet connection.", "locationUnavailable": "Location information is unavailable. Please ensure GPS is enabled.", "locationPermissionDenied": "Location permission was denied. Please enable location access.", "locationGenericError": "Please try again."}, "toasts": {"locationAccessRequired": "Location Access Required", "locationAccessDescription": "Please enable location access in your browser settings to record attendance.", "scanError": "<PERSON><PERSON>", "biometricNotSupported": "Biometric authentication not supported on this device or requires HTTPS", "noBiometricCredentials": "No biometric credentials found. Please register your biometrics first.", "biometricAuthentication": "Biometric Authentication", "biometricAuthDescription": "Touch your fingerprint sensor or use face recognition...", "biometricAuthSuccess": "Biometric Authentication Successful", "biometricAuthSuccessDescription": "Authentication completed successfully", "biometricAuthFailed": "Biometric Authentication Failed", "pinAlsoRequired": "PIN verification also required", "pinAlsoRequiredDescription": "Please enter your PIN to complete the verification process.", "locationPermissionGranted": "Location access granted", "locationPermissionGrantedDescription": "You can now proceed with attendance recording.", "locationPermissionStillDenied": "Location access still denied", "locationPermissionStillDeniedDescription": "Please allow location access in your browser settings to record attendance.", "locationError": "Location error", "locationErrorDescription": "There was an error accessing your location. Please try again.", "cameraPermissionGranted": "Camera access granted", "cameraPermissionGrantedDescription": "You can now start scanning QR codes.", "cameraPermissionStillDenied": "Camera access still denied", "cameraPermissionStillDeniedDescription": "Please allow camera access in your browser settings to scan QR codes.", "cameraError": "Camera error", "cameraErrorDescription": "There was an error accessing your camera. Please try again.", "biometricRequiredBlocked": "Biometric authentication required", "biometricRequiredBlockedDescription": "This school requires biometric authentication only. You cannot record attendance without completing biometric verification.", "biometricRequiredForBoth": "Biometric authentication required", "biometricRequiredForBothDescription": "This school requires both biometric and PIN verification. You must complete biometric authentication first.", "alreadyPresent": "Already Present", "alreadyPresentDescription": "Your attendance was already recorded at {{time}}", "attendanceRecorded": "Attendance Recorded", "attendanceRecordedDescription": "Your attendance has been successfully recorded!", "error": "Error", "errorDescription": "Failed to record attendance. Please try again.", "locationWarning": "Location Warning", "locationWarningDescription": "You are {{distance}} away from your room, which is {{excessDistance}}m outside the {{allowedRadius}}m allowed radius. Your teacher will be notified."}, "validation": {"enterPin": "Please enter your PIN", "noPinSet": "No PIN set in your profile. Please update your profile.", "incorrectPin": "Incorrect PIN. Please try again."}, "success": {"alreadyPresent": "Already Present", "alreadyPresentDescription": "Your attendance was already recorded for today", "attendanceRecorded": "Attendance Recorded", "attendanceRecordedDescription": "Your attendance has been successfully recorded", "scanAnotherCode": "Scan Another QR Code"}, "locationInstructions": {"chrome": {"step1": "Click the lock icon 🔒 in the address bar", "step2": "Click \"Site settings\"", "step3": "Find \"Location\" and change it to \"Allow\"", "step4": "Refresh this page"}, "edge": {"step1": "Click the lock icon 🔒 in the address bar", "step2": "Click \"Site permissions\"", "step3": "Find \"Location\" and change it to \"Allow\"", "step4": "Refresh this page"}, "firefox": {"step1": "Click the lock icon 🔒 in the address bar", "step2": "Click \"More Information\"", "step3": "Go to \"Permissions\"", "step4": "Find \"Access Your Location\" and remove the setting or set to \"Allow\"", "step5": "Refresh this page"}, "safari": {"step1": "Click Safari menu → Preferences", "step2": "Go to \"Privacy & Security\"", "step3": "Under \"Website tracking\", click \"Manage Website Settings\"", "step4": "Find this website and set Location to \"Allow\"", "step5": "Refresh this page"}, "generic": {"step1": "Look for the lock icon 🔒 or site settings in your browser's address bar", "step2": "Find the location permission settings", "step3": "Allow location access for this site", "step4": "Refresh this page"}}, "unknownRoom": "Unknown Room", "defaultRoom": "Default Room", "securityAlerts": {"multipleActiveSessions": "Multiple Active Sessions", "multipleActiveSessionsDescription": "Student has multiple active attendance sessions running simultaneously.", "suspiciousLocationData": "Suspicious Location Data", "suspiciousLocationDataDescription": "Student's location data appears to be artificially generated or spoofed.", "alertTitle": "🚨 {{title}}", "alertMessage": "Security Alert: {{studentName}} - {{message}}"}, "securityNotice": {"title": "Security Notice:", "unusualLocationData": "Unusual location data detected", "teacherNotified": "Your teacher has been notified."}, "notifications": {"attendanceRecorded": "✅ Attendance Recorded", "attendanceRecordedMessage": "You have successfully checked in to {{roomName}} at {{time}}."}, "distanceAlerts": {"title": "Distance Alerts", "loading": "Loading alerts...", "noAlerts": "No distance alerts to display", "unread": "Unread", "room": "Room", "viewLocation": "View Location", "markAsRead": "<PERSON> <PERSON>", "newAlert": "New Distance Alert", "fetchFailed": "Failed to fetch distance alerts", "markReadFailed": "Failed to mark alert as read", "deleteSuccess": "<PERSON><PERSON> deleted successfully", "deleteFailed": "Failed to delete alert"}}, "teacher": {"settings": {"locationSettings": "Location Settings", "locationSettingsDescription": "Configure location settings for attendance verification", "blockLocations": "Block Locations", "roomLocations": "Room Locations", "verificationSettings": "Verification Settings", "selectBlock": "Select Block", "selectBlockPlaceholder": "Select a block", "selectBlockFirst": "Select a block first", "allBlocks": "All Blocks", "noRoomsInBlock": "No rooms in this block", "showingRoomsFromBlock": "Showing {{count}} rooms from Block {{blockName}}", "showingAllRooms": "Showing all {{count}} rooms", "blockLocationInfo": "Setting a location for a block will apply to all rooms in that block. This is recommended for most cases as it's more efficient than setting individual room locations.", "selectRoom": "Select Room", "selectRoomPlaceholder": "Select a room", "unknownBuilding": "Unknown Building", "roomLocationInfo": "Room-specific locations override block locations. Only set room locations if you need different settings than the block default.", "configureVerification": "Configure Location Verification", "verificationDescription": "Control whether students need to verify their location when recording attendance", "verificationMethodSettings": "Verification Method Settings", "verificationMethodDescription": "Configure which authentication methods students must use when recording attendance.", "selectVerificationMethod": "Select Verification Method Requirement", "verificationEither": "Either Biometric or PIN", "verificationEitherDescription": "Students can choose between biometric authentication or PIN verification. Most flexible option.", "verificationBiometricOnly": "Biometric Authentication Only", "verificationBiometricOnlyDescription": "Students must use biometric authentication (fingerprint, face recognition, etc.). Highest security but requires compatible devices.", "verificationPinOnly": "PIN Verification Only", "verificationPinOnlyDescription": "Students must use PIN verification. Most compatible option that works on all devices.", "verificationBothRequired": "Both Biometric and PIN Required", "verificationBothRequiredDescription": "Students must complete both biometric authentication AND PIN verification. Maximum security but may slow down attendance process.", "recommended": "Recommended", "highSecurity": "High Security", "compatible": "Compatible", "maximumSecurity": "Maximum Security", "biometricOptional": "Biometric Optional", "pinOptional": "PIN Optional", "biometricRequired": "Biometric Required", "pinRequired": "PIN Required", "verificationMethodInfo": "These settings apply to all students in your school. Changes will take effect immediately for new attendance recordings.", "verificationMethodSettingsSaved": "Verification method settings have been updated successfully.", "locationVerification": "Location", "verificationMethods": "Methods", "verificationInfo": "Turning off location verification allows students to record attendance without being physically present. Use this for remote classes or when location verification is not needed.", "roomLocationSettings": "Room Location Settings", "roomLocationDescription": "Set the location of this room to enable location-based attendance verification", "blockLocationSettings": "Block Location Settings", "blockLocationDescription": "Set the location of this block to enable location-based attendance verification", "updateCurrentLocation": "Update Current Location", "setCurrentLocation": "Set Current Location", "latitude": "Latitude", "longitude": "Longitude", "attendanceRadius": "Attendance Radius (meters)", "studentsWithinRadiusRoom": "Students must be within this radius to verify attendance", "studentsWithinRadiusBlock": "Students must be within this radius to verify attendance for any room in Block {{blockName}}", "locationPermissionDenied": "Location access is denied. Please enable location access in your browser settings to set room location.", "locationUpdated": "Location Updated", "locationUpdatedMessage": "Room location has been successfully set", "blockLocationUpdatedMessage": "Block {{blockName}} location has been successfully set", "radiusUpdated": "<PERSON><PERSON>", "radiusUpdatedMessage": "Attendance radius has been updated successfully", "blockRadiusUpdatedMessage": "Attendance radius for Block {{blockName}} has been updated successfully", "errorUpdatingRadius": "Failed to update attendance radius", "locationVerificationSettings": "Location Verification Settings", "locationVerificationDescription": "Configure location verification requirements for attendance", "enableLocationVerification": "Enable Location Verification", "blockLevelVerification": "Block-level Verification", "roomLevelVerification": "Room-level Verification", "whenDisabled": "When disabled, no location verification will be required for any attendance", "whenBlockEnabled": "When enabled, students must be within {{distance}}m of the block", "whenRoomEnabled": "When enabled, students must be within {{distance}}m of the room", "adminOnlySettings": "Location settings can only be modified by administrators. Your settings have been disabled. Please contact an administrator if you need to change location verification settings.", "saveSettings": "Save Settings", "settingsSaved": "Settings Saved", "settingsSavedMessage": "Your location verification settings have been saved successfully.", "errorSavingSettings": "Error Saving <PERSON>", "errorSavingSettingsMessage": "There was an error saving your settings. Please try again.", "studentsWithinRadius": "Students must be within the specified radius to verify attendance when location verification is enabled.", "locationRetrieved": "Location Retrieved", "locationRetrievedDescription": "Current location has been loaded. You can edit the coordinates if needed, then click Save.", "saveLocation": "Save Location"}, "dashboard": {"title": "Teacher Dashboard", "attendance": "Attendance", "excuses": "Excuses", "statistics": "Today's Statistics", "statisticsDescription": "Overview of today's attendance", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused", "studentsInClass": "Students present", "studentsMissing": "Students missing", "studentsLate": "Students late", "excusedAbsences": "Excused absences", "roomAttendance": "Room Attendance", "manageRoomAttendance": "Manage dormitory room attendance", "sendVerificationReminders": "Send Verification Reminders", "lastSent": "Last sent: {{time}}", "showAllStudents": "Show All Students", "allStudents": "All Students", "showOnlyAbsent": "Show Only Absent Students", "addBlock": "Add Block", "addRoom": "Add Room to Block", "remindersFailed": "Failed to Send Reminders", "remindersFailedDescription": "There was an error sending attendance reminders. Please try again.", "failedToFetchBlocks": "Failed to fetch blocks", "setupRequired": "Setup Required", "setupRequiredDescription": "Please contact an administrator to complete the system setup.", "failedToFetchRooms": "Failed to fetch rooms", "addBlockDescription": "Please use the block selector to add a new block", "addRoomDescription": "Please use the block selector to add a new room", "refresh": "Refresh", "options": "Options", "block": "Block: {{name}}", "room": "Room: {{name}}", "search": "Search: {{query}}", "searchPlaceholder": "Search students...", "gridView": "Grid View", "listView": "List View", "studentDirectory": "Student Directory", "studentsNeedVerification": "Students who need to verify attendance", "manageStudentAttendance": "Manage student attendance and records", "activeFilters": "Active filters:", "absentOnly": "Absent only", "showingAll": "(showing all)", "noStudentsFound": "No Students Found", "showingAllStudents": "Showing all students instead of filtered {{type}}", "noStudentsInRoom": "No students were found in the selected room or block. This may be because students haven't been assigned to this room/block in their profiles.", "clearFilters": "Clear Filters", "howToAssignRooms": "How to Assign Rooms", "studentRoomAssignment": "Students need to select their room and block in their profile settings. You can also update student profiles in the Student Management section.", "noStudentsToDisplay": "No students to display", "noAbsentStudents": "There are no absent students matching your criteria.", "noStudentsMatchingSearch": "No students found matching your search.", "loadingStudents": "Loading Students", "pleaseWaitLoading": "Please wait while we load the student data..."}, "attendance": {"statusUpdated": "Attendance Status Changed", "studentMarkedAs": "{{name}} has been marked {{status}}", "statusChangedTo": "You changed the attendance status of {{name}} to {{status}}", "updatedAt": "Updated at {{time}}", "attendanceUpdated": "Attendance Updated", "studentNowMarked": "{{name}} is now marked as {{status}}", "errorUpdatingStatus": "Error Updating Status", "failedToUpdate": "Failed to update {{name}}'s status to {{status}}.", "roomAssignmentIssue": "Room assignment issue: {{message}}. Please assign {{name}} to a room first.", "studentVerifiedPresence": "{{name}} has verified their presence", "noStudentsAvailable": "No Students Available", "pleaseWaitForData": "Please wait for student data to load.", "todaysAttendance": "Today's Attendance", "noAbsentStudents": "No Absent Students", "markPresent": "<PERSON> as Present", "markAbsent": "<PERSON> as Absent", "noAbsentStudentsInTarget": "Great news! All students in {{target}} are already present - no reminders needed.", "sendingReminders": "Sending reminders to {{count}} absent students in {{target}}", "noRemindersSent": "📋 No Reminders Needed", "noAbsentStudentsFound": "All students have already checked in - no absent students found to remind.", "remindersSent": "Reminders Sent Successfully", "remindersPartiallySent": "⚠️ Some Reminders Failed to Send", "sentVerificationReminders": "Sent verification reminders to {{count}} student(s)", "failedToSend": "{{count}} failed to send", "target": "Target: {{name}} • Time: {{time}}", "attendanceRemindersSent": "📱 Attendance Reminders Sent Successfully", "studentsNotified": "Successfully sent attendance reminders to {{count}} absent students in {{target}}", "failedToSendReminders": "Failed to Send Reminders", "couldNotSendReminders": "Could not send attendance verification reminders. Please try again.", "remindersSentSuccessfully": "✅ All Attendance Reminders Delivered", "errorLoadingData": "Error Loading Data", "errorLoadingStudentData": "There was an error loading student data. Please try refreshing the page.", "attendanceUIOnlyNoRoom": "Attendance status updated in UI only. No room available for database record.", "failedToUpdateStatus": "Failed to update attendance status. Please try again."}, "profile": {"teacherProfile": "Teacher Profile", "completeYourProfile": "Complete Your Teacher Profile", "updateProfileInfo": "Update your profile information", "viewAndManageProfile": "View and manage your teacher profile", "fillRequiredFields": "Please fill in all required fields to complete your profile setup.", "profileSetupComplete": "Profile setup complete", "profileSetupMessage": "Your profile has been successfully set up. You can now access all features.", "department": "Department", "position": "Position", "subject": "Subject", "teacherId": "Teacher ID", "enterTeacherId": "Enter your teacher ID", "enterDepartment": "Enter your department", "enterPosition": "Enter your position", "enterSubject": "Enter your subject"}, "excuses": {"excusesManagement": "Excuses Management", "manageStudentExcuses": "Review and manage student absence excuse requests", "pending": "Pending", "approved": "Excuse Approved", "rejected": "Excuse Rejected", "all": "All", "student": "Student", "date": "Date", "duration": "Duration", "reason": "Reason", "actions": "Actions", "status": "Status", "approve": "Approve", "reject": "Reject", "addNote": "Add Note", "notes": "Notes", "enterNotes": "Enter notes for this excuse", "saveNotes": "Save Notes", "confirmApprove": "Confirm Approval", "confirmReject": "Confirm Rejection", "approveExcuseQuestion": "Are you sure you want to approve this excuse?", "rejectExcuseQuestion": "Are you sure you want to reject this excuse?", "approvedDescription": "The excuse has been approved successfully", "rejectedDescription": "The excuse has been rejected", "excuseApproved": "Excuse Approved", "excuseRejected": "Excuse Rejected", "excuseUpdated": "Excuse Updated", "updateError": "Update Error", "noPendingExcuses": "No Pending Excuses", "noApprovedExcuses": "No Approved Excuses", "noRejectedExcuses": "No Rejected Excuses", "noExcusesMessage": "There are no excuses to display", "loadingExcuses": "Loading excuses...", "excuseDetails": "Excuse Details", "reviewExcuse": "Review and manage this excuse request", "confirmDeleteExcuse": "Are you sure you want to delete this excuse request? This action cannot be undone.", "export": "Export", "exportAsHTML": "Export as HTML", "exportAsPDF": "Export as PDF", "exportAsCSV": "Export as CSV", "exportComplete": "Export Complete", "excusesExported": "{{count}} excuses exported to {{format}} format", "excusesReport": "Excuses Report", "comprehensiveReport": "Comprehensive Teacher Excuse Management Report", "generatedOn": "Generated on", "totalExcuses": "Total Excuses", "dateRange": "Date Range", "notSpecified": "Not Specified", "unknownStudent": "Unknown Student", "unknownRoom": "Unknown Room", "notApplicable": "N/A", "reportContains": "This report contains {{count}} excuse records as of {{date}}"}, "alerts": {"distanceAlerts": "Distance Alerts", "studentDistanceVerificationAlerts": "Student distance verification alerts", "noDistanceAlerts": "No distance alerts", "loadingAlerts": "Loading alerts...", "deleteAll": "Delete All", "deleteAllTitle": "Delete all alerts", "alertDismissed": "<PERSON><PERSON>", "alertDeleted": "The alert has been deleted", "alertsCleared": "<PERSON><PERSON><PERSON> Cleared", "allAlertsDeleted": "All alerts have been deleted", "errorDeletingAlert": "Failed to delete alert", "errorDeletingAlerts": "Failed to delete alerts", "failedToLoad": "Failed to load alerts", "distanceAlert": "Distance Alert", "severity": "Severity", "distance": "Distance", "room": "Room", "deleteAlertTitle": "Delete alert", "securityLocationAlerts": "Security & Location Alerts", "securityLocationAlertsDescription": "Student security and location verification alerts", "noSecurityLocationAlerts": "No security or location alerts", "securityAlert": "Security Alert", "attendanceAlert": "Attendance Alert", "alert": "<PERSON><PERSON>", "outsideRadius": "outside radius", "viewLocation": "View Location", "getDirections": "Get Directions", "severityLow": "Low", "severityMedium": "Medium", "severityHigh": "High", "severityCritical": "Critical"}}, "admin": {"attendanceManagement": {"title": "Attendance Management", "description": "Manage attendance reports and school structure", "reports": "Attendance Reports", "reportsShort": "Reports", "structure": "School Structure", "structureShort": "Structure", "remindersTab": "Attendance Reminders", "remindersShort": "Reminders", "dailyReports": "Daily Attendance Reports", "dailyReportsDescription": "Export attendance data for the past 7 days", "schoolStructure": "Blocks & Rooms Management", "schoolStructureDescription": "Manage your school's blocks and rooms", "last7Days": "Last 7 Days", "last7DaysDescription": "View and export attendance reports for the past week", "exportDay": "Export Report", "cleanup": "Cleanup Old Records", "loadingReports": "Loading attendance reports...", "loadingStructure": "Loading school structure...", "fetchError": "Failed to load attendance data", "fetchDataError": "Failed to load blocks and rooms", "exportSuccess": "Report exported successfully", "exportSuccessDescription": "Attendance report for {{date}} has been downloaded", "exportError": "Failed to export report", "cleanupSuccess": "Old records cleaned up", "cleanupSuccessDescription": "Records older than 7 days have been removed", "cleanupError": "Failed to cleanup old records", "csv": {"reportDate": "Report Date", "totalStudents": "Total Students", "presentStudents": "Present Students", "absentStudents": "Absent Students", "attendanceRate": "Attendance Rate", "studentName": "Student Name", "studentId": "Student ID", "email": "Email", "status": "Status", "checkInTime": "Check-in Time", "method": "Verification Method"}, "manageStructure": "Manage School Structure", "manageStructureDescription": "Add, edit, and organize blocks and rooms for your school", "blocks": "Blocks", "addBlock": "Add Block", "createFirstBlock": "Create Your First Block", "noBlocks": "No blocks found. Create your first block to get started.", "blockName": "Block Name", "blockNamePlaceholder": "Enter block name (e.g., Main Building, Science Wing)", "blockDescription": "Description", "blockDescriptionPlaceholder": "Optional description for this block", "createBlock": "Create Block", "editBlock": "Edit Block", "createBlockDescription": "Add a new block to organize rooms in your school", "editBlockDescription": "Update the block information", "blockCreated": "Block created successfully", "blockCreatedDescription": "{{name}} has been added to your school", "blockUpdated": "Block updated successfully", "blockUpdatedDescription": "{{name}} has been updated", "blockDeleted": "Block deleted successfully", "blockDeletedDescription": "{{name}} has been removed from your school", "deleteBlockTitle": "Delete Block", "deleteBlockDescription": "Are you sure you want to delete {{name}}? This will also delete all rooms in this block, unassign all students from these rooms, and remove all associated data (attendance records, location settings, etc.). This action cannot be undone.", "cannotDeleteBlock": "Cannot delete block", "blockHasRooms": "This block contains {{count}} room(s). Please delete all rooms first.", "saveBlockError": "Failed to save block", "deleteBlockError": "Failed to delete block", "roomCount": "{{count}} room", "roomCount_plural": "{{count}} rooms", "allRooms": "All Rooms", "roomsInBlock": "Rooms in {{blockName}}", "addRoom": "Add Room", "createFirstRoom": "Create Your First Room", "noRooms": "No rooms found. Create your first room to get started.", "noRoomsInBlock": "No rooms in this block. Add a room to get started.", "roomName": "Room Name", "roomNamePlaceholder": "Enter room name (e.g., Room 101, Chemistry Lab)", "selectBlock": "Select Block", "selectBlockPlaceholder": "Choose a block for this room", "buildingPlaceholder": "Building name (optional)", "floorPlaceholder": "Floor number", "capacity": "Capacity", "capacityPlaceholder": "Maximum students", "floor": "Floor", "building": "Building", "createRoom": "Create Room", "editRoom": "Edit Room", "createRoomDescription": "Add a new room to the selected block", "editRoomDescription": "Update the room information", "roomCreated": "Room created successfully", "roomCreatedDescription": "{{name}} has been added to your school", "roomUpdated": "Room updated successfully", "roomUpdatedDescription": "{{name}} has been updated", "roomDeleted": "Room deleted successfully", "roomDeletedDescription": "{{name}} has been removed from your school", "deleteRoomTitle": "Delete Room", "deleteRoomDescription": "Are you sure you want to delete {{name}}? This will unassign all students from this room and delete all associated data (attendance records, location settings, QR sessions, etc.). This action cannot be undone.", "saveRoomError": "Failed to save room", "deleteRoomError": "Failed to delete room", "reminders": {"title": "Automated Attendance Reminders", "description": "Configure automated reminders and send manual notifications to absent students", "loading": "Loading reminder settings...", "serviceStatus": "Service Status", "serviceRunning": "Reminder Service", "active": "Active", "inactive": "Inactive", "sentToday": "Reminders <PERSON>", "nextCheck": "Next Check", "automatedSettings": "Automated Reminder Settings", "enableAutomation": "Enable Automated Reminders", "enableDescription": "Automatically send reminders to absent students before attendance session ends", "minutesBefore": "Minutes Before End", "minutesBeforeEnd": "minutes before session ends", "minutesDescription": "How many minutes before the attendance session ends should the reminder be sent", "reminderWillSend": "Reminder Schedule", "timePreview": "Reminders will be sent at {{time}} (attendance ends at {{endTime}}, {{minutes}} minutes before)", "lateMarkingTitle": "Smart Late Detection", "lateMarkingDescription": "Students who scan/check in after the automated reminder is sent will be marked as 'late' instead of 'present'. Students who don't scan at all remain 'absent'.", "saveSettings": "Save Re<PERSON><PERSON>", "settingsSaved": "Reminder <PERSON><PERSON><PERSON> Saved", "automationEnabled": "Automated reminders enabled - will send {{minutes}} minutes before session ends", "automationDisabled": "Automated reminders disabled", "loadError": "Failed to load reminder settings", "saveError": "Failed to save reminder settings", "manualReminder": "Manual Reminder", "manualDescription": "Send an immediate reminder to all absent students (like teacher functionality)", "sendNow": "Send Reminder Now", "sending": "Sending...", "manualSent": "Manual Reminder <PERSON>", "sentToStudents": "Reminder sent to {{count}} absent students", "noAbsentStudents": "No Absent Students", "allStudentsPresent": "All students have already checked in", "manualSendError": "Failed to send manual reminder"}}, "carousel": {"title": "Carousel Management", "description": "Manage carousel content for student and teacher dashboards", "items": "Carousel Items", "addItem": "Add Item", "editItem": "Edit Carousel Item", "noItems": "No carousel items yet. Add your first item to get started.", "order": "Order", "image": "Image", "audience": "Audience", "status": "Status", "active": "Active", "inactive": "Inactive", "students": "Students", "teachers": "Teachers", "itemTitle": "Title", "itemDescription": "Description", "itemTitlePlaceholder": "Enter a title for this item", "itemDescriptionPlaceholder": "Enter a description (optional)", "imageUpload": "Image Upload", "targetAudience": "Target Audience", "activeItem": "Active", "itemFormDescription": "Fill in the details for this carousel item.", "itemUpdated": "Carousel item updated successfully", "itemAdded": "Carousel item added successfully", "itemDeleted": "Carousel item deleted successfully", "itemActivated": "Carousel item activated", "itemDeactivated": "Carousel item deactivated", "errorLoading": "Failed to load carousel items", "errorSaving": "Failed to save carousel item", "errorDeleting": "Failed to delete carousel item", "errorToggling": "Failed to update carousel item status", "errorReordering": "Failed to reorder carousel items", "titleRequired": "Title is required", "mediaRequired": "Please select an image", "audienceRequired": "Please select at least one audience", "invalidFileType": "Please select an image file.", "fileTooLarge": "Image file is too large. Maximum size is 5MB.", "errorReadingFile": "Error reading image file. Please try again.", "fileUploaded": "Image uploaded successfully", "fileUploadError": "Failed to upload image"}, "backToSystemAdmin": "Back to System Admin", "systemAdminView": "System Admin View", "tabs": {"users": "Users", "students": "Students", "schools": "Schools", "qrCodes": "QR Codes", "fraud": "<PERSON>aud Detection", "excuses": "Excuses", "parents": "Parents", "alerts": "<PERSON><PERSON><PERSON>", "schoolSettings": "School Settings", "auditLogs": "<PERSON><PERSON>", "settings": "Settings", "security": "Security", "attendanceSettings": "Attendance Settings"}, "qrCodes": {"generator": "Generator", "tabletSetup": "Tablet Setup", "tabletManagement": "Tablet Management", "automatic": "Automatic"}, "dashboard": {"title": "School Admin Dashboard", "users": "Users", "students": "Students", "qrCodes": "QR Codes", "fraud": "<PERSON>aud Detection", "excuses": "Excuses", "alerts": "<PERSON><PERSON><PERSON>", "settings": "Settings", "schoolSettings": "School Settings", "auditLogs": "<PERSON><PERSON>", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused"}, "attendance": {"statusUpdated": "Status Updated", "studentMarkedAs": "{{name}} has been marked {{status}}", "updatedAt": "Updated at {{time}}", "attendanceUpdated": "Attendance Updated", "studentNowMarked": "{{name}} is now marked as {{status}}", "errorUpdatingStatus": "Error Updating Status", "failedToUpdate": "Failed to update {{name}}'s status to {{status}}.", "roomAssignmentIssue": "Room assignment issue: {{message}}. Please assign {{name}} to a room first.", "studentVerifiedPresence": "{{name}} has verified their presence", "noStudentsAvailable": "No Students Available", "pleaseWaitForData": "Please wait for student data to load.", "todaysAttendance": "Today's Attendance", "noAbsentStudents": "No Absent Students"}, "userManagement": {"title": "User Management", "description": "View and manage all users in the system", "searchPlaceholder": "Search by name or ID...", "addUser": "Add User", "students": "Students", "teachers": "Teachers", "administrators": "Administrators", "totalUsers": "Total Users", "user": "User", "role": "Role", "id": "ID", "status": "Status", "actions": "Actions", "noUsersFound": "No users found", "noUsersMatching": "No users found matching", "allRoles": "All Roles", "resetPassword": "Reset password", "blockUser": "Block user", "unblockUser": "Unblock user", "enableMaintenanceMode": "Enable maintenance mode", "disableMaintenanceMode": "Disable maintenance mode", "blockUserConfirm": "Are you sure you want to block {{name}}? This will prevent the user from accessing the system until they are unblocked.", "unblockUserConfirm": "Are you sure you want to unblock {{name}}? This will allow the user to access the system again.", "enableMaintenanceModeConfirm": "Are you sure you want to enable maintenance mode for {{name}}? This will prevent the user from accessing the system until maintenance mode is disabled.", "disableMaintenanceModeConfirm": "Are you sure you want to disable maintenance mode for {{name}}? This will allow the user to access the system again.", "deleteUserConfirm": "Are you sure you want to delete {{name}}? This action cannot be undone and will permanently remove the user and all associated data from the system.", "usersCreatedForYourSchool": "Users will be created for your school", "deleteUser": "Delete user", "userActions": "User Actions", "blocked": "Blocked", "maintenance": "Maintenance", "addNewUser": "Add New User", "addNewUserDescription": "Create a new user account. The user will receive an email to set up their password.", "fullName": "Full Name", "email": "Email", "temporaryPassword": "Temporary Password", "school": "School", "cancel": "Cancel", "createUser": "Create User", "creating": "Creating..."}, "userDetails": {"title": "User Details", "edit": "Edit", "name": "Name", "email": "Email", "joined": "Joined", "course": "Course", "block": "Block", "room": "Room", "studentId": "Student ID", "department": "Department", "subject": "Subject", "teacherId": "Teacher ID", "position": "Position", "notSet": "Not set"}, "userEditForm": {"title": "Edit User Details", "name": "Name", "studentId": "Student ID", "course": "Course", "blockCorridor": "Block/Corridor", "roomNumber": "Room Number", "cancel": "Cancel", "saveChanges": "Save Changes", "saving": "Saving...", "success": "Success", "successMessage": "User details updated successfully.", "error": "Error", "errorMessage": "Failed to update user details. Please try again."}, "studentDirectory": {"title": "Student Directory", "description": "View and manage all students in your school", "searchPlaceholder": "Search by name or ID...", "filterByBlock": "Filter by block", "filterByRoom": "Filter by room", "profile": "Profile", "name": "Name", "studentId": "Student ID", "course": "Course", "block": "Block", "room": "Room", "status": "Status", "actions": "Actions", "notSet": "Not set", "notAssigned": "Not Assigned", "viewMode": "View Mode", "gridView": "Grid View", "listView": "List View", "noStudentsFound": "No students found", "loadingStudents": "Loading students..."}, "settings": {"title": "<PERSON><PERSON>s", "description": "Configure system settings for your school", "locationSettings": {"locationRetrieved": "Location Retrieved", "locationRetrievedDescription": "Current location has been loaded. You can edit the coordinates if needed, then click Save.", "saveLocation": "Save Location", "locationUpdated": "Location Updated", "locationUpdatedMessage": "Room location has been successfully set", "blockLocationUpdatedMessage": "Block {{blockName}} location has been successfully set", "radiusUpdated": "<PERSON><PERSON>", "radiusUpdatedMessage": "Attendance radius has been updated successfully", "blockRadiusUpdatedMessage": "Attendance radius for Block {{blockName}} has been updated successfully", "errorUpdatingRadius": "Failed to update attendance radius", "setLocationFirst": "Please set the location coordinates first before updating the radius"}, "blockLocations": "Block Locations", "roomLocations": "Room Locations", "verification": "Verification", "biometricRequests": "Biometric Deletion Requests", "permissions": "Permissions", "excuseSettings": "Excuse Settings", "attendanceSettings": "Attendance Settings", "notificationSettings": "Notification Settings", "systemSettings": "System Settings", "saveSettings": "Save Settings", "settingsSaved": "Settings Saved", "settingsSavedMessage": "Your settings have been saved successfully.", "errorSavingSettings": "Error Saving <PERSON>", "errorSavingSettingsMessage": "There was an error saving your settings. Please try again.", "systemLocationSettings": "System Location Settings", "systemLocationDescription": "Configure location settings for attendance verification across the system", "selectBlock": "Select Block", "selectRoom": "Select Room", "allBlocks": "All Blocks", "selectBlockFirst": "Select a block first", "noRoomsInBlock": "No rooms in this block", "showingRoomsFromBlock": "Showing {{count}} rooms from Block {{blockName}}", "showingAllRooms": "Showing all {{count}} rooms", "blockLocationSettings": "Block Location Settings", "blockLocationDescription": "Set the location of block {blockName} to enable location-based attendance verification", "roomLocationSettings": "Room Location Settings", "roomLocationDescription": "Set the location of this room to enable location-based attendance verification", "updateCurrentLocation": "Update Current Location", "setCurrentLocation": "Set Current Location", "latitude": "Latitude", "longitude": "Longitude", "attendanceRadius": "Attendance Radius (meters)", "studentsWithinRadius": "Students must be within this radius to verify attendance for any room in Block {blockName}", "studentsWithinRadiusRoom": "Students must be within this radius to verify attendance", "locationPermissionDenied": "Location access is denied. Please enable location access in your browser settings to set block location.", "roomLocationPermissionDenied": "Location access is denied. Please enable location access in your browser settings to set room location.", "roomLocationInfo": "Room-specific locations override block locations. Only set room locations if you need different settings than the block default.", "blockLocationInfo": "Setting a location for a block will apply to all rooms in that block. This is recommended for most cases as it's more efficient than setting individual room locations.", "verificationInfo": "Turning off location verification allows students to record attendance without being physically present. Use this for remote classes or when location verification is not needed.", "teacherPermissionsInfo": "When teachers are not allowed to edit settings, only admin settings will be used for all location verifications. Teachers will see a message indicating they cannot edit location settings.", "attendanceFooter": "Students must be within the specified radius to verify attendance when location verification is enabled.", "configureLocationVerification": "Configure Location Verification", "controlLocationVerification": "Control whether students need to verify their location when recording attendance", "verificationMethodSettings": "Verification Method Settings", "verificationMethodDescription": "Configure which authentication methods students must use when recording attendance.", "selectVerificationMethod": "Select Verification Method Requirement", "verificationEither": "Either Biometric or PIN", "verificationEitherDescription": "Students can choose between biometric authentication or PIN verification. Most flexible option.", "verificationBiometricOnly": "Biometric Authentication Only", "verificationBiometricOnlyDescription": "Students must use biometric authentication (fingerprint, face recognition, etc.). Highest security but requires compatible devices.", "verificationPinOnly": "PIN Verification Only", "verificationPinOnlyDescription": "Students must use PIN verification. Most compatible option that works on all devices.", "verificationBothRequired": "Both Biometric and PIN Required", "verificationBothRequiredDescription": "Students must complete both biometric authentication AND PIN verification. Maximum security but may slow down attendance process.", "recommended": "Recommended", "highSecurity": "High Security", "compatible": "Compatible", "maximumSecurity": "Maximum Security", "biometricOptional": "Biometric Optional", "pinOptional": "PIN Optional", "biometricRequired": "Biometric Required", "pinRequired": "PIN Required", "schoolWideVerificationInfo": "These settings apply to all students in your school. Changes will take effect immediately for new attendance recordings.", "verificationMethodSettingsSaved": "Verification method settings have been updated successfully.", "locationVerification": "Location", "verificationMethods": "Methods", "locationVerificationSettings": "Location Verification Settings", "configureVerificationRequirements": "Configure location verification requirements for attendance", "enableLocationVerification": "Enable Location Verification", "whenEnabledVerifyLocation": "When enabled, students must verify their location to record attendance", "blockLevelVerification": "Block-level Verification", "whenEnabledWithinBlockRadius": "When enabled, students must be within {radius}m of the block", "roomLevelVerification": "Room-level Verification", "whenEnabledWithinRoomRadius": "When enabled, students must be within {radius}m of the room", "saving": "Saving...", "locationSettingsSaved": "Location verification settings saved successfully", "locationSettingsSaveError": "Failed to save location verification settings", "locationSettingsLoadError": "Failed to load location verification settings. Using defaults.", "teacherPermissions": "Teacher Permissions", "controlTeacherPermissions": "Control whether teachers can edit location settings", "allowTeachersToEdit": "Allow Teachers to Edit Location Settings", "teachersCanEdit": "Teachers can edit location settings for their rooms and blocks. Their settings will take precedence over admin settings.", "teachersCannotEdit": "Teachers cannot edit location settings. Only admin settings will be used for all location verifications.", "savePermissions": "Save Permissions", "teachersWillSeeAndModify": "Teachers will see and be able to modify their location settings.", "teachersWillSeeMessage": "Teachers will see a message indicating they cannot edit location settings.", "teacherPermissionsUpdated": "Teacher permissions updated. Teachers {canEdit} edit location settings.", "teacherPermissionsSaveError": "Failed to save teacher permissions. Please try again.", "teacherPermissionsLoadError": "Failed to load teacher permissions. Using default settings.", "teacherPermissionsNotAllowed": "When teachers are not allowed to edit settings, all location verifications will only use admin settings. Teachers will see a message indicating they cannot edit location settings.", "can": "can", "cannot": "cannot", "excuses": "Excuses", "attendance": "Attendance", "notifications": "Notifications", "socialMedia": "Social Media", "excuseSubmissionSettings": "Excuse Submission Settings", "controlExcuseSubmission": "Control when students can submit excuse requests", "excuseTimeRestrictionInfo": "Setting time restrictions helps prevent unnecessary excuse requests and ensures that students submit excuses during appropriate hours.", "submissionTimeRestrictions": "Submission Time Restrictions", "submissionTimeRestrictionsDescription": "Students will only be able to submit excuse requests during the time window you specify below. Outside of these hours, the submission form will be disabled.", "submissionStartTime": "Submission Start Time", "submissionEndTime": "Submission End Time", "maxDaysInAdvance": "Max Days in Advance", "maxDaysInAdvanceDescription": "Maximum number of days in advance students can request excuses", "maxExcuseDuration": "Max Excuse Duration (Days)", "maxExcuseDurationDescription": "Maximum number of days for a single excuse request", "attendanceRecordingSettings": "Attendance Recording Settings", "controlAttendanceRecording": "Control when students can record their attendance", "attendanceTimeRestrictionInfo": "Setting time restrictions for attendance recording helps ensure that students only record attendance during appropriate class hours and prevents unauthorized attendance recording outside of class times.", "parentNotificationSettings": "Parent Notification Settings", "configureParentNotifications": "Configure how and when parents are notified about student excuses", "parentNotificationInfo": "Parent notifications help keep families informed about their children's attendance and excuse requests, improving communication between the school and parents.", "systemAdminSettings": "System Administrator Settings", "manageSystemSettings": "Manage system-wide settings and access controls", "systemAdminOnlyInfo": "These settings are only available to system administrators. The system admin code is required for creating new system administrator accounts.", "notificationPreferences": "Notification Preferences", "configureGlobalParentNotifications": "Configure global settings for parent notifications", "enableParentNotifications": "Enable Parent Notifications", "whenEnabledParentsNotified": "When enabled, parents will be notified about their children's excuse requests", "defaultNotificationMethod": "Default Notification Method", "emailOnly": "Email Only", "smsOnly": "SMS Only", "bothEmailAndSMS": "Both Email & SMS", "noNotificationsDisabled": "No Notifications (Disabled)", "importantNote": "Important Note", "globalDefaultSettingsInfo": "These are global default settings. Individual parent contact preferences will override these defaults.", "emailNotificationTemplates": "Email Notification Templates", "customizeEmailTemplates": "Customize the email templates sent to parents", "templateType": "Template Type", "newRequest": "New Request", "approved": "Approved", "rejected": "Rejected", "emailTemplate": "<PERSON>ail Te<PERSON>late", "availableVariables": "Available variables", "smsNotificationTemplates": "SMS Notification Templates", "customizeSmsTemplates": "Customize the SMS templates sent to parents", "smsTemplate": "SMS Template", "keepSmsMessagesConcise": "Keep SMS messages concise (160 characters or less for standard SMS)", "characters": "{{count}} characters", "resetToDefault": "Reset to De<PERSON>ult", "parentNotificationSettingsUpdated": "Parent notification settings have been updated successfully", "excuseSubmissionSettingsUpdated": "Excuse submission settings have been updated successfully", "failedToSaveNotificationSettings": "Failed to save notification settings", "confirmResetNotificationSettings": "Are you sure you want to reset all notification settings to default?", "settingsReset": "Settings Reset", "parentNotificationSettingsReset": "Parent notification settings have been reset to default values", "failedToLoadNotificationSettings": "Failed to load notification settings", "generalSettingsTab": "General Settings", "templateVariablesTab": "Template Variables", "emailTemplatesTab": "Email Templates", "smsTemplatesTab": "SMS Templates", "templateVariablesConfiguration": "Template Variables Configuration", "templateVariablesConfigurationDescription": "Configure the values for template variables used in notification messages. These variables will replace {{schoolName}}, {{contactEmail}}, and {{schoolPolicy}} in your templates.", "schoolName": "School Name", "schoolNamePlaceholder": "Enter your school name", "schoolNameHelp": "This will replace {{schoolName}} in your templates", "contactEmail": "Contact Email", "contactEmailPlaceholder": "<EMAIL>", "contactEmailHelp": "This will replace {{contactEmail}} in your templates", "schoolAttendancePolicy": "School Attendance Policy", "schoolAttendancePolicyPlaceholder": "Enter your school's attendance policy information", "schoolAttendancePolicyHelp": "This will replace {{schoolPolicy}} in your templates", "templateVariables": "Template Variables", "templateVariablesInfo": "These variables can be used in your email and SMS templates. Make sure to save your changes after updating these values.", "templatesUpdated": "Templates Updated", "templatesUpdatedToCurrentLanguage": "Email and SMS templates have been updated to match the current language.", "updateToCurrentLanguage": "Update to Current Language"}, "tablets": {"title": "Tablet Management", "subtitle": "Configure and manage attendance tablets for your rooms", "setupQR": {"title": "Tablet Setup QR Generator", "generateTitle": "Generate Setup QR Code", "generateSubtitle": "Create a QR code that tablets can scan to automatically configure themselves for a specific room and school.", "school": "School", "selectSchool": "Select a school", "block": "Block (Optional)", "selectBlock": "Select a block", "room": "Room", "selectRoom": "Select a room", "deviceName": "Device Name (Optional)", "deviceNamePlaceholder": "e.g., Room 101 Tablet", "setupQRCode": "Setup QR Code", "setupInstructions": "Setup Instructions:", "instruction1": "Ensure the tablet is connected to the same WiFi network", "instruction2": "Open the tablet's camera or QR scanner app", "instruction3": "Scan the QR code above", "instruction4": "The tablet will automatically open the setup page", "instruction5": "Wait for automatic registration and configuration", "note": "Note:", "noteText": "If scanning doesn't work, copy the manual setup URL below and paste it in the tablet's browser.", "manualSetupURL": "Manual Setup URL", "downloadQR": "Download QR", "copySetupURL": "Copy Setup URL", "secureSetup": "Secure Setup:", "secureSetupText": "Each tablet will be registered with a unique device certificate for secure authentication."}, "display": {"setupRequired": "Tablet Setup Required", "setupRequiredDescription": "Please scan the setup QR code provided by your administrator to configure this tablet for attendance tracking.", "retrySetup": "Retry Setup", "scanQRInstruction": "📱 Scan this QR code with your phone to mark attendance", "roomVerification": "Make sure you're in your assigned room before scanning", "noActiveQR": "No Active QR Code", "waitingForSession": "Waiting for attendance session to begin...", "attendanceQRCode": "Attendance QR Code", "connected": "Connected", "disconnected": "Disconnected", "lastUpdate": "Last update:", "capacity": "Capacity:", "recentCheckins": "Recent Check-ins", "checkedIn": "checked in!", "timeRemaining": "Time Remaining:", "active": "Active", "expiringSoon": "Expiring Soon", "refreshing": "🔄 Refreshing...", "preparingQR": "Preparing QR Code...", "attendanceClosed": "Attendance Closed", "qrWillAppear": "QR code will appear automatically during attendance hours", "opensAt": "Attendance opens at", "waitingConfig": "Waiting for attendance configuration...", "checkUpdates": "Check for Updates", "scanToMarkAttendance": "Scan To Mark Attendance", "openCameraApp": "Open your camera app and point it at the QR code", "clickMaximize": "Click maximize button for larger QR code", "todaysScans": "Today's Scans", "scansToday": "scans today", "authenticated": "Authenticated", "excellent": "Excellent", "good": "Good", "poor": "Poor", "offline": "Offline", "closed": "CLOSED", "liveAttendance": "Live Attendance", "block": "Block", "floor": "Floor", "loading": "Loading...", "testScan": "Test Scan", "checkedInSuccessfully": "Checked in successfully! ✨", "lateArrival": "Late arrival noted 📝", "earlyBird": "Early bird! 🐦", "new": "NEW", "lessThanMinuteAgo": "less than a minute ago", "waitingForScans": "Waiting for scans...", "studentAttendanceRealTime": "Student attendance will appear here in real-time", "checkingUpdates": "Checking for updates every 10 seconds", "unknownTablet": "Unknown Tablet", "tabletConfigured": "Tablet Configured! 🎉", "successfullyRegistered": "Successfully registered as {{deviceName}}", "maximizeQRCode": "Maximize QR Code", "refreshQRCode": "Refresh QR Code"}, "manage": {"title": "Registered Tablets", "subtitle": "Monitor and manage all registered tablet devices", "refresh": "Refresh", "loading": "Loading...", "noTablets": {"title": "No Tablets Registered", "subtitle": "Generate setup QR codes to register your first tablet", "setupFirst": "Setup First Tablet"}}}, "socialMedia": {"title": "Social Media Integration", "description": "Configure your school's social media channels to display updates in the student dashboard.", "enabled": "Enabled", "disabled": "Disabled", "settings": "Settings", "setupGuide": "Setup Guide", "enableFeed": "Enable Social Media Feed", "enableFeedDescription": "Show social media updates in the student dashboard", "globalSettings": "Global Settings", "refreshInterval": "Refresh <PERSON> (minutes)", "refreshIntervalDescription": "How often to check for new posts (5-1440 minutes)", "showEngagementStats": "Show Engagement Stats", "platformConfiguration": "Platform Configuration", "showEmbedCodes": "Show Embed Codes", "hideEmbedCodes": "Hide Embed Codes", "schoolWebsite": "School Website", "platformIntegration": "{{platform}} integration", "instagramUsername": "Instagram Username", "twitterUsername": "Twitter Username", "usernamePlaceholder": "@yourschool", "settingsUpdatedSuccessfully": "Social media integration settings have been updated successfully.", "failedToLoadSettings": "Failed to load social media settings", "failedToSaveSettings": "Failed to save social media settings", "websiteUrl": "Website URL", "rssFeedUrl": "RSS Feed URL (Optional)", "youtubeChannelUrl": "YouTube Channel URL", "youtubeChannelId": "YouTube Channel ID", "facebookPageUrl": "Facebook Page URL", "embedCode": "Embed Code (For Real Posts)", "toShowRealPosts": "To show real posts:", "embedInstructions": {"facebook": {"step1": "Go to Facebook Page Plugin", "step2": "Enter your Facebook page URL", "step3": "Configure settings (show timeline, width, height)", "step4": "Copy the generated HTML code and paste it here"}, "twitter": {"step1": "Go to Twitter Publish", "step2": "Enter your Twitter profile URL", "step3": "Choose \"Embedded Timeline\"", "step4": "Copy the generated HTML code and paste it here"}, "instagram": {"step1": "Go to an Instagram post you want to embed", "step2": "Click the three dots (...) menu", "step3": "Select \"Embed\"", "step4": "Copy the embed code and paste it here", "note": "Note: Instagram doesn't provide feed embeds, only individual posts"}, "youtube": {"title": "Shows 5 Most Recent Videos:", "step1": "Go to YouTube Studio → Settings → Channel → Advanced", "step2": "Copy your Channel ID (starts with \"UC\")", "step3": "Paste it in the \"YouTube Channel ID\" field above", "step4": "We create responsive video playlist embeds!", "note": "Students can watch your latest videos directly!"}, "default": "Follow the platform's embed instructions"}, "youtubeHelp": {"title": "Shows 6 Most Recent Videos:", "enterChannelId": "Enter your YouTube Channel ID (starts with \"UC\")", "gridLayout": "Creates beautiful grid layout with 6 separate videos", "individualVideos": "Students can watch each video individually", "findChannelId": "Find your Channel ID: YouTube Studio → Settings → Channel → Advanced", "perfect": "Perfect: Each video shows with index parameter for proper display"}, "embedPlaceholders": {"facebook": "Paste Facebook Page Plugin embed code here...", "twitter": "<a class=\"twitter-timeline\" href=\"https://twitter.com/yourusername?ref_src=twsrc%5Etfw\">Tweets by yourusername</a> <script async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"></script>", "instagram": "Instagram embed code from Instagram's embed feature", "youtube": "✨ NEW: Just use the \"YouTube Channel URL\" field above! No embed code needed. For custom embeds, paste iframe code here.", "default": "Paste embed code from the platform..."}, "guide": {"title": "How to Show Real Social Media Posts", "subtitle": "Follow these guides to display actual posts from your school's social media accounts", "steps": "Steps:", "platformDescriptions": {"facebook": "Show your Facebook page's timeline with recent posts", "twitter": "Embed your Twitter timeline with latest tweets", "instagram": "Embed individual Instagram posts (no feed available)", "youtube": "✨ Shows 6 most recent videos in beautiful grid layout!"}, "difficulty": {"easy": "Easy", "medium": "Medium", "superEasy": "Super Easy"}, "badges": {"realTime": "Real-time", "individualPosts": "Individual Posts"}, "buttons": {"openTool": "Open {{platform}} Tool", "copySampleCode": "Copy Sample Code", "viewSampleCode": "View Sample Code", "copied": "Copied!"}, "importantNotes": "Important Notes", "notes": {"facebookTwitter": "Facebook & Twitter: Provide real-time feeds that automatically update with new posts", "instagram": "Instagram: Only allows embedding individual posts, not full feeds due to API restrictions", "youtube": "YouTube: Can embed channel feeds or individual videos", "demoMode": "Demo Mode: When no embed codes are provided, the system shows sample posts for demonstration"}}, "copied": "Copied!", "codeCopied": "{{label}} sample code copied to clipboard"}, "excuseCleanup": {"title": "Excuse Expiry Cleanup", "description": "Automatically clean up expired excuses and revert attendance status", "status": "Status", "running": "Running", "stopped": "Stopped", "expiredExcuses": "Expired Excuses", "lastCleanup": "Last Cleanup", "expiredExcusesFound": "Expired Excuses Found", "expiredExcusesFoundMessage": "There are {{count}} expired excuses that need cleanup. Run the cleanup process to revert attendance status and remove expired records.", "enableAutomaticCleanup": "Enable Automatic Cleanup", "enableAutomaticCleanupDescription": "Automatically clean up expired excuses and revert attendance status", "checkInterval": "Check Interval (minutes)", "checkIntervalDescription": "How often to check for expired excuses (5-1440 minutes)", "notifyStudents": "Notify Students", "notifyStudentsDescription": "Send notifications to students when their excuses expire", "saveSettings": "Save Settings", "runCleanupNow": "Run Cleanup Now", "checkExpired": "Check Expired", "checking": "Checking", "howItWorks": "How It Works", "automaticCleanup": "Automatic Cleanup", "automaticCleanupDescription": "Checks for expired excuses at the specified interval", "statusReversion": "Status Reversion", "statusReversionDescription": "Changes attendance status from 'excused' back to 'absent'", "recordCleanup": "Record Cleanup", "recordCleanupDescription": "Removes expired approved excuses and old rejected excuses (7+ days)", "studentNotifications": "Student Notifications", "studentNotificationsDescription": "Optionally notifies students when their excuses expire", "settingsSaved": "Settings Saved", "settingsSavedMessage": "Excuse cleanup settings have been updated successfully", "cleanupCompleted": "Cleanup Completed", "cleanupCompletedMessage": "Successfully processed {{count}} expired excuses", "cleanupFailed": "Cleanup Failed", "cleanupFailedMessage": "Failed to run excuse cleanup", "checkComplete": "Check Complete", "checkCompleteMessage": "Found {{count}} expired excuses", "errorLoadingSettings": "Failed to load excuse cleanup settings", "errorSavingSettings": "Failed to save excuse cleanup settings", "errorCheckingExpired": "Failed to check for expired excuses"}, "schoolSettings": {"title": "School Settings", "description": "Customize your school settings and appearance", "general": "General", "branding": "Branding", "messages": "Messages", "notifications": "Notifications", "security": "Security", "carousel": "Carousel", "socialMedia": "Social Media", "carouselManagement": "Carousel Management", "carouselManagementDescription": "Upload and manage carousel content that will be displayed on student and teacher dashboards.", "noCarouselContent": "No carousel content yet", "addCarouselContent": "Add images to display in the dashboard carousel.", "addContent": "Add Content", "carouselSettings": {"title": "Title", "description": "Manage carousel content for student and teacher dashboards", "items": "Carousel Items", "addItem": "Add Item", "editItem": "Edit Carousel Item", "noItems": "No carousel items yet. Add your first item to get started.", "order": "Order", "image": "Image", "audience": "Audience", "status": "Status", "active": "Active", "inactive": "Inactive", "itemTitle": "Title", "itemDescription": "Description", "itemTitlePlaceholder": "Enter a title for this item", "itemDescriptionPlaceholder": "Enter a description (optional)", "imageUpload": "Image Upload", "targetAudience": "Target Audience", "students": "Students", "teachers": "Teachers", "admins": "School Admins", "adminAudienceHint": "Show this carousel item on your own admin dashboard", "activeItem": "Active", "itemFormDescription": "Fill in the details for this carousel item.", "titleRequired": "Title is required", "mediaRequired": "Please select an image", "audienceRequired": "Please select at least one audience", "itemUpdated": "Carousel item updated successfully", "itemAdded": "Carousel item added successfully", "confirmDelete": "Are you sure you want to delete this item?", "itemDeleted": "Carousel item deleted successfully", "invalidFileType": "Please select an image file.", "errorLoading": "Failed to load carousel items", "errorSaving": "Failed to save carousel item", "errorDeleting": "Failed to delete carousel item", "errorToggling": "Failed to update carousel item status", "errorReordering": "Failed to reorder carousel items", "itemActivated": "Carousel item activated", "itemDeactivated": "Carousel item deactivated", "fileTooLarge": "Image file is too large. Maximum size is 5MB.", "errorReadingFile": "Error reading image file. Please try again."}, "schoolName": "School Name", "enterSchoolName": "Enter school name", "schoolLogo": "School Logo", "uploadLogo": "Upload Logo", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "primaryElementsWillUseThisColor": "Primary elements will use this color", "secondaryElementsWillUseThisColor": "Secondary elements will use this color", "colorPreview": "Color Preview", "colorsArePreviewedInRealTime": "Colors are previewed in real-time. Click \"Save Branding\" to permanently apply these changes.", "primaryButton": "Primary Button", "secondaryButton": "Secondary Button", "noLogoUploaded": "No logo uploaded", "changeLogo": "Change Logo", "chooseLogo": "<PERSON><PERSON>", "remove": "Remove", "newLogoSelected": "New Logo Selected", "confirmRemoveLogo": "Are you sure you want to remove the school logo? This action cannot be undone.", "logoRemoved": "Logo Removed", "logoRemovedSuccess": "Your school logo has been removed successfully.", "logoRemoveError": "There was an error removing the logo. Please try again.", "uploadFailed": "Upload Failed", "uploadFailedDescription": "There was an error uploading your logo. Please try again.", "clickSaveBrandingToUpload": "Click \"Save Branding\" to upload and apply this logo.", "yourSchoolColorsAndLogo": "Your school colors and logo will be used throughout the application to create a consistent brand experience.", "savedSuccessfully": "Saved successfully!", "saving": "Saving...", "uploading": "Uploading...", "saved": "Saved", "saveBranding": "Save Branding", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "enableEmailNotificationsForStudentsAndParents": "Enable email notifications for students and parents", "enableSmsNotificationsForStudentsAndParents": "Enable SMS notifications for students and parents", "notificationPreferencesDescription": "These settings control the default notification preferences for your school. Individual users can override these settings in their profiles.", "saveNotificationSettings": "Save Notification Settings", "systemAdminOverride": "System Admin Override", "settingsOverriddenBySystemAdmin": "Some settings have been overridden by the system administrator and cannot be changed.", "settingControlledBySystemAdmin": "This setting is controlled by the system administrator", "overridden": "Overridden", "schoolInvitationCode": "School Invitation Code", "missingInvitationCode": "Missing Invitation Code", "missingInvitationCodeDescription": "Your school doesn't have an invitation code. Students cannot register without this. Please use the button below to generate an invitation code.", "noInvitationCodeGeneratedYet": "No invitation code generated yet", "show": "Show", "hide": "<PERSON>de", "currentInvitationCode": "Current Invitation Code:", "copiedToClipboard": "Copied to clipboard", "invitationCodeCopiedToClipboard": "Invitation code copied to clipboard", "invitationCodeDescription": "The invitation code is required for new users to join your school. This code will remain valid until you regenerate it. You can regenerate it anytime if you need to revoke access.", "regenerating": "Regenerating...", "generateInvitationCode": "Generate Invitation Code", "regenerateInvitationCode": "Regenerate Invitation Code", "studentDashboardMessage": "Student Dashboard Message", "studentMessagePlaceholder": "Enter a message to display on student dashboards", "studentMessageDescription": "This message will be displayed at the top of the dashboard for all students in your school.", "teacherDashboardMessage": "Teacher Dashboard Message", "teacherMessagePlaceholder": "Enter a message to display on teacher dashboards", "teacherMessageDescription": "This message will be displayed at the top of the dashboard for all teachers in your school.", "dashboardMessages": "Dashboard Messages", "dashboardMessagesInfo": "Use dashboard messages to communicate important information to students and teachers. These messages will appear at the top of their respective dashboards.", "emptyMessageInfo": "Leave a field empty to remove the message from that user type's dashboard.", "brandingSettingsSaved": "Branding Settings Saved", "brandingSettingsSavedMessage": "Your school branding settings have been successfully updated.", "dashboardMessagesSaved": "Dashboard Messages Saved", "dashboardMessagesSavedMessage": "Your dashboard messages have been successfully updated.", "saveMessages": "Save Messages"}, "profile": {"adminProfile": "Admin Profile", "completeYourProfile": "Complete Your Admin Profile", "completeProfile": "Complete Profile", "updateProfileInfo": "Update your profile information", "viewAndManageProfile": "View and manage your admin profile", "fillRequiredFields": "Please fill in all required fields to complete your profile setup.", "profileSetupComplete": "Profile setup complete", "profileSetupMessage": "Your profile has been successfully set up. You can now access all features.", "position": "Position", "adminId": "Admin ID", "enterAdminId": "Enter your admin ID", "enterPosition": "Enter your position", "enterSchoolName": "Enter your school name", "school": "School", "enterFullName": "Enter your full name", "schoolName": "School Name", "incompleteProfile": "Your admin profile is incomplete. Please complete your profile to access all features.", "goToProfileTab": "Profile tab"}, "alerts": {"title": "<PERSON><PERSON>erts Dashboard", "description": "Monitor and manage attendance alerts across all rooms", "pending": "Pending", "resolved": "Resolved", "dismissed": "Dismissed", "noAlerts": "No {{status}} alerts", "student": "Student", "room": "Room", "teacher": "Teacher", "time": "Time", "distance": "Distance", "actions": "Actions", "approve": "Approve", "reject": "Reject", "dismiss": "<PERSON><PERSON><PERSON>", "alertApproved": "<PERSON><PERSON> App<PERSON>", "alertRejected": "<PERSON><PERSON> Rejected", "alertDismissed": "<PERSON><PERSON>", "loadingAlerts": "Loading alerts...", "success": "Success", "error": "Error", "attemptedToMarkAttendance": "Attempted to mark attendance in", "distanceFromRoom": "Distance from room", "meters": "meters", "viewStudentLocation": "View Student Location", "viewRoomLocation": "View Room Location", "fetchFailed": "Failed to fetch alerts", "markedAs": "<PERSON><PERSON> marked as {{action}}", "updateFailed": "Failed to update alert status"}, "qrGenerator": {"title": "QR Code Generator", "description": "Generate and manage QR codes for attendance", "selectBlock": "Select Block", "selectRoom": "Select Room", "chooseBlock": "Choose a block...", "chooseRoom": "Choose a room...", "block": "Block", "floor": "Floor", "regenerateQRCode": "Regenerate QR Code", "qrCodeData": "QR Code Data", "qrCodeDataJSON": "QR Code Data (JSON)", "roomCapacity": "Room Capacity", "students": "students", "qrCodeSecurityFeatures": "QR Code Security Features", "timeLimitedExpiration": "Time-limited expiration", "minute": " minute", "minutePlural": "s", "fullUUIDPreservation": "Full UUID preservation (no truncation)", "completeCryptographicSignatures": "Complete cryptographic signatures", "roomAndBlockValidation": "Room and block validation", "sessionBasedTracking": "Session-based tracking", "encryptedRoomIdentifier": "Encrypted room identifier", "timestampsToPrevent": "Timestamps to prevent replay attacks", "qrChallengeRotates": "QR Challenge rotates every {{interval}}s, valid for {{total}}s total", "securityPriority": "Security Priority: Full format used - no data truncation", "roomInformation": "Room Information", "roomID": "Room ID", "capacity": "Capacity", "sessionID": "Session ID", "liveAttendance": "Live Attendance", "noAttendanceRecorded": "No attendance recorded yet", "qrCodesExpireAfter": "QR codes automatically expire after {{minutes}} minutes for security", "selectBlockAndRoom": "Select a block and room to generate QR codes", "expiresIn": "Expires in:"}, "fraudDetection": {"title": "Enhanced <PERSON>", "description": "Advanced analytics and monitoring of potential attendance fraud", "overview": "Overview", "alerts": "alerts", "location": "Location", "devices": "Devices", "totalAlerts": "Total Alerts", "pending": "pending", "resolved": "resolved", "investigating": "Investigating", "highRisk": "High Risk", "mediumRisk": "Medium Risk", "lowRisk": "Low Risk", "unknownStudent": "Unknown Student", "unknown": "Unknown", "ofTotalAlerts": "of total alerts", "locationFraud": "Location Fraud", "deviceFraud": "<PERSON><PERSON>", "fraudDistribution": "Fraud Distribution", "locationBased": "Location-based", "deviceBased": "Device-based", "timeBased": "Time-based", "patternBased": "Pattern-based", "riskAssessment": "Risk Assessment", "unread": "Unread", "fraudDistributionTitle": "Fraud Distribution", "searchAlerts": "Search alerts...", "filters": "Filters", "filterAlerts": "<PERSON><PERSON>", "severity": "Severity", "allSeverities": "All severities", "type": "Type", "allTypes": "All types", "status": "Status", "allStatuses": "All statuses", "resetFilters": "Reset filters", "showing": "Showing", "of": "of", "noFraudAlertsDetected": "No fraud alerts detected yet", "noAlertsMatchFilters": "No alerts match your current filters", "actions": "Actions", "markAsInvestigating": "<PERSON> as Investigating", "markAsResolved": "<PERSON> as Resolved", "deleteAlert": "Delete Alert", "room": "Room", "distance": "Distance", "exceededBy": "Exceeded by", "deviceChange": "Device change", "viewLocation": "View Location", "getDirections": "Get Directions", "locationBasedFraudDetection": "Location-based Fraud Detection", "monitorAttendanceVerification": "Monitor attendance verification attempts from suspicious locations", "distanceViolations": "Distance Violations", "studentsAttemptingOutsideRadius": "Students attempting to mark attendance from outside allowed radius", "geofenceViolations": "Geofence Violations", "studentsOutsideCampus": "Students outside campus boundaries", "locationSpoofing": "Location Spoofing", "detectedGpsSpoofing": "Detected GPS/location spoofing attempts", "locationFraudPreventionTips": "Location Fraud Prevention Tips", "adjustRoomGeofenceRadius": "Adjust Room Geofence Radius", "configureGeofenceRadius": "Configure appropriate geofence radius for each room based on building size and campus layout. Current average radius: 50m", "enableMultiFactorVerification": "Enable Multi-factor Verification", "requireBothLocation": "Require both location verification and biometric/PIN authentication for high-risk areas or during exam periods.", "implementLocationConsistency": "Implement Location Consistency Checks", "flagSuspiciousChanges": "Flag suspicious rapid location changes that would be physically impossible.", "deviceBasedFraudDetection": "Device-based Fraud Detection", "monitorSuspiciousDevicePatterns": "Monitor suspicious device patterns and inconsistencies", "deviceInconsistencies": "Device Inconsistencies", "studentsUsingDifferentDevices": "Students using different devices within short timeframes", "concurrentSessions": "Concurrent Sessions", "multipleAttendanceRecords": "Multiple attendance records from different locations", "emulatorDetection": "Emulator Detection", "detectedEmulatorUsage": "Detected emulator or virtual device usage", "deviceFraudPreventionTips": "<PERSON><PERSON> Tips", "implementDeviceFingerprinting": "Implement Device Fingerprinting", "trackUniqueDeviceCharacteristics": "Track unique device characteristics to detect when students share credentials.", "enforceDeviceRegistration": "Enforce Device Registration", "requireStudentsToRegister": "Require students to register their primary device and limit attendance marking to registered devices.", "detectEmulatorsAndRooted": "Detect Emulators and Rooted Devices", "identifyAndBlockAttendance": "Identify and block attendance verification from emulators, rooted, or jailbroken devices.", "newFraudAlert": "<PERSON> <PERSON><PERSON>", "newFraudAttemptDetected": "A new potential fraud attempt has been detected", "statusUpdated": "Status Updated", "alertMarkedAsInvestigating": "<PERSON><PERSON> marked as investigating", "error": "Error", "failedToUpdateStatus": "Failed to update alert status", "alertMarkedAsResolved": "<PERSON><PERSON> marked as resolved", "failedToResolveAlert": "Failed to resolve alert", "success": "Success", "alertDeletedSuccessfully": "<PERSON><PERSON> deleted successfully", "failedToDeleteAlert": "Failed to delete alert"}, "excusesManagement": {"title": "Student Excuses Management", "description": "Review and manage student absence excuse requests", "management": "Management", "settings": "Settings", "submissionSettings": "Submission Settings", "cleanupSettings": "Cleanup Settings", "total": "Total", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "searchPlaceholder": "Search by student name or reason...", "allTeachers": "All Teachers", "allStatuses": "All Statuses", "export": "Export", "all": "All", "noExcusesFound": "No excuses found", "noExcuseRequestsMatching": "There are no excuse requests matching your filters", "noPendingExcuses": "No pending excuses", "noPendingExcuseRequests": "There are no pending excuse requests to review", "noApprovedExcuses": "No approved excuses", "noApprovedExcuseRequests": "There are no approved excuse requests", "noRejectedExcuses": "No rejected excuses", "noRejectedExcuseRequests": "There are no rejected excuse requests", "excuseRequestDetails": "Excuse Request Details", "reviewStudentExcuse": "Review the student's excuse request", "student": "Student", "room": "Room", "date": "Date", "duration": "Duration", "time": "Time", "reason": "Reason", "status": "Status", "adminNotes": "Admin Notes", "error": "Error", "addNotesPlaceholder": "Add notes about this excuse (optional)...", "teacherNotes": "Teacher Notes", "by": "by", "reject": "Reject", "approve": "Approve", "confirmDeletion": "Confirm Deletion", "confirmDeleteExcuse": "Are you sure you want to delete this excuse request? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "excuseDeleted": "Excuse Deleted", "excuseDeletedSuccess": "The excuse request has been deleted successfully.", "failedToDelete": "Failed to delete the excuse. Please try again.", "exportComplete": "Export Complete", "excusesExported": "{count} excuses exported to CSV.", "unknownStudent": "Unknown Student", "unknownRoom": "Unknown", "filterByTeacher": "Filter by teacher", "filterByStatus": "Filter by status", "teacher": "Teacher", "notes": "Notes", "notApplicable": "N/A", "unknownTeacher": "Unknown Teacher", "excusesReport": "Excuses Report", "comprehensiveReport": "Comprehensive Student Excuse Management Report", "generatedOn": "Generated on", "totalExcuses": "Total Excuses", "dateRange": "Date Range", "reviewedBy": "Reviewed By", "notSpecified": "Not Specified", "exportAsHTML": "Export as HTML", "exportAsPDF": "Export as PDF", "exportAsCSV": "Export as CSV", "exportSuccess": "Successfully exported {{count}} excuses to {{format}} format", "reportContains": "This report contains {{count}} excuse records as of {{date}}"}, "parentNotifications": {"title": "Parent Notifications", "description": "Manage parent contact information and notification settings", "aboutParentNotifications": "About Parent Notifications", "aboutDescription": "This feature allows you to manage parent contact information and notification preferences. Parents will be automatically notified when their children submit excuse requests or when the status of those requests changes. You can enable or disable notifications for specific students or individual parent contacts.", "parentContacts": "Parent Contacts", "messageTemplates": "Message Templates", "emailSmsServices": "Email & SMS Services", "notificationServiceSettings": "Notification Service Settings", "configureEmailSms": "Configure email and SMS services for parent notifications", "emailService": "Email Service", "smsService": "SMS Service", "sendgridApiKey": "SendGrid API Key", "enterSendgridApiKey": "Enter your SendGrid API key", "fromEmailAddress": "From Email Address", "enterFromEmail": "<EMAIL>", "emailVerificationRequired": "This email address must be verified in your SendGrid account", "emailNotificationsEnabled": "Email notifications enabled", "emailNotificationsDisabled": "Email notifications disabled", "saveEmailConfiguration": "Save Email Configuration", "testEmailService": "Test Email Service", "enterTestEmailAddress": "Enter test email address", "test": "Test", "twilioAccountSid": "<PERSON><PERSON><PERSON> Account SID", "enterTwilioAccountSid": "Enter your <PERSON><PERSON><PERSON> Account SID", "twilioAuthToken": "<PERSON><PERSON><PERSON>", "enterTwilioAuthToken": "Enter your <PERSON><PERSON><PERSON>", "twilioPhoneNumber": "Twilio Phone Number", "enterTwilioPhoneNumber": "Enter your Twilio phone number with country code", "smsNotificationsEnabled": "SMS notifications enabled", "smsNotificationsDisabled": "SMS notifications disabled", "saveSmsConfiguration": "Save SMS Configuration", "testSmsService": "Test SMS Service", "enterTestPhoneNumber": "Enter test phone number with country code", "apiKeysSecurelyStored": "API keys and tokens are stored securely in the database", "students": "Students", "notificationHistory": "Notification History", "notificationHistoryTitle": "Notification History", "refresh": "Refresh", "refreshing": "Refreshing...", "searchByStudentOrRecipient": "Search by student or recipient...", "status": "Status", "allStatuses": "All Statuses", "successful": "Successful", "failed": "Failed", "type": "Type", "allTypes": "All Types", "email": "Email", "sms": "SMS", "noNotificationLogs": "No Notification Logs", "noLogsMatchCriteria": "No logs match your search criteria. Try adjusting your filters.", "noLogsRecorded": "No notification logs have been recorded yet.", "dateAndTime": "Date & Time", "student": "Student", "recipient": "Recipient", "sent": "<PERSON><PERSON>", "failedStatus": "Failed", "unknownStudent": "Unknown Student", "errorFetchingLogs": "Failed to fetch notification logs: {message}", "studentName": "Student Name", "notifications": "Notifications", "actions": "Actions", "searchPlaceholder": "Search students", "noStudentsFound": "No students found", "manageContacts": "Manage Contacts", "noStudentSelected": "No Student Selected", "selectStudentMessage": "Please select a student from the Students tab to manage their parent contacts", "backToStudents": "Back to Students", "parentContactsFor": "Parent Contacts for", "addParentContact": "Add Parent Contact", "addContactInfo": "Add contact information for a parent or guardian", "parentGuardianName": "Parent/Guardian Name", "fullName": "Full name", "emailAddress": "Email Address", "emailPlaceholder": "<EMAIL>", "phoneNumber": "Phone Number", "enterPhoneNumber": "Enter phone number", "notificationMethod": "Notification Method", "selectNotificationMethod": "Select notification method", "emailOnly": "Email Only", "smsOnly": "SMS Only", "bothEmailSms": "Both Email & SMS", "noNotifications": "No notifications", "enableNotifications": "Enable Notifications", "cancel": "Cancel", "addContact": "Add Contact", "editParentContact": "Edit Parent Contact", "updateContactInfo": "Update contact information for this parent or guardian", "saveChanges": "Save Changes", "noParentContacts": "No Parent Contacts", "noContactsAdded": "No parent contacts have been added for this student yet", "addFirstContact": "Add First Contact", "parentName": "Parent Name", "contactInfo": "Contact Info", "enabled": "Enabled", "disabled": "Disabled", "notificationsEnabled": "Notifications Enabled", "notificationsDisabled": "Notifications Disabled", "notificationsEnabledMessage": "Parent notifications for this student have been enabled.", "notificationsDisabledMessage": "Parent notifications for this student have been disabled.", "error": "Error", "selectStudentError": "Please select a student first.", "failedToLoadStudents": "Failed to load students. Please try again.", "contactAdded": "Contact Added", "contactAddedSuccess": "Parent contact information has been added successfully.", "contactUpdated": "Contact Updated", "contactUpdatedSuccess": "Parent contact information has been updated successfully.", "contactDeleted": "Contact Deleted", "contactDeletedSuccess": "Parent contact has been removed successfully.", "failedToDeleteContact": "Failed to delete parent contact: {message}", "testNotification": "Test Notification", "sendTestNotification": "Send Test Notification", "sendTestNotificationDescription": "Send a test notification to verify parent contact information", "testNotificationSent": "Test Notification Sent", "testNotificationSentDescription": "Successfully sent test notification to parent(s) of {{studentName}}", "notificationFailed": "Notification Failed", "failedToSendTestNotification": "Failed to send test notification: {{error}}", "testNotificationSubject": "Test Notification for {{name}}'s Parent", "newAbsenceRequestSubject": "New Absence Request from {{name}}", "absenceRequestApprovedSubject": "Absence Request Approved for {{name}}", "absenceRequestRejectedSubject": "Absence Request Rejected for {{name}}", "notificationSubject": "Notification for {{name}}'s Parent", "testNotificationMessage": "This is a test notification for {{name}}'s parent. If you received this message, the notification system is working correctly.", "newAbsenceRequestMessage": "Your child, {{name}}, has submitted a request for absence from school. This request is pending approval from school administration.", "absenceRequestApprovedMessage": "Your child's ({{name}}) absence request has been APPROVED by the school administration.", "absenceRequestRejectedMessage": "Your child's ({{name}}) absence request has been REJECTED by the school administration. Please contact the school for more information.", "notificationMessage": "This is a notification regarding your child, {{name}}.", "noParentContactsDescription": "There are no parent contacts configured for this student. Please add at least one parent contact before sending test notifications.", "notificationType": "Notification Type", "selectNotificationType": "Select notification type", "testMessage": "Test Message", "newExcuseRequest": "New Excuse Request", "excuseApproved": "Excuse Approved", "excuseRejected": "Excuse Rejected", "customMessageOptional": "Custom Message (Optional)", "customMessagePlaceholder": "Enter a custom message or leave blank to use the default template", "leaveBlankForDefault": "Leave blank to use the default template for the selected notification type", "notificationRecipients": "Notification Recipients", "testWillBeSentTo": "This test will be sent to the following contacts:", "viaEmail": "Email ({{email}})", "viaSMS": "SMS ({{phone}})", "viaEmailAndSMS": "Email & SMS ({{email}}, {{phone}})", "sending": "Sending...", "sampleExcuseReason": "Sample excuse reason"}, "automaticQR": {"title": "Automatic QR Generation", "serviceStatus": "Service Status", "initializing": "Initializing...", "error": "Error", "active": "Active", "inactive": "Inactive", "updated": "Updated", "startService": "Start Service", "generateNow": "Generate Now", "refreshStatus": "Refresh Status", "stopService": "Stop Service", "activeSessions": "Active Sessions", "totalRooms": "Total Rooms", "currentSchool": "Current School", "yourSchoolSession": "Your School Session", "status": "Status", "rooms": "Rooms", "startTime": "Start Time", "endTime": "End Time", "school": "School", "roomsCount": "{{count}} rooms", "howItWorks": "How It Works", "currentConfiguration": "Current Configuration", "qrExpiryTime": "QR Expiry Time", "minutes": "minutes", "generationInterval": "Generation Interval", "seconds": "seconds", "transitionTiming": "Transition Timing", "oneSecondBeforeExpiry": "1 second before expiry", "source": "Source", "envConfiguration": ".env configuration", "automaticStart": "Automatic Start", "automaticStartDescription": "QR generation begins automatically when attendance time starts", "smartTiming": "Smart Timing", "smartTimingDescription": "New QR generated every {{interval}} seconds, transitions at 1 second remaining", "configurableExpiry": "Configurable Expiry", "configurableExpiryDescription": "QR codes expire after {{minutes}} minutes (set in .env file)", "allRooms": "All Rooms", "allRoomsDescription": "QR codes are generated for all rooms with tablets in your school", "automaticStop": "Automatic Stop", "automaticStopDescription": "Generation stops automatically when attendance time ends", "serviceStarted": "Service Started", "serviceStartedDescription": "Automatic QR generation service has been started successfully", "serviceStopped": "Service Stopped", "serviceStoppedDescription": "Automatic QR generation service has been stopped successfully", "generationTriggered": "Generation Triggered", "generationTriggeredDescription": "QR codes have been generated manually for your school", "manualTriggerFailed": "Manual trigger failed", "errorStoppingService": "Error stopping service", "noSchoolId": "No school ID found for current user"}, "tabletSetup": {"title": "Tablet Setup Helper", "generateURLsForBlock": "Generate URLs for entire block", "generateURLForRoom": "Generate URL for single room", "selectBlock": "Select a block", "selectRoom": "Select a room", "block": "Block", "floor": "Floor", "generate": "Generate", "setupInstructions": "Setup Instructions", "instruction1": "Generate URLs for the rooms where you want to place tablets", "instruction2": "Open each URL on the corresponding room's tablet", "instruction3": "The tablet will automatically configure itself for that room", "instruction4": "QR codes will appear automatically when generated from admin dashboard", "generatedTabletURLs": "Generated Tablet URLs", "room": "Room", "tabletURL": "Tablet URL", "copyURL": "Copy URL", "testTablet": "Test Tablet", "bulkActions": "Bulk Actions", "copyAllURLs": "Copy All URLs", "openAllTablets": "Open All Tablets", "setupQRCode": "Setup QR Code", "scanQRCodeInstruction": "Scan this QR code with the tablet to automatically open the URL", "copied": "Copied!", "urlCopied": "URL copied to clipboard", "failedToCopy": "Failed to copy URL"}, "schoolContext": {"yourSchoolInformation": "Your school information", "address": "Address", "phone": "Phone", "email": "Email", "active": "Active", "totalUsers": "Total Users", "students": "Students", "teachers": "Teachers", "administrators": "Administrators"}, "auditLogs": {"title": "<PERSON><PERSON>", "description": "View audit logs for", "searchPlaceholder": "Search logs...", "filterByAction": "Filter by action", "filterByEntity": "Filter by entity", "allActions": "All Actions", "allEntities": "All Entities", "noAuditLogs": "No Audit Logs", "noAuditLogsRecorded": "No audit logs have been recorded yet.", "noLogsMatchCriteria": "No logs match your search criteria. Try adjusting your filters.", "dateTime": "Date & Time", "action": "Action", "entity": "Entity", "entityType": "Entity Type", "entityId": "Entity ID", "user": "User", "school": "School", "ipAddress": "IP Address", "details": "Details", "view": "View", "showing": "Showing {{shown}} of {{total}} logs", "pagination": "Page {{current}} of {{total}}", "auditLogDetails": "Audit Log Details", "detailedInformation": "Detailed information about this audit log entry", "userAgent": "User Agent", "notAvailable": "N/A", "system": "System", "noDetailsAvailable": "No details available", "actions": {"user_login": "User Login", "user_logout": "User <PERSON>", "user_created": "User Created", "user_updated": "User Updated", "user_deleted": "User Deleted", "school_created": "School Created", "school_updated": "School Updated", "school_deleted": "School Deleted", "school_settings_updated": "School Settings Updated", "invitation_code_generated": "Invitation Code Generated", "invitation_code_used": "Invitation Code Used", "security_alert": "Security Alert", "permission_changed": "Permission Changed", "data_exported": "Data Exported", "data_imported": "Data Imported", "attendance_recorded": "Attendance Recorded", "attendance_updated": "Attendance Updated", "attendance_deleted": "Attendance Deleted", "delete": "Delete", "create": "Create", "update": "Update", "read": "Read"}, "entities": {"user": "User", "school": "School", "student": "Student", "teacher": "Teacher", "admin": "Admin", "attendance": "Attendance", "room": "Room", "block": "Block", "excuse": "Excuse", "notification": "Notification", "settings": "Settings", "system_school_settings_override": "System School Settings Override", "invitation_code": "Invitation Code", "attendance_record": "Attendance Record"}}, "biometricDeletionRequests": {"title": "Biometric Deletion Requests", "description": "Manage student requests to delete their biometric credentials", "loading": "Loading...", "pendingRequests": "Pending Requests", "processedRequests": "Processed Requests", "noPendingRequests": "No Pending Requests", "noPendingRequestsMessage": "All biometric deletion requests have been processed.", "student": "Student", "location": "Location", "requestDate": "Request Date", "processedDate": "Processed Date", "status": "Status", "actions": "Actions", "view": "View", "approve": "Approve", "reject": "Reject", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "requestDetails": "Request Details", "requestDetailsDescription": "View detailed information about this biometric deletion request", "studentName": "Student Name", "studentEmail": "Student Email", "studentNumber": "Student Number", "requestReason": "Request Reason", "adminNotes": "Admin Notes", "approveRequest": "Approve Deletion Request", "approveRequestDescription": "This will permanently delete the student's biometric credentials. This action cannot be undone.", "rejectRequest": "Reject Deletion Request", "rejectRequestDescription": "The student's biometric credentials will remain active.", "adminNotesPlaceholder": "Optional notes about this decision...", "rejectionReasonPlaceholder": "Reason for rejection (optional)...", "approving": "Approving...", "rejecting": "Rejecting...", "requestApproved": "Request Approved", "credentialsDeleted": "The student's biometric credentials have been permanently deleted.", "requestRejected": "Request Rejected", "studentNotified": "The student has been notified of the decision.", "errorLoading": "Error Loading Requests", "errorLoadingMessage": "Failed to load biometric deletion requests. Please try again.", "errorApproving": "Error Approving Request", "errorApprovingMessage": "Failed to approve the deletion request. Please try again.", "errorRejecting": "Error Rejecting Request", "errorRejectingMessage": "Failed to reject the deletion request. Please try again.", "clearProcessed": "Clear Processed", "clear": "Clear", "clearing": "Clearing...", "clearAll": "Clear All", "clearProcessedRequests": "Clear Processed Requests", "clearProcessedRequestsDescription": "Are you sure you want to permanently delete {{count}} processed requests? This action cannot be undone.", "processedRequestsCleared": "Processed Requests Cleared", "processedRequestsClearedMessage": "{{count}} processed requests have been successfully cleared.", "requestCleared": "Request Cleared", "requestClearedMessage": "Request has been successfully cleared.", "errorClearing": "Error Clearing", "errorClearingMessage": "An error occurred while clearing requests. Please try again."}}, "pwa": {"installTitle": "Install App", "installDescription": "Install our app for the best experience with offline access and push notifications.", "installCompactDescription": "Get faster access & offline features", "installButton": "Install App", "install": "Install", "installing": "Installing...", "installSuccess": "App Installed Successfully!", "installSuccessDescription": "You can now access the app from your home screen.", "installFailed": "Installation failed. Please try again.", "manualInstall": "Manual Installation", "updateTitle": "Update Available", "updateDescription": "A new version of the app is available with improvements and bug fixes.", "updateCompactDescription": "New features & improvements ready", "updateButton": "Update Now", "update": "Update", "updateLater": "Later", "later": "Later", "updating": "Updating...", "updateApplied": "Update applied successfully!", "updateFailed": "Update failed. Please try again.", "remindLater": "We'll remind you later", "remindLaterDescription": "Install prompt will appear again tomorrow", "offline": "Offline", "online": "Online", "benefit1": "Works offline - access your data anytime", "benefit2": "Faster loading and better performance", "benefit3": "Push notifications for important updates"}, "aiAssistant": {"title": "ATS Assistant", "subtitle": "Online • Ready to help", "typing": "Typing...", "thinking": "AI is thinking", "analyzing": "Analyzing your request...", "listening": "Listening...", "voiceInputActive": "Listening...", "speakMessage": "Listen to message", "stopSpeaking": "Stop speaking", "floatingButtonText": "AI Assistant - Ask me anything!", "defaultUser": "User", "quickStart": "Try asking me:", "greeting": "Hi, {{userName}}! 👋 I'm your intelligent ATS Assistant. I'm here to help you with whatever you need. Feel free to ask me anything about the system!", "introduction": {"greeting": "Hello {{userName}}! 👋 I'm your intelligent ATS Assistant.", "roleSpecific": {"admin": "As an administrator, I can help you manage the entire attendance system, user accounts, and system settings.", "teacher": "As a teacher, I can assist you with class management, attendance tracking, and student monitoring.", "student": "As a student, I can help you with attendance check-ins, QR scanning, and understanding your attendance records."}, "pageContext": {"dashboard": "I see you're on the dashboard. I can help you understand your overview and navigate the system.", "attendance": "You're viewing attendance records. I can explain attendance data and help with any questions.", "qr": "You're on the QR scanner page. I can guide you through the check-in process.", "biometric": "You're managing biometric settings. I can help with biometric authentication setup.", "profile": "You're viewing your profile. I can assist with account settings and preferences.", "admin": "You're in the admin panel. I can help with system management and user administration.", "general": "I can help you navigate and understand any part of the attendance system."}, "invitation": "What would you like to know or do today? I'm here to make your experience seamless! ✨"}, "quickActions": {"scanQR": "📱 Scan QR Code", "checkAttendance": "📊 Check Attendance", "biometricHelp": "🔐 Biometric Help", "appTour": "🎯 App Tour"}, "floatingButtonThinking": "AI Assistant is thinking...", "welcome": "Welcome to ATS Assistant!", "welcomeDescription": "I'm here to help you with anything related to our attendance system and your school.", "askAnything": "Ask me anything...", "tryAsking": "Try asking me:", "responses": {"qrScan": "I'll help you with QR code scanning! 📱", "attendance": "I can help you with attendance information! 📊", "biometric": "I'll guide you through biometric authentication! 🔐", "help": "I'm here to help! 🤖 I can assist you with:", "profile": "I can help you manage your profile! 👤", "notifications": "I can help you with notifications! 🔔"}, "actions": {"openScanner": "📱 Open Scanner", "cameraHelp": "📷 Camera Issues", "viewAttendance": "📊 View Attendance", "attendanceStats": "📈 Statistics", "setupBiometric": "🔐 Setup Biometrics", "troubleshoot": "❓ Troubleshoot", "appTour": "🎯 App Tour", "commonIssues": "🔧 Common Issues", "editProfile": "✏️ Edit Profile", "privacySettings": "🔒 Privacy Settings", "notificationSettings": "⚙️ Notification Settings", "testNotification": "🧪 Test Notification", "qrScanningGuide": "📱 QR Scanning Guide", "appFeatures": "✨ App Features", "askAnotherQuestion": "❓ Ask Another Question", "exploreFeatures": "✨ Explore Features", "qrScanning": "📱 QR Scanning", "biometricAuth": "🔐 Biometric Auth", "reportsAnalytics": "📊 Reports & Analytics", "attendance": "📊 Attendance", "technicalIssues": "🔧 Technical Issues", "qrSteps": "📋 Step-by-Step Guide", "qrProblems": "🔧 QR Problems", "cameraAccess": "📷 Camera Access", "viewMyAttendance": "📈 View My Attendance", "markAttendance": "✅ Mark Attendance", "attendanceProblems": "❓ Attendance Problems", "setupBiometrics": "⚙️ Setup Biometrics", "fixBiometricIssues": "🔧 Fix Biometric Issues", "securityInfo": "🛡️ Security Info", "enableLocation": "📍 Enable Location", "locationProblems": "🔧 Location Problems", "privacyInfo": "🔒 Privacy Info", "setupNotifications": "🔔 Setup Notifications", "notReceivingNotifications": "🔧 Not Receiving Notifications", "attendanceRecords": "📊 Attendance Records", "biometricSetup": "🔐 Biometric Setup", "troubleshooting": "🔧 Troubleshooting", "cameraIssues": "📷 Camera Issues", "biometricIssues": "🔐 Biometric Issues", "locationIssues": "📍 Location Issues", "describeIssue": "💬 Describe Issue"}, "actionMessages": {"qrScanningHelp": "How do I scan QR codes?", "checkAttendance": "How do I check my attendance?", "showAppFeatures": "What features does this app have?", "qrScanningInstructions": "Show me step-by-step QR scanning instructions", "anotherQuestion": "I have another question", "exploreMoreFeatures": "What else can this app do?", "helpWithQrScanning": "Help with QR code scanning", "helpWithAttendance": "Help with attendance records", "technicalProblem": "I have a technical problem", "showFeatures": "Show me app features", "detailedQrSteps": "Show me detailed QR scanning steps", "qrNotWorking": "QR scanning is not working", "cameraAccess": "How do I allow camera access?", "viewMyAttendance": "How do I check my attendance records?", "markAttendance": "How do I mark my attendance?", "attendanceIssues": "I have issues with my attendance records", "setupBiometric": "How do I set up biometric authentication?", "biometricNotWorking": "Biometric authentication is not working", "biometricSecurity": "Is biometric authentication secure?", "enableLocation": "How do I enable location services?", "locationNotWorking": "Location verification is not working", "locationPrivacy": "How is my location data used?", "setupNotifications": "How do I enable push notifications?", "notificationSettings": "How do I customize notification preferences?", "notReceivingNotifications": "I'm not receiving notifications", "qrCodeFeatures": "Tell me about QR code features", "biometricAuthFeatures": "Explain biometric authentication", "reportingFeatures": "What reporting features are available?", "appInstallation": "Can I install this as an app?", "cameraIssues": "Camera is not working for QR scanning", "biometricIssues": "Biometric authentication is failing", "locationIssues": "Location verification is not working", "describeIssue": "Let me describe my specific problem"}, "fallbackResponses": {"qrRelated": "I'll help you with QR code scanning! 📱\n\nIs there something specific about QR scanning you'd like to know?", "attendanceRelated": "I'll help you with attendance tracking! 📊\n\nWhat specific aspect of attendance would you like to know about?", "biometricRelated": "I'll help you with biometric authentication! 🔐\n\nWhat do you need help with regarding biometric authentication?", "locationRelated": "I'll help you with location-based attendance! 📍\n\nWhat do you need to know about location verification?", "notificationRelated": "I'll help you with notifications! 🔔\n\nWhat would you like to know about notifications?", "featureInquiry": "I'd be happy to explain our app features! ✨\n\nWhich feature would you like to learn more about?", "helpRequest": "I'm here to help you! 🤖\n\nWhat would you like help with?", "problemReport": "I'll help you solve your issue! 🔧\n\nCould you please provide more details about the specific issue you're facing? This will help me give you the most accurate solution.", "gratitudeResponses": ["You're very welcome! 😊 I'm always here to help you with the attendance system!", "Happy to help! 🌟 Feel free to ask me anything else about the app!", "My pleasure! 🤝 Is there anything else I can assist you with today?"], "generalInquiry": "I'd love to help you! 🤔 Could you please be more specific about what you need assistance with?\n\nI can help you with:\n• QR code scanning and attendance marking\n• Biometric authentication setup\n• Checking attendance records and statistics\n• App features and functionality\n• Troubleshooting technical issues\n• Profile and settings management\n\nWhat would you like to know more about?"}, "greetings": {"welcomeBack": "Welcome back! 👋", "welcomeBackDescription": "I'm here to help you with attendance, QR scanning, and any questions you might have!", "firstTimeWelcome": "Welcome to ATS! 🎉", "firstTimeDescription": "I'm your AI assistant, ready to guide you through the attendance system. Let's get started!", "newUserWelcome": "Great to have you here! ✨", "newUserDescription": "I'll help you navigate the system and answer any questions about attendance tracking.", "student": {"welcomeBack": "Welcome back, Student! 📚", "welcomeBackDescription": "Ready to check your attendance, scan QR codes, or need help with the dormitory system?", "firstTime": "Welcome to your attendance portal! 🎓", "firstTimeDescription": "I'm here to help you with QR scanning, checking your attendance records, and navigating the dormitory system.", "newUser": "Welcome to your new school! 🌟", "newUserDescription": "Let me guide you through the attendance system, QR scanning, and everything you need to know about dormitory check-ins!"}, "teacher": {"welcomeBack": "Welcome back, Teacher! 👨‍🏫", "welcomeBackDescription": "Need help with student attendance, generating QR codes, or managing your classes?", "firstTime": "Welcome to the teacher portal! 📋", "firstTimeDescription": "I can help you monitor student attendance, understand the QR system, and manage your teaching responsibilities.", "newUser": "Welcome to the teaching team! 🍎", "newUserDescription": "I'll help you get familiar with the attendance system, student monitoring, and all the tools available to teachers."}, "admin": {"welcomeBack": "Welcome back, Administrator! 🛡️", "welcomeBackDescription": "Ready to manage attendance policies, generate QR codes, or oversee the school system?", "firstTime": "Welcome to the admin dashboard! ⚙️", "firstTimeDescription": "I can assist you with system configuration, user management, attendance policies, and administrative tasks.", "newUser": "Welcome to your administrative role! 👑", "newUserDescription": "Let me help you understand the full system capabilities, user management, and administrative features at your disposal."}, "pageReload": {"dashboard": {"title": "Back to your dashboard, {{userName}}! 📊", "description": "I'm here to help you navigate your dashboard, check recent activity, or answer any questions you might have."}, "attendance": {"title": "Ready to track attendance, {{userName}}? 📝", "description": "I can help you view attendance records, understand the data, or assist with attendance-related tasks."}, "qrScanner": {"title": "Time to scan some QR codes, {{userName}}! 📱", "description": "Need help with QR scanning? I can guide you through the process or troubleshoot any issues."}, "dormitory": {"title": "Managing dormitory check-ins, {{userName}}? 🏠", "description": "I'm here to help with dormitory management, check-in procedures, or answer any dormitory-related questions."}, "socialMedia": {"title": "Checking the social feed, {{userName}}? 📱", "description": "I can help you understand the social features, manage posts, or assist with community interactions."}, "biometric": {"title": "Working with biometrics, {{userName}}? 👆", "description": "Need help with biometric registration, deletion requests, or troubleshooting biometric features?"}, "profile": {"title": "Updating your profile, {{userName}}? 👤", "description": "I can help you manage your profile settings, update information, or explain profile features."}, "settings": {"title": "Adjusting your settings, {{userName}}? ⚙️", "description": "Need help configuring your preferences, understanding settings, or making changes to your account?"}, "admin": {"title": "Managing the system, {{userName}}? 🛡️", "description": "I'm here to assist with administrative tasks, user management, system configuration, or policy settings."}, "teacher": {"title": "Managing your classes, {{userName}}? 👨‍🏫", "description": "I can help with student management, attendance tracking, QR code generation, or teaching tools."}, "student": {"title": "Ready for your studies, {{userName}}? 🎓", "description": "I'm here to help with attendance checking, QR scanning, dormitory features, or any student-related questions."}, "general": {"title": "Welcome back, {{userName}}! 👋", "description": "I'm here to help you with whatever you need. Feel free to ask me anything about the system!"}}}, "pageContext": {"dashboard": "I see you're on the dashboard - perfect place to get an overview!", "attendance": "Looking at attendance records? I can help explain what you're seeing.", "qr": "Ready to scan some QR codes? I'm here to guide you through the process!", "profile": "Updating your profile? I can help with any questions about the settings.", "settings": "Configuring the system? I'm here to help with any administrative tasks."}, "capabilities": ["QR code scanning", "Attendance tracking", "Biometric setup", "Profile management", "Notifications", "School information"], "errors": {"sendFailed": "Failed to send message. Please try again.", "generalError": "Sorry, I encountered an error. Please try again or contact support if the problem persists."}}, "social": {"updates": "Updates", "schoolUpdates": "School Updates", "stayConnected": "Stay connected with the latest news and announcements", "comingSoon": "Social Media Feed Coming Soon!", "notConfigured": "Your school's social media integration is not yet configured. Contact your administrator to enable this feature and stay connected with school updates!", "noPostsYet": "No posts yet", "checkBackLater": "Check back later for the latest updates from your school!", "refresh": "Refresh", "viewPost": "View Post", "justNow": "Just now", "hoursAgo": "{{count}}h ago", "daysAgo": "{{count}}d ago", "all": "All", "liveFromPage": "Live from our official {{platform}} page", "autoRefreshing": "Auto-refreshing", "auto": "Auto", "platforms": {"instagram": "Instagram", "twitter": "Twitter", "facebook": "Facebook", "youtube": "YouTube", "website": "Website"}}, "debug": {"qr": {"testGenerated": "Test QR Generated", "expiresIn": "QR code will expire in {{seconds}} seconds", "generateFailed": "Failed to generate QR"}}, "landing": {"getStarted": "Get Started", "keyFeatures": "Key Features", "hero": {"badge": "🚀 Next-Generation Attendance System", "title1": "Transform Your School's", "title2": "Attendance Management", "subtitle": "Secure, intelligent, and user-friendly attendance tracking system with biometric authentication, real-time analytics, and comprehensive fraud prevention for modern educational institutions.", "features": {"qr": "QR Code Scanning", "biometric": "Biometric Auth", "location": "GPS Verification", "multilingual": "Multi-Language"}, "learnMore": "Learn More"}, "stats": {"accuracy": "Accuracy Rate", "speed": "Check-in Time", "availability": "System Uptime", "schools": "Different Features"}, "features": {"badge": "🎯 Powerful Features", "title": "Everything You Need for Modern Attendance Management", "subtitle": "Our comprehensive platform combines cutting-edge technology with intuitive design to deliver the most advanced attendance tracking solution available.", "qr": {"title": "QR Code Attendance", "description": "Lightning-fast attendance marking with secure QR codes. Students simply scan to check in, with automatic time-based code generation and validation."}, "biometric": {"title": "Biometric Authentication", "description": "Advanced fingerprint and facial recognition technology ensures foolproof identity verification, eliminating proxy attendance and fraud."}, "location": {"title": "GPS Location Verification", "description": "Precise location tracking ensures students are physically present on campus. Configurable geofencing with customizable radius settings."}, "analytics": {"title": "Real-time Analytics & Reports", "description": "Comprehensive dashboards with live attendance data, detailed reports, trend analysis, and automated alerts for administrators and teachers."}, "multilingual": {"title": "Multi-Language Support", "description": "Full internationalization with English and Turkish support. Automatic language detection and seamless switching for global accessibility."}, "excuses": {"title": "Digital Excuse Management", "description": "Streamlined excuse submission and approval workflow. Students submit digital excuses with photo evidence, teachers review and approve instantly."}, "advanced": {"title": "Advanced Security & Customization", "security": "Enterprise Security", "securityDesc": "Bank-level encryption and security", "notifications": "Smart Notifications", "notificationsDesc": "Real-time alerts and updates", "branding": "Custom Branding", "brandingDesc": "Personalized school themes", "cloud": "Cloud Infrastructure", "cloudDesc": "99.9% uptime guarantee"}}, "howItWorks": {"badge": "📋 Simple Process", "title": "How It Works in 4 Easy Steps", "subtitle": "Our streamlined process makes attendance tracking effortless for everyone involved, from initial setup to daily operations.", "step1": {"title": "Initial Setup", "description": "Configure your school settings, add users, and customize verification methods. One-time setup takes just minutes."}, "step2": {"title": "Deploy Tablets", "description": "Install tablets in classrooms with our secure kiosk mode. QR codes generate automatically during attendance hours."}, "step3": {"title": "Students Check In", "description": "Students scan QR codes and verify identity with biometrics or PIN. Location is automatically validated."}, "step4": {"title": "Real-time Analytics", "description": "Teachers and administrators instantly see attendance data, receive alerts, and access comprehensive reports."}, "process": {"title": "Seamless Data Flow", "student": "Student Action", "studentDesc": "Scan & verify identity", "system": "System Processing", "systemDesc": "Validate & record data", "teachers": "Teacher Dashboard", "teachersDesc": "View real-time updates"}}, "userTypes": {"badge": "👥 User-Centric Design", "title": "Designed for Every User Type", "subtitle": "Tailored experiences and powerful tools for students, teachers, administrators, and system managers.", "students": {"title": "Students", "subtitle": "Simple, fast, and secure attendance marking", "feature1": "Lightning-fast QR code scanning with instant feedback", "feature2": "Biometric authentication for enhanced security", "feature3": "Personal attendance history and statistics", "feature4": "Digital excuse submission with photo evidence", "feature5": "Multi-language interface support"}, "teachers": {"title": "Teachers", "subtitle": "Comprehensive classroom management tools", "feature1": "Real-time attendance dashboard with live updates", "feature2": "Instant absence alerts and notifications", "feature3": "Detailed student attendance patterns and analytics", "feature4": "Excuse review and approval workflow", "feature5": "Fraud detection alerts and location verification"}, "administrators": {"title": "School Administrators", "subtitle": "Complete school-wide oversight and control", "feature1": "System-wide attendance analytics and reporting", "feature2": "User management and role assignment", "feature3": "Custom branding and school configuration", "feature4": "Advanced fraud detection and security monitoring", "feature5": "Automated notifications and parent communication"}, "systemAdmin": {"title": "System Administrators", "subtitle": "Enterprise-level system management and control", "feature1": "Global Database Management", "feature1Desc": "Multi-school data oversight and cleanup", "feature2": "System Maintenance", "feature2Desc": "Automated updates and health monitoring", "feature3": "Security Management", "feature3Desc": "Advanced security policies and access control", "feature4": "Platform Configuration", "feature4Desc": "System-wide settings and customization"}}, "cta": {"badge": "🚀 Ready to Transform?", "title": "Start Your Digital Transformation Today", "description": "Join hundreds of educational institutions worldwide that have revolutionized their attendance management with our cutting-edge platform.", "benefit1": "Setup in Minutes", "benefit2": "Enterprise Security", "benefit3": "24/7 Support", "startNow": "Start For Free", "createAccount": "Create Account", "trustIndicator": "Trusted and reliable for all educational institutions worldwide", "secure": "Bank-Level Security", "realtime": "Real-Time Updates", "multilingual": "Multi-Language", "cloud": "Cloud-Based"}}, "loading": {"authenticating": "Authenticating...", "teacherDashboard": "Loading Teacher Dashboard...", "adminDashboard": "Loading Admin Dashboard...", "studentDashboard": "Loading Student Dashboard...", "roomData": "Loading room data...", "recordingAttendance": "Recording your attendance...", "students": "Loading students...", "schoolInformation": "Loading school information...", "blocksAndRooms": "Loading blocks and rooms...", "fraudDetectionDashboard": "Loading fraud detection dashboard...", "failedToLoadInitialData": "Failed to load initial data. Please refresh the page.", "pleaseWait": "Please wait...", "processing": "Processing...", "saving": "Saving...", "loading": "Loading...", "fetchingData": "Fetching data...", "initializing": "Initializing...", "verifying": "Verifying...", "connecting": "Connecting...", "synchronizing": "Synchronizing...", "excuses": "Loading excuses...", "alerts": "Loading alerts...", "roomSettings": "Loading room settings...", "verificationSettings": "Loading verification settings...", "checkingMaintenanceStatus": "Checking maintenance status...", "checkingUserStatus": "Checking user status..."}, "cleanup": {"title": "Database Cleanup", "description": "Manage automatic database cleanup and data retention", "retentionPolicy": "Data Retention Policy", "retentionPolicyDescription": "Automatic cleanup removes old data based on these retention periods", "currentStatistics": "Current Statistics", "currentStatisticsDescription": "Current record counts in the database", "manualCleanup": "Manual Cleanup", "manualCleanupDescription": "Trigger an immediate cleanup operation", "automaticCleanup": "Automatic Cleanup", "lastCleanup": "Last Cleanup", "never": "Never", "runCleanupNow": "Run Cleanup Now", "runCleanupNowDescription": "This will immediately clean up old records based on retention settings", "runCleanup": "Run Cleanup", "running": "Running...", "cleanupCompleted": "Cleanup Completed", "cleanupCompletedDescription": "Removed {{total}} records: {{attendance}} attendance, {{notifications}} notifications, {{excuses}} excuses, {{alerts}} alerts", "cleanupFailed": "Cleanup failed. Please try again.", "noDataAvailable": "No data available", "automaticCleanupEnabled": "Automatic cleanup is enabled", "automaticCleanupSchedule": "Runs daily at 2:00 AM", "automaticCleanupBenefits": "Keeps database size minimal and improves performance", "attendanceRecords": "Attendance Records", "notifications": "Notifications", "excuses": "Excuses", "locationAlerts": "Location Alerts", "biometricCredentials": "Biometric Credentials", "feedbackSubmissions": "Feedback Submissions", "systemLogs": "System Logs", "userActivityLogs": "User Activity Logs", "qrSessions": "QR Sessions"}}