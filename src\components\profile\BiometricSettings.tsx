import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Shield,
  Plus,
  Trash2,
  CheckCircle,
  Fingerprint,
  AlertTriangle,
  ChevronDown,
  Clock,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { isWebAuthnAvailable, isPasskeyAvailable, isBiometricOnlySupported } from "@/lib/webauthn";
import { supabase } from "@/lib/supabase";
import SimpleBiometricAuth from "@/components/auth/SimpleBiometricAuth";

export default function BiometricSettings() {
  const [hasRegistration, setHasRegistration] = useState(false);
  const [showRegistration, setShowRegistration] = useState(false);
  const [showDeletion, setShowDeletion] = useState(false);
  const [loading, setLoading] = useState(true);
  const [registering, setRegistering] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [pendingDeletionRequest, setPendingDeletionRequest] = useState<any>(null);
  const [approvedDeletionRequest, setApprovedDeletionRequest] = useState<any>(null);

  const { user, profile, updateProfile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Fetch deletion requests
  const fetchDeletionRequests = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("biometric_deletion_requests")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Find pending and approved requests
      const pending = data?.find(req => req.status === 'pending');
      const approved = data?.find(req => req.status === 'approved');

      setPendingDeletionRequest(pending || null);
      setApprovedDeletionRequest(approved || null);

      // If there's an approved deletion request, update biometric status
      if (approved) {
        // Double-check by verifying no credentials exist in database
        try {
          console.log('Approved deletion detected, verifying credential status...');
          const hasPasskey = await isPasskeyAvailable(user.id);
          console.log(`After deletion verification: hasPasskey = ${hasPasskey}`);
          setHasRegistration(hasPasskey);

          // If credentials were actually deleted, update profile
          if (!hasPasskey && profile) {
            console.log('Updating profile to reflect biometric deletion');
            await updateProfile({ biometricRegistered: false });
          }
        } catch (verifyError) {
          console.error("Error verifying biometric status after deletion:", verifyError);
          // Fallback: assume deletion was successful
          console.log('Fallback: assuming deletion was successful');
          setHasRegistration(false);
          if (profile) {
            try {
              await updateProfile({ biometricRegistered: false });
            } catch (updateError) {
              console.error("Error updating profile after deletion:", updateError);
            }
          }
        }
      }
    } catch (error) {
      console.error("Error fetching deletion requests:", error);
    }
  };

  // Load biometric data on mount
  useEffect(() => {
    const loadBiometricData = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Check if WebAuthn is supported
        const supported = isWebAuthnAvailable();
        setIsSupported(supported);

        if (supported) {
          // Check if user has registered biometrics
          console.log('Checking biometric availability for user:', user.id);
          const hasPasskey = await isPasskeyAvailable(user.id);
          console.log('Initial biometric check result:', hasPasskey);
          setHasRegistration(hasPasskey);

          // Fetch deletion requests
          await fetchDeletionRequests();
        }
      } catch (error) {
        console.error("Error loading biometric data:", error);
        toast({
          title: t("common.error"),
          description: t("biometrics.loadError"),
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadBiometricData();
  }, [user, toast, t]);

  // Set up real-time subscription for deletion request updates
  useEffect(() => {
    if (!user) return;

    // Subscribe to changes in biometric deletion requests
    const subscription = supabase
      .channel('biometric_deletion_requests')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'biometric_deletion_requests',
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          console.log('Deletion request updated:', payload);
          // Refresh deletion requests and biometric status when status changes
          refreshBiometricStatus();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user]);

  // Manual refresh function
  const refreshBiometricStatus = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Re-check biometric availability
      const hasPasskey = await isPasskeyAvailable(user.id);
      setHasRegistration(hasPasskey);

      // Refresh deletion requests
      await fetchDeletionRequests();

      // Update profile if needed
      if (profile && profile.biometricRegistered !== hasPasskey) {
        await updateProfile({ biometricRegistered: hasPasskey });
      }
    } catch (error) {
      console.error("Error refreshing biometric status:", error);
    } finally {
      setLoading(false);
    }
  };

  // Handle deletion request submission
  const handleDeletionRequest = async () => {
    if (!user || !profile) return;

    try {
      // Create biometric deletion request
      const { error } = await supabase
        .from("biometric_deletion_requests")
        .insert([
          {
            user_id: user.id,
            school_id: profile.school_id,
            student_name: profile.name || profile.email,
            student_email: profile.email,
            student_number: profile.student_number,
            block_name: profile.block_name,
            room_name: profile.room_name,
            request_reason: t("biometrics.defaultDeletionReason"),
            status: 'pending'
          }
        ]);

      if (error) throw error;

      setShowDeletion(false);
      setPendingDeletionRequest({
        user_id: user.id,
        school_id: profile.school_id,
        student_name: profile.name || profile.email,
        student_email: profile.email,
        status: 'pending',
        created_at: new Date().toISOString()
      });

      toast({
        title: t("biometrics.deletionRequestSubmitted"),
        description: t("biometrics.deletionRequestSubmittedMessage"),
      });
    } catch (error) {
      console.error("Error submitting biometric deletion request:", error);
      toast({
        title: t("common.error"),
        description: t("biometrics.deletionRequestSubmissionError"),
        variant: "destructive",
      });
    }
  };

  // Handle direct biometric registration (no approval needed)
  const handleRegistration = async () => {
    if (!user) return;

    try {
      setRegistering(true);

      // Start biometric registration
      await startRegistration(user.id, user.email || "");

      setShowRegistration(false);
      setHasRegistration(true);

      // Update the profile to reflect biometric registration
      if (profile) {
        try {
          await updateProfile({ biometricRegistered: true });
        } catch (error) {
          console.error("Error updating profile after biometric registration:", error);
        }
      }

      toast({
        title: t("common.success"),
        description: t("biometrics.registrationSuccess"),
      });
    } catch (error) {
      console.error("Error during biometric registration:", error);
      toast({
        title: t("biometrics.registrationFailed"),
        description: error instanceof Error ? error.message : t("biometrics.registrationError"),
        variant: "destructive",
      });
    } finally {
      setRegistering(false);
    }
  };

  // Handle registration success
  const handleRegistrationSuccess = () => {
    setShowRegistration(false);
    setHasRegistration(true);

    toast({
      title: t("common.success"),
      description: t("biometrics.registrationSuccess"),
    });
  };

  // Handle registration error
  const handleRegistrationError = (error: string) => {
    toast({
      title: t("biometrics.registrationFailed"),
      description: error,
      variant: "destructive",
    });
  };

  // Request biometric deletion (requires admin approval)
  const handleRequestDeletion = () => {
    setShowDeletion(true);
  };

  // Submit deletion request
  const handleSubmitDeletionRequest = async () => {
    if (!user || !profile) return;

    try {
      const { error } = await supabase
        .from("biometric_deletion_requests")
        .insert([
          {
            user_id: user.id,
            school_id: profile.school_id,
            student_name: profile.name || profile.email,
            student_email: profile.email,
            request_reason: t("biometrics.defaultDeletionReason"),
            status: 'pending'
          }
        ]);

      if (error) throw error;

      setShowDeletion(false);
      await fetchDeletionRequests(); // Refresh the deletion requests

      toast({
        title: t("biometrics.deletionRequestSubmitted"),
        description: t("biometrics.deletionRequestSubmittedMessage"),
      });
    } catch (error) {
      console.error("Error submitting deletion request:", error);
      toast({
        title: t("common.error"),
        description: t("biometrics.deletionRequestSubmissionError"),
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4 sm:p-8">
        <div className="text-center">
          <Shield className="w-6 h-6 sm:w-8 sm:h-8 mx-auto text-gray-400 mb-2" />
          <p className="text-sm sm:text-base text-gray-600">{t("biometrics.loading")}</p>
        </div>
      </div>
    );
  }

  if (!isSupported) {
    return (
      <Card className="mx-2 sm:mx-0">
        <CardHeader className="pb-4 sm:pb-6">
          <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
            <Shield className="w-4 h-4 sm:w-5 sm:h-5" />
            {t("biometrics.title")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 sm:py-8">
            <AlertTriangle className="w-8 h-8 sm:w-12 sm:h-12 mx-auto text-orange-400 mb-3 sm:mb-4" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">
              {t("biometrics.notAvailable")}
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4 px-2">
              {t("biometrics.notAvailableDesc")}
            </p>
            <div className="bg-blue-50 p-3 sm:p-4 rounded-lg text-left">
              <p className="text-xs sm:text-sm font-medium text-blue-800 mb-2">
                {t("biometrics.requirements")}:
              </p>
              <ul className="text-xs sm:text-sm text-blue-700 space-y-1">
                <li>• {t("biometrics.modernBrowser")}</li>
                <li>• {t("biometrics.httpsConnection")}</li>
                <li>• {t("biometrics.biometricDevice")}</li>
                <li>• {t("biometrics.webauthnSupport")}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-0">
      <div className="text-center sm:text-left">
        <h2 className="text-xl sm:text-2xl font-bold mb-2">{t("biometrics.title")}</h2>
        <p className="text-sm sm:text-base text-gray-600 px-2 sm:px-0">
          {t("biometrics.description")}
        </p>
      </div>

      {/* Current Status */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 border rounded-lg gap-3 sm:gap-0">
        <div className="flex items-center gap-3">
          <div
            className={`p-2 rounded-lg ${
              hasRegistration ? "bg-green-100" : "bg-gray-100"
            }`}
          >
            <Fingerprint
              className={`w-4 h-4 sm:w-5 sm:h-5 ${
                hasRegistration ? "text-green-600" : "text-gray-600"
              }`}
            />
          </div>
          <div>
            <p className="font-medium text-sm sm:text-base">{t("biometrics.authentication")}</p>
            <p className="text-xs sm:text-sm text-gray-600">
              {hasRegistration
                ? t("biometrics.registered")
                : t("biometrics.notRegistered")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          {hasRegistration ? (
            pendingDeletionRequest ? (
              <Button
                disabled
                size="sm"
                className="w-full sm:w-auto text-xs sm:text-sm bg-yellow-100 text-yellow-800 border border-yellow-300 cursor-not-allowed"
              >
                <Clock className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                {t("biometrics.deletionPending")}
              </Button>
            ) : (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="text-green-600 border-green-200 hover:bg-green-50 w-full sm:w-auto text-xs sm:text-sm"
                    size="sm"
                  >
                    <CheckCircle className="w-3 h-3 mr-1" />
                    {t("biometrics.active")}
                    <ChevronDown className="w-3 h-3 ml-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={refreshBiometricStatus}
                    disabled={loading}
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    {t("biometrics.refreshStatus")}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleRequestDeletion}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    {t("biometrics.requestDeletion")}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )
          ) : (
            <Button
              onClick={() => setShowRegistration(true)}
              size="sm"
              disabled={registering}
              className="w-full sm:w-auto text-xs sm:text-sm bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 font-medium"
            >
              {registering ? (
                <>
                  <div className="w-3 h-3 sm:w-4 sm:h-4 mr-1 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  {t("biometrics.registering")}
                </>
              ) : (
                <>
                  <Plus className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                  {t("biometrics.register")}
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Pending Deletion Request Status */}
      {pendingDeletionRequest && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mx-2 sm:mx-0">
          <div className="flex items-start gap-3">
            <Clock className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-yellow-800 mb-1">
                {t("biometrics.deletionPendingTitle")}
              </h3>
              <p className="text-sm text-yellow-700 mb-2">
                {t("biometrics.deletionPendingMessage")}
              </p>
              <p className="text-xs text-yellow-600">
                {t("biometrics.requestSubmittedOn")}: {new Date(pendingDeletionRequest.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Approved Deletion Request Status */}
      {approvedDeletionRequest && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mx-2 sm:mx-0">
          <div className="flex items-start gap-3">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-green-800 mb-1">
                {t("biometrics.deletionApprovedTitle")}
              </h3>
              <p className="text-sm text-green-700 mb-2">
                {t("biometrics.deletionApprovedMessage")}
              </p>
              {approvedDeletionRequest.admin_notes && (
                <div className="bg-green-100 p-2 rounded text-xs text-green-800 mb-2">
                  <strong>{t("biometrics.adminNote")}:</strong> {approvedDeletionRequest.admin_notes}
                </div>
              )}
              <p className="text-xs text-green-600">
                {t("biometrics.requestApprovedOn")}: {new Date(approvedDeletionRequest.approved_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Biometric Registration Interface - Show right after register button */}
      {showRegistration && user && profile && (
        <div className="border-2 border-blue-200 rounded-lg p-1 bg-gradient-to-r from-blue-50 to-purple-50 mx-2 sm:mx-0">
          <Card className="border-0 shadow-none bg-transparent">
            <CardHeader className="pb-4 sm:pb-6">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <Fingerprint className="w-5 h-5 text-blue-600" />
                {t("biometrics.registerTitle")}
              </CardTitle>
              <p className="text-xs sm:text-sm text-gray-600">
                {t("biometrics.registerDescription")}
              </p>
            </CardHeader>
            <CardContent>
              <SimpleBiometricAuth
                userId={user.id}
                username={profile.name || profile.email}
                mode="register"
                onSuccess={handleRegistrationSuccess}
                onError={handleRegistrationError}
                className="border-0 shadow-none"
              />
              <div className="mt-3 sm:mt-4">
                <Button
                  onClick={() => setShowRegistration(false)}
                  variant="outline"
                  className="w-full text-sm"
                  size="sm"
                >
                  {t("common.cancel")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Security Notice */}
      <div className="bg-green-50 p-3 sm:p-4 rounded-lg">
        <div className="flex items-start gap-2 sm:gap-3">
          <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-600 mt-0.5 flex-shrink-0" />
          <div className="text-xs sm:text-sm">
            <p className="font-medium text-green-800 mb-1">
              {t("biometrics.securityPrivacy")}
            </p>
            <ul className="text-green-700 space-y-1">
              <li>• {t("biometrics.dataStaysLocal")}</li>
              <li>• {t("biometrics.encryptedStorage")}</li>
              <li>• {t("biometrics.removeAnytime")}</li>
              <li>• {t("biometrics.pinBackup")}</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Benefits */}
      <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
        <div className="flex items-start gap-2 sm:gap-3">
          <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="text-xs sm:text-sm">
            <p className="font-medium text-blue-800 mb-1">
              {t("biometrics.benefits")}
            </p>
            <ul className="text-blue-700 space-y-1">
              <li>• {t("biometrics.quickAttendance")}</li>
              <li>• {t("biometrics.enhancedSecurity")}</li>
              <li>• {t("biometrics.noPasswords")}</li>
              <li>• {t("biometrics.multipleDevices")}</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Deletion Request Dialog */}
      <AlertDialog open={showDeletion} onOpenChange={setShowDeletion}>
        <AlertDialogContent className="mx-4 sm:mx-0 max-w-md sm:max-w-lg">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-base sm:text-lg">{t("biometrics.requestDeletion")}</AlertDialogTitle>
            <AlertDialogDescription className="text-sm">
              {t("biometrics.deletionRequestSubmittedMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <AlertDialogCancel className="w-full sm:w-auto text-sm">{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSubmitDeletionRequest}
              className="bg-red-600 hover:bg-red-700 w-full sm:w-auto text-sm"
            >
              {t("biometrics.requestDeletion")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>


    </div>
  );
}
