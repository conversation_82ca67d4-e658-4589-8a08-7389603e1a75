import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Upload,
  Users,
  Fingerprint,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Download,
  Play,
  Pause,
  RotateCcw,
  Monitor,
  FileText,
  Plus
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { getDeviceInfo } from "@/lib/webauthn";

interface Student {
  id: string;
  email: string;
  name: string;
  status: 'pending' | 'registered' | 'failed' | 'skipped';
  error?: string;
  registeredAt?: string;
}

interface RegistrationSession {
  id: string;
  deviceId: string;
  deviceName: string;
  sessionName: string;
  totalStudents: number;
  registeredStudents: number;
  failedRegistrations: number;
  sessionStatus: 'active' | 'completed' | 'cancelled';
  startedAt: string;
  completedAt?: string;
  students: Student[];
}

export default function BulkBiometricRegistration() {
  const [sessions, setSessions] = useState<RegistrationSession[]>([]);
  const [currentSession, setCurrentSession] = useState<RegistrationSession | null>(null);
  const [loading, setLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [studentEmails, setStudentEmails] = useState('');
  const [sessionName, setSessionName] = useState('');
  const [deviceInfo, setDeviceInfo] = useState<any>(null);

  const { user, profile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    // Get device information
    const info = getDeviceInfo();
    setDeviceInfo(info);
    
    // Load existing sessions
    loadSessions();
  }, []);

  const loadSessions = async () => {
    if (!profile?.school_id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('registration_sessions')
        .select(`
          *,
          registration_session_students (
            student_id,
            student_email,
            student_name,
            registration_status,
            registration_order,
            registered_at,
            error_message
          )
        `)
        .eq('school_id', profile.school_id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedSessions = data.map(session => ({
        id: session.id,
        deviceId: session.device_id,
        deviceName: session.device_name,
        sessionName: session.session_name,
        totalStudents: session.total_students,
        registeredStudents: session.registered_students,
        failedRegistrations: session.failed_registrations,
        sessionStatus: session.session_status,
        startedAt: session.started_at,
        completedAt: session.completed_at,
        students: session.registration_session_students.map((s: any) => ({
          id: s.student_id,
          email: s.student_email,
          name: s.student_name,
          status: s.registration_status,
          error: s.error_message,
          registeredAt: s.registered_at
        })).sort((a: any, b: any) => a.registration_order - b.registration_order)
      }));

      setSessions(formattedSessions);
    } catch (error) {
      console.error('Error loading sessions:', error);
      toast({
        title: t('common.error'),
        description: 'Failed to load registration sessions',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const createSession = async () => {
    if (!profile?.school_id || !deviceInfo) return;

    const emails = studentEmails
      .split('\n')
      .map(email => email.trim())
      .filter(email => email && email.includes('@'));

    if (emails.length === 0) {
      toast({
        title: t('common.error'),
        description: 'Please enter valid student email addresses',
        variant: 'destructive'
      });
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('start_registration_session', {
        device_id_param: deviceInfo.deviceId,
        device_name_param: deviceInfo.deviceName,
        session_name_param: sessionName || `Registration Session ${new Date().toLocaleDateString()}`,
        school_id_param: profile.school_id,
        student_emails: emails
      });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error || 'Failed to create registration session');
      }

      toast({
        title: t('common.success'),
        description: `Registration session created with ${data.total_students} students`
      });

      setShowCreateDialog(false);
      setStudentEmails('');
      setSessionName('');
      loadSessions();
    } catch (error) {
      console.error('Error creating session:', error);
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : 'Failed to create session',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'registered':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'skipped':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'registered': 'default',
      'failed': 'destructive',
      'skipped': 'secondary',
      'pending': 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Bulk Biometric Registration</h2>
          <p className="text-muted-foreground">
            Register multiple students' biometrics on this kiosk device
          </p>
        </div>
        
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              New Session
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Create Registration Session</DialogTitle>
              <DialogDescription>
                Set up a new bulk registration session for students on this device.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="sessionName">Session Name (Optional)</Label>
                <Input
                  id="sessionName"
                  value={sessionName}
                  onChange={(e) => setSessionName(e.target.value)}
                  placeholder="e.g., Grade 10 Registration - January 2025"
                />
              </div>
              
              <div>
                <Label htmlFor="deviceInfo">Device Information</Label>
                <div className="p-3 bg-muted rounded-md text-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <Monitor className="w-4 h-4" />
                    <span className="font-medium">{deviceInfo?.deviceName}</span>
                  </div>
                  <div className="text-muted-foreground">
                    Device ID: {deviceInfo?.deviceId}
                  </div>
                </div>
              </div>
              
              <div>
                <Label htmlFor="studentEmails">Student Email Addresses</Label>
                <Textarea
                  id="studentEmails"
                  value={studentEmails}
                  onChange={(e) => setStudentEmails(e.target.value)}
                  placeholder="Enter student email addresses, one per line:&#10;<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                  rows={8}
                  className="font-mono text-sm"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Enter one email address per line. Only valid students from your school will be included.
                </p>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancel
              </Button>
              <Button onClick={createSession} disabled={loading}>
                {loading ? 'Creating...' : 'Create Session'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Device Info Card */}
      {deviceInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Current Device
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Device Name:</span> {deviceInfo.deviceName}
              </div>
              <div>
                <span className="font-medium">Device ID:</span> {deviceInfo.deviceId}
              </div>
              <div>
                <span className="font-medium">Platform:</span> {deviceInfo.platform}
              </div>
              <div>
                <span className="font-medium">Browser:</span> {deviceInfo.userAgent.split(' ')[0]}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Registration Sessions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Registration Sessions
          </CardTitle>
          <CardDescription>
            Manage and monitor bulk registration sessions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {sessions.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Registration Sessions</h3>
              <p className="text-muted-foreground mb-4">
                Create your first bulk registration session to get started.
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Session
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {sessions.map((session) => (
                <Card key={session.id} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{session.sessionName}</CardTitle>
                        <CardDescription>
                          Device: {session.deviceName} • Started: {new Date(session.startedAt).toLocaleString()}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(session.sessionStatus)}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentSession(session)}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{session.totalStudents}</div>
                        <div className="text-sm text-muted-foreground">Total Students</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{session.registeredStudents}</div>
                        <div className="text-sm text-muted-foreground">Registered</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{session.failedRegistrations}</div>
                        <div className="text-sm text-muted-foreground">Failed</div>
                      </div>
                    </div>

                    <Progress
                      value={(session.registeredStudents / session.totalStudents) * 100}
                      className="h-2"
                    />
                    <div className="text-sm text-muted-foreground mt-1">
                      {session.registeredStudents} of {session.totalStudents} students registered
                      ({Math.round((session.registeredStudents / session.totalStudents) * 100)}%)
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Session Details Dialog */}
      {currentSession && (
        <Dialog open={!!currentSession} onOpenChange={() => setCurrentSession(null)}>
          <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{currentSession.sessionName}</DialogTitle>
              <DialogDescription>
                Registration session details and student progress
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Session Summary */}
              <div className="grid grid-cols-4 gap-4 p-4 bg-muted rounded-lg">
                <div className="text-center">
                  <div className="text-lg font-bold">{currentSession.totalStudents}</div>
                  <div className="text-sm text-muted-foreground">Total</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{currentSession.registeredStudents}</div>
                  <div className="text-sm text-muted-foreground">Registered</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-600">{currentSession.failedRegistrations}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-yellow-600">
                    {currentSession.totalStudents - currentSession.registeredStudents - currentSession.failedRegistrations}
                  </div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                </div>
              </div>

              {/* Students Table */}
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Registered At</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentSession.students.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell className="font-medium">{student.name}</TableCell>
                        <TableCell>{student.email}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(student.status)}
                            {getStatusBadge(student.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {student.registeredAt
                            ? new Date(student.registeredAt).toLocaleString()
                            : '-'
                          }
                        </TableCell>
                        <TableCell>
                          {student.error && (
                            <span className="text-sm text-red-600">{student.error}</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setCurrentSession(null)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
