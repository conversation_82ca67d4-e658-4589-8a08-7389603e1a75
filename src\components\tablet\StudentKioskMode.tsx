import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Fingerprint, Mail, User, AlertTriangle, CheckCircle, Loader2, Clock, Settings } from "lucide-react";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { startAuthentication, canPerformBiometricAuth, startKioskRegistration, isBiometricOnlySupported } from "@/lib/webauthn";
import { toast } from "sonner";

interface StudentKioskModeProps {
  roomId: string;
  schoolId: string;
  onSuccess?: (studentId: string, studentName: string) => void;
  onCancel?: () => void;
}

export default function StudentKioskMode({ roomId, schoolId, onSuccess, onCancel }: StudentKioskModeProps) {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [studentId, setStudentId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [hasBiometricsOnDevice, setHasBiometricsOnDevice] = useState<boolean | null>(null);
  const [studentProfile, setStudentProfile] = useState<any>(null);
  const [step, setStep] = useState<'email' | 'biometric' | 'success'>('email');
  const [recentCheckins, setRecentCheckins] = useState<any[]>([]);

  // Fetch recent check-ins for this room
  useEffect(() => {
    const fetchRecentCheckins = async () => {
      try {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const { data, error } = await supabase
          .from('attendance_records')
          .select('id, timestamp, profiles(name)')
          .eq('room_id', roomId)
          .gte('timestamp', today.toISOString())
          .order('timestamp', { ascending: false })
          .limit(5);
          
        if (error) throw error;
        
        setRecentCheckins(data || []);
      } catch (err) {
        console.error('Error fetching recent check-ins:', err);
      }
    };
    
    fetchRecentCheckins();
    // Set up interval to refresh recent check-ins
    const interval = setInterval(fetchRecentCheckins, 30000);
    return () => clearInterval(interval);
  }, [roomId]);

  // Find student by email
  const findStudentByEmail = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // First check if email is valid
      if (!email || !email.includes('@')) {
        throw new Error(t('kiosk.invalidEmail'));
      }
      
      // Find the student profile
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('id, user_id, name, email, biometric_registered, school_id')
        .eq('email', email.trim().toLowerCase())
        .eq('school_id', schoolId)
        .single();
        
      if (profileError || !profiles) {
        throw new Error(t('kiosk.studentNotFound'));
      }
      
      // Check if student has registered biometrics
      if (!profiles.biometric_registered) {
        throw new Error(t('kiosk.biometricsNotRegistered'));
      }
      
      // Store student profile
      setStudentProfile(profiles);
      setStudentId(profiles.user_id);

      // Check if student has biometrics registered on THIS specific device
      // Note: WebAuthn credentials are device-specific, so even if a student
      // has registered on their phone, they need to register on each kiosk device
      const canAuth = await canPerformBiometricAuth(profiles.user_id);
      setHasBiometricsOnDevice(canAuth);

      console.log(`Student ${profiles.name} biometric status on this device:`, canAuth);

      // Move to biometric verification step
      setStep('biometric');
      
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Verify student with biometrics
  const verifyWithBiometrics = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (!studentProfile || !studentId) {
        throw new Error(t('kiosk.noStudentSelected'));
      }

      // Try to authenticate - if it fails, it means credentials aren't on this device
      // We'll catch the error and provide helpful guidance

      // Authenticate with biometrics
      console.log('Authenticating student with biometrics');
      await startAuthentication(studentId);

      // If we get here, authentication was successful
      await recordAttendance();

      // Show success state
      setSuccess(true);
      setStep('success');

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(studentId, studentProfile.name);
      }

      // Reset form after 5 seconds
      setTimeout(() => {
        resetForm();
      }, 5000);

    } catch (err: any) {
      console.error('Biometric verification error:', err);

      // Handle specific WebAuthn errors
      if (err.message?.includes('No credentials available') ||
          err.message?.includes('passkey') ||
          err.name === 'NotAllowedError') {
        // This means the student needs to register on this device
        setError(t('kiosk.needToRegisterOnDevice'));
        // Update the status to show registration is needed
        setHasBiometricsOnDevice(false);
      } else {
        setError(err.message || t('kiosk.biometricVerificationFailed'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Register biometrics on this kiosk device
  const registerBiometrics = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (!studentProfile || !studentId) {
        throw new Error(t('kiosk.noStudentSelected'));
      }

      console.log('Registering biometrics for student on kiosk device');

      // Register biometrics on this device using kiosk-specific function
      await startKioskRegistration(studentId, studentProfile.email, schoolId);

      console.log('Biometric registration successful');

      // Update the biometric status
      setHasBiometricsOnDevice(true);

      // Show success message for registration
      toast.success(t('kiosk.biometricRegistrationSuccess'), {
        description: t('kiosk.biometricRegistrationSuccessDescription')
      });

      // Go back to biometric verification step
      setStep('biometric');

    } catch (err: any) {
      console.error('Biometric registration error:', err);
      setError(err.message || t('kiosk.biometricRegistrationFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  // Record attendance in the database
  const recordAttendance = async () => {
    try {
      const now = new Date();
      
      // Get device info
      const deviceInfo = {
        device: "kiosk",
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        kiosk: true,
        roomId: roomId,
        schoolId: schoolId
      };
      
      // Check if already present today
      const today = new Date().toISOString().split("T")[0];
      const { data: existingAttendance } = await supabase
        .from("attendance_records")
        .select("*")
        .eq("student_id", studentId)
        .eq("room_id", roomId)
        .gte("timestamp", `${today}T00:00:00.000Z`)
        .lt("timestamp", `${today}T23:59:59.999Z`);
      
      if (existingAttendance && existingAttendance.length > 0) {
        // Already checked in today
        toast.info(t('kiosk.alreadyCheckedIn'), {
          description: t('kiosk.alreadyCheckedInDescription')
        });
        return;
      }
      
      // Create attendance record
      const { error } = await supabase
        .from("attendance_records")
        .insert({
          student_id: studentId,
          room_id: roomId,
          school_id: schoolId,
          timestamp: now.toISOString(),
          device_info: deviceInfo,
          verification_method: "kiosk_biometric",
          status: "present",
          created_at: now.toISOString(),
        });
      
      if (error) throw error;
      
      toast.success(t('kiosk.attendanceRecorded'), {
        description: t('kiosk.attendanceRecordedDescription')
      });
      
    } catch (err) {
      console.error('Error recording attendance:', err);
      throw new Error(t('kiosk.attendanceRecordingFailed'));
    }
  };

  // Reset the form
  const resetForm = () => {
    setEmail('');
    setStudentId('');
    setStudentProfile(null);
    setError(null);
    setSuccess(false);
    setStep('email');
    setHasBiometricsOnDevice(null);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          {step === 'email' && <Mail className="w-5 h-5" />}
          {step === 'biometric' && <Fingerprint className="w-5 h-5" />}
          {step === 'register' && <Fingerprint className="w-5 h-5 text-blue-500" />}
          {step === 'success' && <CheckCircle className="w-5 h-5 text-green-500" />}
          {t('kiosk.title')}
        </CardTitle>
        <CardDescription>
          {step === 'email' && t('kiosk.enterEmailDescription')}
          {step === 'biometric' && t('kiosk.verifyBiometricDescription')}
          {step === 'register' && t('kiosk.registerBiometricDescription')}
          {step === 'success' && t('kiosk.successDescription')}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>{t('common.error')}</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {step === 'email' && (
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">
                {t('kiosk.emailLabel')}
              </label>
              <Input
                id="email"
                type="email"
                placeholder={t('kiosk.emailPlaceholder')}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
                className="w-full"
              />
            </div>
          </div>
        )}
        
        {step === 'biometric' && studentProfile && (
          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <User className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-medium">{studentProfile.name}</h3>
                    <p className="text-sm text-muted-foreground">{studentProfile.email}</p>
                  </div>
                </div>

                {/* Settings button - always show, but with different text based on status */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setStep('register')}
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  {hasBiometricsOnDevice ? t('kiosk.reregisterBiometrics') : t('kiosk.setupBiometrics')}
                </Button>
              </div>

              {/* Status indicator */}
              {hasBiometricsOnDevice !== null && (
                <div className="mt-3 pt-3 border-t">
                  <div className="flex items-center gap-2 text-sm">
                    {hasBiometricsOnDevice ? (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-green-700 dark:text-green-400">
                          {t('kiosk.biometricsRegisteredOnThisDevice')}
                        </span>
                      </>
                    ) : (
                      <>
                        <AlertTriangle className="w-4 h-4 text-orange-500" />
                        <span className="text-orange-700 dark:text-orange-400">
                          {t('kiosk.biometricsNotRegisteredOnThisDevice')}
                        </span>
                      </>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('kiosk.deviceSpecificNote')}
                  </p>
                </div>
              )}
            </div>

            {hasBiometricsOnDevice ? (
              <div className="text-center">
                <Fingerprint className="w-16 h-16 mx-auto text-primary animate-pulse" />
                <p className="mt-2">{t('kiosk.touchFingerprint')}</p>
              </div>
            ) : (
              <div className="text-center">
                <Settings className="w-16 h-16 mx-auto text-orange-500" />
                <p className="mt-2 font-medium">{t('kiosk.setupRequired')}</p>
                <p className="text-sm text-muted-foreground">{t('kiosk.setupRequiredDescription')}</p>
              </div>
            )}
          </div>
        )}

        {step === 'register' && studentProfile && (
          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-lg">
              <div className="flex items-center gap-3">
                <User className="w-8 h-8 text-primary" />
                <div>
                  <h3 className="font-medium">{studentProfile.name}</h3>
                  <p className="text-sm text-muted-foreground">{studentProfile.email}</p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-start gap-3">
                <Fingerprint className="w-6 h-6 text-blue-600 dark:text-blue-400 mt-1" />
                <div>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                    {hasBiometricsOnDevice ? t('kiosk.reregisterSetup') : t('kiosk.firstTimeSetup')}
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    {hasBiometricsOnDevice ? t('kiosk.reregisterSetupDescription') : t('kiosk.deviceSpecificSetupDescription')}
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center">
              <Fingerprint className="w-16 h-16 mx-auto text-blue-500 animate-pulse" />
              <p className="mt-2 font-medium">{t('kiosk.registerFingerprint')}</p>
              <p className="text-sm text-muted-foreground mt-1">{t('kiosk.registerFingerprintDescription')}</p>
            </div>
          </div>
        )}
        
        {step === 'success' && (
          <div className="text-center space-y-4">
            <CheckCircle className="w-16 h-16 mx-auto text-green-500" />
            <h3 className="text-xl font-medium text-green-600">{t('kiosk.checkInSuccessful')}</h3>
            <p>{t('kiosk.redirectingShortly')}</p>
            <div className="mt-4">
              <Clock className="w-5 h-5 mx-auto text-muted-foreground" />
              <p className="text-sm text-muted-foreground">{new Date().toLocaleTimeString()}</p>
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-between">
        {step === 'email' && (
          <>
            <Button variant="outline" onClick={onCancel}>
              {t('common.cancel')}
            </Button>
            <Button 
              onClick={findStudentByEmail} 
              disabled={isLoading || !email}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('common.loading')}
                </>
              ) : (
                t('common.continue')
              )}
            </Button>
          </>
        )}
        
        {step === 'biometric' && (
          <>
            <Button variant="outline" onClick={resetForm}>
              {t('common.back')}
            </Button>
            <Button
              onClick={verifyWithBiometrics}
              disabled={isLoading || hasBiometricsOnDevice === false}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('kiosk.verifying')}
                </>
              ) : hasBiometricsOnDevice ? (
                t('kiosk.verifyNow')
              ) : (
                t('kiosk.setupFirstMessage')
              )}
            </Button>
          </>
        )}

        {step === 'register' && (
          <>
            <Button variant="outline" onClick={resetForm}>
              {t('common.back')}
            </Button>
            <Button
              onClick={registerBiometrics}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('kiosk.registering')}
                </>
              ) : (
                t('kiosk.registerNow')
              )}
            </Button>
          </>
        )}
        
        {step === 'success' && (
          <Button onClick={resetForm} className="w-full">
            {t('kiosk.checkInAnother')}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
