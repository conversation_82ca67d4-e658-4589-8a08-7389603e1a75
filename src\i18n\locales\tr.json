{"app": {"name": "{{APP_NAME}}", "shortName": "{{APP_SHORT_NAME}}", "description": "{{APP_DESCRIPTION}}", "tagline": "<PERSON><PERSON>"}, "common": {"languageCode": "tr", "locale": "tr", "direction": "ltr", "language": "Dil", "english": "English", "turkish": "Türkçe", "save": "<PERSON><PERSON>", "cancel": "İptal", "loading": "Yükleniyor...", "success": "Başarılı", "error": "<PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "saveChanges": "Değişiklikleri Kaydet", "processing": "İşleniyor...", "deleting": "Siliniyor...", "notSet": "Ayarlanmamış", "ofTotal": "toplam", "settings": "<PERSON><PERSON><PERSON>", "profile": "Profil", "logout": "Çıkış Yap", "login": "<PERSON><PERSON><PERSON>", "signup": "<PERSON><PERSON><PERSON>", "verifying": "Doğrulanıyor...", "email": "E-posta", "password": "Şifre", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON>", "fullName": "Tam Ad", "name": "İsim", "role": "Rol", "selectRole": "Bir rol seçin", "student": "<PERSON><PERSON><PERSON><PERSON>", "teacher": "Öğretmen", "admin": "Yönetici", "roles": {"student": "<PERSON><PERSON><PERSON><PERSON>", "teacher": "Öğretmen", "admin": "Yönetici"}, "school": "<PERSON><PERSON>", "selectSchool": "<PERSON><PERSON> Seç", "invitationCode": "<PERSON><PERSON>", "enterInvitationCode": "<PERSON><PERSON> kodunu girin", "required": "Zorun<PERSON>", "optional": "İsteğe Bağlı", "submit": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "Oluştur", "update": "<PERSON><PERSON><PERSON><PERSON>", "search": "Ara", "actions": "İşlemler", "photo": "Fotoğraf", "id": "<PERSON><PERSON>", "filter": "Filtrele", "all": "Tümü", "room": "<PERSON><PERSON>", "rooms": "<PERSON><PERSON><PERSON>", "time": "Zaman", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON>", "none": "Hiç<PERSON>i", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "backToHome": "<PERSON><PERSON>", "next": "İleri", "unknown": "Bilinmiyor", "finish": "Bitir", "continue": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "phone": "Telefon", "welcome": "Hoşgeldiniz", "showRooms": "Odaları Göster", "hideRooms": "Odaları Gizle", "dashboard": "Panel", "profileSettings": "<PERSON><PERSON>", "or": "veya <PERSON> git", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block": "Blok", "warning": "Uyarı", "unknownError": "Bilinmeyen hata oluştu", "note": "Not", "status": "Durum", "hello": "<PERSON><PERSON><PERSON><PERSON>", "present": "Mevcut", "absent": "Devamsız", "late": "Geç", "excused": "<PERSON><PERSON><PERSON>", "total": "Toplam", "cleaning": "Temizleniyor...", "exporting": "Dışa aktarılıyor...", "date": "<PERSON><PERSON><PERSON>", "export": "Dışa Aktar", "download": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "building": "<PERSON><PERSON>", "floor": "<PERSON>"}, "maintenance": {"title": "Bakım Modu", "subtitle": "Sistem Bakımda", "message": "Deneyiminizi iyileştirmek için şu anda bazı bakım çalışmaları yapıyoruz.", "defaultMessage": "Sistem şu anda iyileştirmeler için bakım altında.", "estimatedTime": "<PERSON><PERSON><PERSON>", "defaultEstimatedTime": "birkaç saat", "checkAgain": "Tekrar Kontrol <PERSON>", "contactAdmin": "Acil yardıma ihtiyacınız varsa, lütfen sistem yöneticinizle iletişime geçin.", "workingToImprove": "Deneyiminizi iyileştirmek için çalışıyoruz. Lütfen daha sonra tekrar kontrol edin.", "thankYou": "Sabrınız için teşekkür ederiz.", "refreshPage": "Sayfayı Yenile", "systemMaintenance": "Sistem Bakımı", "maintenanceInProgress": "<PERSON><PERSON><PERSON><PERSON> Ed<PERSON>"}, "blockedAccount": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON>", "message": "Hesabınız geçici olarak engellenmiştir.", "personalizedMessage": "<PERSON><PERSON><PERSON><PERSON> {{userName}}, he<PERSON><PERSON><PERSON><PERSON><PERSON>z geçici olarak engellenmiştir.", "reason": "Bu işlem güvenlik veya politika nedenleriyle gerçekleştirilmiştir.", "contactAdmin": "Bunun bir hata olduğunu dü<PERSON>ü<PERSON>ü<PERSON>nız, lüt<PERSON> yardım için yöneticinizle iletişime geçin.", "supportInfo": "Acil destek için lütfen sistem yöneticinizle iletişime geçin.", "signOut": "Çıkış Yap", "accountSuspended": "Hesap Askıya Alındı", "temporaryRestriction": "Geçici Erişim Kı<PERSON>ıtlaması"}, "offline": {"title": "Çevrimdışısınız", "subtitle": "İnternet Bağlantısı Yok", "description": "<PERSON><PERSON> anda sunucuya ba<PERSON><PERSON>mıyoruz", "connectionRestored": "Bağlantı Geri Yüklendi", "connectionRestoredDescription": "İnternet bağlantınız geri yüklendi. Uygulama şimdi güncelleniyor.", "offlineDescription": "Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin. Çevrimdışıyken bazı özellikler kullanılamayabilir.", "troubleshootingTips": "<PERSON><PERSON> gider<PERSON> i<PERSON>çlar<PERSON>:", "checkConnection": "İnternet bağlantınızı kontrol edin", "tryDifferentNetwork": "Farklı bir ağa bağlanmayı deneyin", "restartRouter": "Mümkünse modeminizi yeniden başlatın", "checkOtherApps": "<PERSON><PERSON><PERSON> uygulamaların internete bağlanıp bağlanamadığını kontrol edin", "offlineMode": "Çevrimdışı Mod", "tryAgain": "<PERSON><PERSON><PERSON> dene", "refresh": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON>", "goBack": "<PERSON><PERSON>", "checkingConnection": "Bağlantı kontrol ediliyor...", "retryCount": "<PERSON><PERSON><PERSON>", "connectionStatus": "Bağlantı durumu", "online": "Çevrimiçi", "offline": "Çevrimdışı", "persistentIssues": "So<PERSON><PERSON> devam ederse, çevrimdışı modda bazı özellikler hala kullanılabilir. Bağlantınız düzeldiğinde daha sonra tekrar deneyebilirsiniz.", "noInternetAccess": "İnternet Erişimi Yok", "limitedFunctionality": "Çevrimdışıyken sınırlı işlevsellik mevcut"}, "biometrics": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Hesabınıza hızlı ve güvenli erişim için biyometrik kimlik doğrulamayı ayarlayın.", "authentication": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registered": "Kimlik doğrulaması için parmak izi veya yüz tanımayı kullanın", "notRegistered": "Kayıtlı değil - biyometrik kimlik doğrulamayı ayarlamak için tıklayın", "active": "Aktif", "register": "<PERSON><PERSON>", "remove": "Kaldır", "loading": "<PERSON><PERSON>yomet<PERSON>...", "notAvailable": "<PERSON><PERSON>yo<PERSON><PERSON>ğru<PERSON>a <PERSON>llanılamıyor", "notAvailableDesc": "Cihazınız veya tarayıcınız biyometrik kimlik doğrulamayı desteklemiyor veya HTTPS kullanmanız gerekiyor.", "requirements": "Gereksinimler:", "modernBrowser": "Modern tarayıcı (Chrome, Firefox, Safari, Edge)", "httpsConnection": "HTTPS bağlantısı (veya geliştirme için localhost)", "biometricDevice": "Parmak izi sensörü veya yüz tanıma özellikli cihaz", "webauthnSupport": "Tarayıcıda WebAuthn desteği etkin", "securityPrivacy": "Güvenlik ve Gizlilik", "dataStaysLocal": "Biyometrik verileriniz cihazınızda kalır", "encryptedStorage": "<PERSON><PERSON><PERSON>miş şablonlar güvenli bir şekilde <PERSON>ır", "removeAnytime": "Biyometrik er<PERSON><PERSON><PERSON><PERSON> istediğiniz zaman kaldırabilirsiniz", "pinBackup": "<PERSON><PERSON> yedekleme her zaman mevcuttur", "benefits": "<PERSON><PERSON><PERSON>", "quickAttendance": "Çok hızlı yoklama işaretleme", "enhancedSecurity": "Donanım destekli kimlik doğrulama ile gelişmiş güvenlik", "noPasswords": "Hatırlanacak şifre yok", "multipleDevices": "Parmak izi sensörleri ve yüz tanıma ile çalışır", "registerTitle": "Biyometrik <PERSON>lik Doğrulamayı Kaydet", "registerDescription": "Hı<PERSON><PERSON>ı ve güvenli erişim için parmak izi veya yüz tanımayı ayarlayın", "confirmDelete": "Biyometrik <PERSON>lik Doğrulamayı Kaldır", "confirmDeleteDesc": "Biyometrik kimlik doğrulamanızı kaldırmak istediğinizden emin misiniz? Yoklama doğrulaması için PIN'inizi kullanmanız gerekecek.", "registrationSuccess": "Biyometrik kayıt başarıyla tamamlandı!", "registrationFailed": "<PERSON><PERSON>yo<PERSON><PERSON>şarısız", "removalSuccess": "Biyometrik kimlik doğrulama başarıyla kaldırıldı", "removalError": "Biyometrik kayıt kaldırılamadı", "loadError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registerBiometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registerYourBiometric": "Biyometriğ<PERSON><PERSON>", "authenticateWithBiometric": "Biyometrik ile Kimlik Doğrulama", "setupFingerprintOrFace": "Hızlı eri<PERSON>im için parmak izi veya yüz tanımayı ayarlayın", "useRegisteredBiometric": "Kimlik doğrulaması için kayıtlı biyometriğinizi kullanın", "instructions": "Talimatlar:", "clickButtonToStart": "Başlamak için yukarıdaki düğmeye tıklayın", "followBrowserPrompt": "Tarayıcınızın biyometrik istemini takip edin", "useFingerprintOrFace": "Parmak izi sensörü veya yüz tanımayı kullanın", "completeWhenPrompted": "İstendiğinde işlemi tamamlayın", "dataStaysSecure": "Biyometrik verileriniz cihazınızda güvende kalır", "touchSensorMessage": "Parmak izi sensörünüze dokunun veya yüz tanımayı kullanın...", "registrationSuccessMessage": "Biyometrik kayıt başarıyla tamamlandı!", "authenticationSuccessMessage": "Biyometrik kimlik doğrulama başarılı!", "biometricAuthentication": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceNotSupported": "Cihazınız veya tarayıcınız biyometrik kimlik doğrulamayı desteklemiyor veya HTTPS kullanmanız gerekiyor", "deviceWithSensor": "Parmak izi sensörü veya yüz tanıma özellikli cihaz", "setupBiometric": "Hesabınıza hızlı ve güvenli erişim için biyometrik kimlik doğrulamayı ayarlayın", "useFingerprintOrFaceAuth": "Kimlik doğrulaması için parmak izi veya yüz tanımayı kullanın", "notRegisteredSetup": "Kayıtlı değil - biyometrik kimlik doğrulamayı ayarlamak için tıklayın", "securityAndPrivacy": "Güvenlik ve Gizlilik", "biometricDataNeverLeaves": "Biyometrik verileriniz cihazınızdan asla ayrılmaz", "onlyEncryptedTemplates": "Yalnızca şifrelenmiş şablonlar güvenli bir şekilde saklanır", "pinBackupAvailable": "<PERSON><PERSON> yedekleme her zaman mevcuttur", "benefitsOfBiometric": "B<PERSON>yometrik <PERSON> Doğrulamanın <PERSON>ı", "quickAttendanceMarking": "Sadece bir dokunuşla hızlı yoklama işaretleme", "noNeedToRemember": "PIN veya şifre hatırlamaya gerek yok", "worksWithSensors": "Parmak izi sensörleri ve yüz tanıma ile çalışır", "registerBiometricAuth": "Biyometrik <PERSON>lik Doğrulamayı Kaydet", "followPromptsToSetup": "Parmak izi veya yüz tanımayı ayarlamak için talimatları takip edin", "cancel": "İptal", "preparingAuthentication": "Biyometrik kimlik doğrulama hazırlanıyor...", "biometricSignInSuccessful": "✨ <PERSON><PERSON><PERSON><PERSON><PERSON>arılı", "welcomeBackSecure": "Tekrar hoş geldiniz! Güvenli bir şekilde giriş yaptınız.", "authenticationFailed": "Kimlik doğrulama başarısız", "biometricSignInFailed": "<PERSON><PERSON>yomet<PERSON>ız", "tryAgainOrUseEmail": "Lütfen tekrar deneyin veya e-posta/<PERSON><PERSON><PERSON> kullan<PERSON>n.", "notSupported": "Biyometrik kimlik doğrulama desteklenmiyor", "useEmailPassword": "Lütfen giriş yapmak için e-posta ve şifre kullanın", "biometricSignIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signInSecurely": "Parmak izi veya yüz tanıma ile güvenli giriş yapın", "authenticationAvailable": "Biyometrik kimlik doğrulama mevcut", "ready": "Hazır", "checkingAvailability": "Biyometrik kullanılabilirlik kontrol ediliyor...", "noBiometricSetup": "Biyometrik kimlik doğrulama ayarlanmamış", "signInWithBiometrics": "<PERSON><PERSON>yometrik <PERSON><PERSON>", "fast": "Hızlı", "secure": "<PERSON><PERSON><PERSON><PERSON>", "private": "<PERSON><PERSON>", "mustRegisterFirst": "Bu özelliği kullanmak için önce profil ayarlarınızdan biyometrik kaydı yapmalısınız.", "biometricOnlyTitle": "<PERSON><PERSON><PERSON>", "biometricOnlyDescription": "Güvenlik için sadece parmak izi, yüz tanıma veya diğer biyometrik yöntemlere izin verilir. PIN, desen ve şifre kimlik doğrulaması yetkisiz erişimi önlemek için devre dışı bırakılmıştır.", "biometricOnlyRequired": "<PERSON><PERSON><PERSON>", "biometricOnlyNotSupported": "Bu cihaz sadece biyometrik kimlik doğrulamayı desteklemiyor. Lütfen parmak izi, yüz tanıma veya diğer biyometrik sensörleri olan bir cihaz kullanın.", "biometricOnlyInstructions": "<PERSON><PERSON><PERSON>, y<PERSON>z tanıma veya diğer biyometrik yöntemleri kullanın. PIN/desen/şifreye izin verilmez.", "biometricOnlyFailed": "Biyometrik kimlik doğrulama başarısız. Lütfen sadece biyometrik yöntemleri (parmak izi, y<PERSON><PERSON>ı<PERSON>, vb.) kullandığınızdan emin olun.", "chooseHardwareBound": "PIN/desen yedeklerine izin veren bulut tabanlı depolama yerine donanım bağlı seçenekleri (Samsung Pass gibi) seçin.", "avoidCloudStorage": "PIN/desen yedeklerine izin veren Google Hesabı veya bulut depolama seçeneklerinden kaçının.", "hardwareBoundOnly": "Ma<PERSON><PERSON>um güvenlik için sadece donanım bağlı biyometrik kimlik doğrulama kullanın.", "strictBiometricOnlyRegister": "ÖNEMLİ: SADECE parmak izi, yüz tanıma veya diğer biyometrik yöntemleri kullanın. Gmail, PIN/desen ile Samsung Pass veya şifre seçenekleri görürseniz - bunları KULLANMAYIN. Kapatın ve sadece saf biyometrik kimlik doğrulama ile tekrar deneyin.", "strictBiometricOnlyAuth": "ÖNEMLİ: SADECE parmak izi, yüz tanıma veya diğer biyometrik yöntemleri kullanın. PIN/desen/şifre seçenekleri görürseniz - bunları KULLANMAYIN. Sadece biyometrik kimlik doğrulama kullanın.", "defaultDeletionReason": "Öğrenci güvenlik amaçlı biyometrik kimlik bilgilerinin silinmesini talep ediyor", "deletionRequestSubmitted": "<PERSON><PERSON><PERSON>", "deletionRequestSubmittedMessage": "Biyometrik silme talebiniz okul yöneticinize onay için <PERSON>.", "deletionRequestSubmissionError": "<PERSON><PERSON>e talebi gönderilemedi. Lütfen tekrar deneyin.", "requestDeletion": "<PERSON><PERSON><PERSON>", "refreshStatus": "<PERSON><PERSON><PERSON>", "deletionPendingTitle": "<PERSON><PERSON><PERSON>", "deletionPendingMessage": "Biyometrik silme talebiniz okul yöneticinizin onayını bekliyor.", "deletionApprovedTitle": "<PERSON><PERSON><PERSON>", "deletionApprovedMessage": "Okul yöneticiniz biyometrik silme talebinizi onayladı. Kimlik bilgileriniz kaldırıldı.", "deletionPending": "<PERSON><PERSON><PERSON>", "registrationError": "Biyometrik kayıt sırasında bir hata oluştu", "registering": "Kay<PERSON>ili<PERSON>r..."}, "auth": {"signIn": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON>", "forgotPassword": "<PERSON>if<PERSON>i <PERSON>uttum?", "resetPassword": "Şifreyi <PERSON>", "createAccount": "<PERSON><PERSON><PERSON>", "alreadyHaveAccount": "Zaten hesabınız var mı?", "dontHaveAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "signInHere": "<PERSON><PERSON><PERSON> giri<PERSON> ya<PERSON>ın", "signUpHere": "<PERSON><PERSON><PERSON> kayıt olun", "contactAdminForCode": "Davet kodu için okul yöneticinizle iletişime geçin", "enterCodeExactly": "<PERSON><PERSON>, tire veya özel karakterler dahil olmak üzere size verildiği şekilde girin", "invalidInvitationCode": "Girdiğiniz davet kodu yanlış. Lütfen kodu kontrol edin ve tekrar deneyin.", "expiredInvitationCode": "Bu davet kodunun süresi dolmuş. Lütfen yeni bir kod için okul yöneticinizle iletişime geçin.", "noInvitationCode": "Bu okulun aktif bir davet kodu yok. Lütfen okul yöneticinizle iletişime geçin.", "invalidSchool": "Seçilen okul geçersiz veya artık mevcut değil. Lütfen farklı bir okul seçin.", "emailAlreadyRegistered": "Bu e-posta adresi zaten kayıtlı. Lütfen farklı bir e-posta kullanın veya giriş yapmayı deneyin.", "passwordRequirements": "Şifre en az 8 karakter uzunluğunda olmalıdır", "passwordsDoNotMatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor", "invalidEmail": "Geçersiz e-posta adresi", "invalidCredentials": "Geçersiz e-posta veya şifre", "accountCreated": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> oluşturuldu", "verifyEmail": "Lütfen e-posta adresinizi doğrulayın", "resetPasswordEmailSent": "Şifre sıfırlama e-postası gönderildi", "passwordResetSuccess": "Şifre başarıyla sıfırlandı", "loginSuccess": "Başarıyla giriş yapıldı", "logoutSuccess": "Başarıyla çıkış yapıldı", "logoutError": "Çıkış yapılırken hata oluştu. Lütfen tekrar deneyin.", "sessionExpired": "Oturumunuz sona erdi. Lütfen tekrar giriş yapın.", "loginFailed": "G<PERSON>ş başarısız. Lütfen kimlik bilgilerinizi kontrol edin.", "networkError": "Ağ hatası. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.", "refreshPageConfirm": "Sayfayı yenileyip tekrar denemek ister misiniz?", "emailAndPassword": "E-posta ve Şifre", "biometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "welcomeBack": "<PERSON><PERSON><PERSON><PERSON>", "enterCredentials": "Hesabınıza erişmek için kimlik bilgilerinizi girin", "emailPlaceholder": "<EMAIL>", "yourEmail": "E-posta adresiniz", "loggingIn": "<PERSON><PERSON><PERSON> yapılıyor...", "login": "<PERSON><PERSON><PERSON>", "signedInSuccessfully": "Başarıyla giriş yaptınız.", "noAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "systemAdminSetup": "Sistem Yöneticisi Ku<PERSON>lumu", "createSystemAdminDescription": "Tam sistem erişimi olan bir sistem yöneticisi hesabı oluşturun", "noInvitationCodeRequired": "<PERSON><PERSON> kodu gere<PERSON>", "allFieldsRequired": "<PERSON><PERSON><PERSON> al<PERSON>", "passwordTooShort": "Şifre en az 8 karakter uzunluğunda olmalıdır", "fullNamePlaceholder": "Adınız Soyadınız", "adminEmailPlaceholder": "<EMAIL>", "systemAdminCode": "Sistem Yöneticisi Kodu", "systemAdminCodePlaceholder": "Sistem yöneticisi kodunu girin", "systemAdminCodeDescription": "Bu kod sistem yöneticisi hesabı oluşturmak için gereklidir", "creatingAccount": "<PERSON><PERSON><PERSON>...", "createSystemAdminAccount": "Sistem Yöneticisi Hesabı Oluştur", "alreadyHaveAccountLogin": "Zaten hesabınız var mı? Giriş Ya<PERSON>ın", "authenticating": "Doğrulanıyor...", "placeholders": {"yourName": "Adınız", "yourEmail": "E-posta adresiniz", "password": "Şifre", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON>", "adminEmail": "Yönetici e-postası", "systemAdminCode": "Sistem yönetici kodu", "invitationCode": "<PERSON><PERSON> kodu"}, "validationError": "Doğrulama Hatası", "pleaseSelectSchool": "Lütfen listeden bir okul seçin", "invitationCodeRequired": "<PERSON><PERSON><PERSON> i<PERSON>in davet kodu gereklidir", "checkInvitationCodeMessage": "Lütfen davet kodunuzu kontrol edin ve tekrar deneyin", "pleaseSelectValidSchool": "Lütfen listeden geçerli bir okul seçin", "trySigningInInstead": "Bu e-posta adresi zaten kayıtlı. Bunun yerine giriş yapmayı deneyin", "pleaseEnterValidEmail": "Lütfen geçerli bir e-posta adresi girin", "biometricFailed": "Biyometrik <PERSON>a Başarısız", "signupFailed": "Kayıt Başarısız"}, "settings": {"languageSettings": "<PERSON><PERSON>ı", "languageDescription": "Arayüz ve tüm içerik için tercih ettiğiniz dili seçin.", "currentLanguage": "Mevcut Dil", "active": "Aktif", "selectLanguage": "<PERSON><PERSON>", "languageInfo": "<PERSON><PERSON>", "interfaceLanguage": "Arayüz dilini anı<PERSON>tirir", "exportLanguage": "PDF ve CSV dışa aktarma içeriğini etkiler", "notificationLanguage": "<PERSON><PERSON><PERSON><PERSON>", "instantChange": "Yeniden başlatma gerekmez - değişiklikler anında uygulanır", "testLanguage": "<PERSON><PERSON>", "supportedLanguages": "<PERSON><PERSON><PERSON><PERSON>"}, "attendance": {"attendance": "<PERSON><PERSON><PERSON>", "present": "Mevcut", "absent": "Yok", "late": "Geç", "excused": "<PERSON><PERSON><PERSON>", "attendanceRecordingSettings": "Yo<PERSON>ma <PERSON> A<PERSON>ları", "controlAttendanceRecording": "Öğrencilerin yoklamalarını ne zaman kaydedebileceklerini kontrol edin", "attendanceTimeRestrictionInfo": "Yoklama kaydı için zaman kısıtlamaları ayarlamak, öğrencilerin yalnızca uygun ders saatlerinde yoklama kaydetmelerini sağlamaya ve ders saatleri dışında yetkisiz yoklama kaydını önlemeye yardımcı olur.", "attendanceTimeRestrictionsDescription": "Öğrenciler yoklamalarını yalnızca aşağıda belirttiğiniz zaman diliminde kaydedebileceklerdir. Bu saatlerin dışında, yoklama kaydı devre dışı bırakılacaktır.", "attendanceRecordingSettingsUpdated": "Yoklama kayıt zamanı ayarları başarıyla güncellendi.", "recordingTimeRestrictions": "Kayıt Zamanı Kısıtlamaları", "recordingStartTime": "Kayıt Başlangıç Saati", "recordingEndTime": "Kayıt Bitiş Saati", "saving": "Kay<PERSON>ili<PERSON>r...", "saveSettings": "Ayarları Kaydet", "settingsSaved": "<PERSON><PERSON><PERSON>", "status": {"unknown": "BİLİNMEYEN", "present": "Mevcut", "absent": "Yok", "late": "Geç", "excused": "<PERSON><PERSON><PERSON>", "allStatuses": "<PERSON><PERSON><PERSON>", "presentOnly": "<PERSON><PERSON><PERSON>", "absentOnly": "<PERSON><PERSON><PERSON>", "lateOnly": "Sadece Geç", "excusedOnly": "<PERSON><PERSON><PERSON>"}, "actions": {"markAsPresent": "<PERSON><PERSON><PERSON>", "markAsAbsent": "<PERSON><PERSON>ak <PERSON>aretle"}, "date": "<PERSON><PERSON><PERSON>", "time": "Saat", "block": "Blok", "room": "Sınıf", "scan": "Tara", "record": "<PERSON><PERSON>", "verify": "<PERSON><PERSON><PERSON><PERSON>", "verifyAttendance": "Yoklamayı Doğrula", "submit": "<PERSON><PERSON><PERSON>", "history": "Geçmiş", "report": "<PERSON><PERSON>", "export": {"title": "Yoklama Kayıtlarını Dışa Aktar", "description": "Seçilen tarih aralığı için yoklama kayıtlarını dışa aktarın", "dateRange": "<PERSON><PERSON><PERSON>", "filterByStatus": "<PERSON><PERSON><PERSON>", "recordsFound": "{{count}} ka<PERSON>ıt bulundu", "withStatus": "durum ile", "noRecordsForPeriod": "Seçilen tarih aralığı için kayıt bulunamadı. Farklı bir dönem seçmeyi deneyin.", "noStatusRecordsForPeriod": "Seçilen tarih aralığı için {{status}} durumunda kayıt bulunamadı. Farklı bir durum veya tarih aralığı seçmeyi deneyin.", "noRecordsAvailable": "Yoklama kaydı mevcut değil. Yoklama alındığında kayıtlar burada görünecektir.", "exportRecords": "Kayıtları Dışa Aktar", "selectDateRangeAndFormat": "<PERSON><PERSON><PERSON> a<PERSON>ığı ve dışa aktarma formatını seçin", "start": "Başlangıç", "end": "Bitiş", "filter": "Filtrele", "dateRangeRequired": "<PERSON><PERSON>h <PERSON>lığı Gerekli", "selectDateRange": "Lütfen bir başlangıç ve bitiş tarihi seçin", "selectDate": "<PERSON><PERSON><PERSON>", "noRecordsFound": "Kayıt Bulunamadı", "noRecordsDescription": "Seçilen tarih için devam kaydı bulunamadı", "noRecordsForDateRange": "Seçilen tarih aralığı için kayıt bulunamadı", "noStatusRecordsForDateRange": "Seçilen tarih a<PERSON>ığı için {{status}} durumunda kayıt bulunamadı", "chooseFormat": "Dışa Aktarma Formatını Seçin", "downloadCSV": "CSV Olarak İndir", "downloadPDF": "PDF Olarak <PERSON>ndir", "downloadHTML": "HTML Olarak İndir", "selectForExport": "Dışa Aktarma İçin Seç", "exportSuccessful": "Dışa Aktarma Başarılı", "reportExportedAs": "Rapor {{format}} olarak dışa aktarıldı", "exportFailed": "Dışa Aktarma Başarısız", "failedToGenerate": "<PERSON><PERSON>", "attendanceReport": "YOKLAMA RAPORU", "to": "ile", "generatedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "totalRecords": "Toplam Kayıt", "attendanceSummary": "YOKLAMA ÖZETİ", "reportInformation": "RAPOR BİLGİLERİ", "allDates": "<PERSON><PERSON><PERSON>", "reportDescription": "<PERSON><PERSON> rap<PERSON>, seç<PERSON>n tarih aralığı için öğrenci yoklama kayıtlarının bir özetini sağlar.", "systemName": "Campus Guardian - Yoklama Y<PERSON>", "notAvailable": "<PERSON><PERSON><PERSON>", "unknownStudent": "Bilinmeyen Öğrenci", "unknownRoom": "Bilinmeyen Oda", "by": "tarafından", "page": "Say<PERSON>", "of": "/", "generated": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "headers": {"dateTime": "<PERSON><PERSON><PERSON> ve <PERSON>", "studentName": "Öğrenci <PERSON>ı", "studentId": "Öğrenci No", "room": "Sınıf", "status": "Durum", "verification": "Doğrulama"}, "export": "Dışa Aktar", "exportAsHTML": "HTML olarak dışa aktar", "exportAsPDF": "PDF olarak dışa aktar", "exportAsCSV": "CSV olarak dışa aktar", "exportComplete": "Dışa Aktarma Tamamlandı", "exportSuccess": "{{count}} ka<PERSON>ıt ba<PERSON><PERSON><PERSON> {{format}} formatında dışa aktarıldı", "comprehensiveReport": "Kapsamlı Yoklama Yönetimi Raporu", "reportContains": "<PERSON>u rapor {{date}} ta<PERSON><PERSON> itiba<PERSON>yle {{count}} yoklama kaydı içermektedir", "manualByTeacher": "Öğret<PERSON>"}, "filter": "Filtrele", "dateRange": "<PERSON><PERSON><PERSON>", "startDate": "Başlangıç <PERSON>", "endDate": "Bitiş Tarihi", "apply": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Sıfırla", "noData": "Yoklama verisi bulunamadı", "recordSuccess": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "recordError": "<PERSON><PERSON><PERSON>", "verificationRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "locationVerification": "Konum <PERSON>", "biometricVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinVerification": "PIN Doğrulama", "excuses": "<PERSON><PERSON><PERSON><PERSON>", "attendanceHistory": "Yoklama Geçmişi", "yourRecordsForAllClasses": "<PERSON><PERSON><PERSON> dersler için yoklama kayıtlarınız", "noRecordsAvailable": "Kayıt Bulunamadı", "historyWillAppear": "Bir derse giriş yaptığınızda yoklama geçmişiniz burada görünecektir", "unknownRoom": "Bilinmeyen Sınıf", "unknownCourse": "Bilinmeyen Ders", "unknownBuilding": "Bilinmeyen <PERSON>", "manualVerification": "<PERSON>", "allTime": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON><PERSON>", "thisWeek": "<PERSON><PERSON><PERSON>", "thisMonth": "<PERSON><PERSON>", "filterBy": "Filtrele", "noAttendanceData": "Yo<PERSON><PERSON> verisi yok", "yourAttendanceHistoryWillAppearHere": "Yoklama geçmişiniz burada görünecek", "errorLoadingData": "<PERSON><PERSON><PERSON> hatası", "locationVerified": "Konum Doğrulandı", "deviceWeb": "Cihaz: Web"}, "dashboard": {"studentDashboard": "Öğrenci Paneli"}, "students": {"directory": {"title": "<PERSON>ğ<PERSON><PERSON>", "description": "Sistemdeki tüm öğrencileri görüntüleyin ve yönetin", "searchPlaceholder": "<PERSON><PERSON><PERSON>, ID veya e-posta ile arayın...", "switchToGridView": "Izgara Görünümüne Geç", "switchToListView": "Liste Görünümüne Geç", "selectBlock": "Blok Seçin", "allBlocks": "<PERSON><PERSON><PERSON>", "editBlock": "<PERSON><PERSON><PERSON><PERSON>", "deleteBlock": "<PERSON><PERSON><PERSON><PERSON>", "addNewBlock": "<PERSON><PERSON>", "selectRoom": "<PERSON><PERSON>", "allRooms": "<PERSON><PERSON><PERSON>", "editRoom": "Odayı Düzenle", "deleteRoom": "Odayı Sil", "addNewRoom": "<PERSON><PERSON>", "profile": "Profil", "name": "İsim", "studentId": "Öğren<PERSON>", "course": "<PERSON><PERSON>", "block": "Blok", "room": "<PERSON><PERSON>", "status": "Durum", "actions": "İşlemler", "notSet": "Ayarlanmadı", "notAssigned": "Atanmadı", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewDetails": "Detayları Görüntüle", "noMatchingStudents": "Arama kriterlerinize uyan öğrenci yok", "trySearchingBy": "<PERSON><PERSON><PERSON>, ID veya e-posta ile aramayı deneyin", "noStudentsAssignedTo": "Bu {{type}}a atanmış öğrenci yok", "noStudentsInSystem": "Sistemde öğrenci bulunamadı", "fetchBlocksRoomsError": "Blok ve odalar getirilemedi", "fetchRoomError": "Oda bilgisi getirilemedi", "loadError": "Öğrenci dizini yüklenemedi", "loadErrorTryAgain": "Öğrenci dizini yüklenemedi. Lütfen tekrar deneyin.", "editBlockTitle": "<PERSON><PERSON><PERSON><PERSON>", "editBlockDescription": "Blok adını güncelleyin. Bu, bu bloğa atanmış tüm öğrencileri etkileyecektir.", "blockName": "Blok Adı", "editRoomTitle": "Odayı Düzenle", "editRoomDescription": "Oda adını güncelleyin. Bu, bu odaya atanmış tüm öğrencileri etkileyecektir.", "roomName": "<PERSON><PERSON> Adı", "addNewBlockTitle": "<PERSON><PERSON>", "addNewBlockDescription": "Odaları ve öğrencileri düzenlemek için yeni bir blok oluşturun.", "enterBlockName": "Blok adını girin...", "addBlock": "Blok <PERSON>", "addNewRoomTitle": "<PERSON><PERSON>", "addNewRoomDescription": "<PERSON><PERSON><PERSON> bloğa yeni bir oda oluşturun.", "enterRoomName": "<PERSON>da adını girin...", "addRoom": "<PERSON><PERSON>", "failedToLoadInitialData": "İlk veriler yüklenemedi. Lütfen sayfayı yenileyin.", "dataLoaded": "<PERSON><PERSON><PERSON>", "loadedStudentsFromCache": "Önbellekten {{count}} öğ<PERSON><PERSON>", "cannotDeleteBlock": "Blok Silinemez", "blockHasStudents": "Bu bloğa atanmış öğrenciler var. Lütfen önce onları yeniden atayın.", "blockHasRooms": "Bu bloğa atanmış odalar var. Lütfen önce odaları silin veya yeniden atayın.", "blockDeleted": "Blok <PERSON>", "blockDeletedSuccess": "Blok başar<PERSON><PERSON>.", "failedToDeleteBlock": "Blok silinemedi. Atanmış oda veya öğrenci olmadığından emin olun.", "cannotDeleteRoom": "<PERSON><PERSON>", "roomHasStudents": "Bu odaya atanmış öğrenciler var. Lütfen önce onları yeniden atayın.", "roomHasAttendanceRecords": "Bu odanın yoklama kayıtları var. Lütfen önce arşivleyin veya yeniden atayın.", "roomDeleted": "<PERSON><PERSON>", "roomDeletedSuccess": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>.", "failedToDeleteRoom": "Oda silinemedi. Yoklama kaydı veya atanmış öğrenci olmadığından emin olun.", "blockUpdated": "Blok Güncellendi", "blockUpdatedSuccess": "Blok başarıyla güncellendi.", "failedToUpdateBlock": "Blok güncellenemedi. Lütfen tekrar deneyin.", "roomUpdated": "<PERSON><PERSON>", "roomUpdatedSuccess": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>.", "failedToUpdateRoom": "Oda güncellenemedi. Lütfen tekrar deneyin.", "blockNameCannotBeEmpty": "Blok adı boş olamaz", "blockAddedSuccess": "Blok başarıyla eklendi", "failedToAddBlock": "Blok eklenemedi. Lütfen tekrar deneyin.", "roomNameCannotBeEmpty": "<PERSON>da adı boş olamaz", "selectBlockToAddRoom": "Odayı eklemek için lütfen bir blok seçin", "roomAddedSuccess": "<PERSON>da başarıyla eklendi", "failedToAddRoom": "Oda eklenemedi. Lütfen tekrar deneyin."}, "profile": {"title": "Öğrenci Profili", "detailedInfo": "{{name}} hakkında detaylı bilgi", "studentId": "Öğren<PERSON>", "email": "E-posta", "course": "<PERSON><PERSON>", "block": "Blok", "room": "<PERSON><PERSON>", "biometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registered": "<PERSON><PERSON><PERSON><PERSON>", "notRegistered": "Kayıtlı Değil", "todaysAttendance": "Bugünkü Yoklama"}}, "profile": {"completeYourProfile": "Profili<PERSON><PERSON>", "completeProfileMessage": "Devam etmeden önce lütfen öğrenci profilinizi tamamlayın. Aşağıdaki tüm gerekli bilgileri doldurun.", "registerBiometrics": "Biyometrik ka<PERSON>ınızı yapın", "forFasterVerification": "daha hızlı yoklama doğrulaması için", "updateProfile": "<PERSON><PERSON>", "studentProfile": "Öğrenci Profili", "updateYourInformation": "Profil bilgiler<PERSON>zi gü<PERSON><PERSON>n", "viewAndManage": "Öğrenci profilinizi görüntüleyin ve yönetin", "fillRequiredFields": "Profil kurulumunuzu tamamlamak için lütfen tüm gerekli alanları doldurun.", "tabs": {"profile": "Profil", "biometrics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language": "Dil"}, "uploading": "Yükleniyor...", "changePhoto": "Fotoğrafı Değiştir", "fullName": "Tam Ad", "enterFullName": "Tam adınızı girin", "studentId": "Öğren<PERSON>", "enterStudentId": "Öğrenci numaranızı girin", "course": "Sınıf/Seviye", "enterCourse": "Sınıfınızı veya seviyenizi girin", "block": "Blok", "selectBlock": "Bir blok seçin", "room": "Sınıf", "selectRoom": "<PERSON><PERSON> sı<PERSON>ı<PERSON> se<PERSON>", "pin": "PIN", "enterPin": "6 haneli PIN girin", "pinDescription": "Bu PIN yedek doğrulama yöntemi olarak kullanılacaktır.", "school": "<PERSON><PERSON>", "selectSchool": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "schoolDescription": "<PERSON><PERSON> ettiğiniz okulu seçin.", "save": "Değişiklikleri Kaydet", "edit": "<PERSON><PERSON>", "registerBiometricAuth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biometricRegistered": "<PERSON><PERSON>yomet<PERSON>", "setupComplete": "<PERSON>il kuru<PERSON> ta<PERSON>ı", "setupCompleteMessage": "Profiliniz başarıyla kuruldu. Artık tüm özelliklere erişebilirsiniz.", "email": "E-posta", "biometricStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registered": "<PERSON><PERSON><PERSON><PERSON>", "notRegistered": "<PERSON>ıtlı değil", "registering": "Kay<PERSON>ili<PERSON>r...", "saving": "Kay<PERSON>ili<PERSON>r...", "completeSetup": "<PERSON><PERSON><PERSON><PERSON>", "loadingSchools": "<PERSON><PERSON><PERSON> yükleniyor...", "noSchoolsAvailable": "Okul bulunamadı", "fileTooLarge": "<PERSON><PERSON>a ç<PERSON> büyük", "selectSmallerImage": "Lütfen 5MB'den küçük bir resim seçin", "invalidFileType": "Geçersiz dosya türü", "selectImageFile": "Lütfen bir resim dosyası seçin", "photoUpdated": "<PERSON>oto<PERSON><PERSON><PERSON>", "photoUpdatedDesc": "Profil fotoğrafınız g<PERSON>", "uploadFailed": "Yükleme başarısız", "failedToUploadPhoto": "Profil fotoğrafı yüklenemedi", "setupCompleteDesc": "Profiliniz başarıyla kuruldu. Artık tüm özelliklere erişebilirsiniz.", "failedToUpdateProfile": "Profil güncellenemedi. Lütfen tekrar deneyin.", "noSchoolsFound": "Okul bulunamadı", "noSchoolsFoundDesc": "Henüz yöneticiler tarafından okul eklenmemiş. Okulunuzu eklemek için yöneticinizle iletişime geçin.", "failedToFetchSchools": "<PERSON><PERSON><PERSON>", "failedToFetchBlocks": "Bloklar yüklenemedi", "failedToFetchRooms": "<PERSON><PERSON><PERSON>", "profileUpdated": "<PERSON><PERSON>", "profileUpdatedDesc": "Profiliniz başarıyla güncellendi.", "editProfile": "<PERSON><PERSON>", "profileInformation": "<PERSON><PERSON>"}, "notifications": {"alerts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Yoklama durumunuz hakkında güncel kalın", "new": "<PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dismiss": "Ka<PERSON><PERSON>", "dismissing": "Kapatılıyor...", "noNotificationsYet": "<PERSON><PERSON><PERSON>z Bildirim <PERSON>", "emptyStateMessage": "<PERSON><PERSON>ma du<PERSON>, bi<PERSON><PERSON><PERSON> burada görü<PERSON>cek", "attendanceStatusChanged": "<PERSON><PERSON><PERSON>", "attendanceStatusChangedDescription": "{{studentName}} <PERSON><PERSON><PERSON><PERSON><PERSON>n yoklama durumunu {{status}} olarak <PERSON>ğiştirdiniz", "error": "<PERSON><PERSON>", "failedToLoad": "Bildir<PERSON><PERSON> yü<PERSON>", "failedToDelete": "<PERSON><PERSON><PERSON><PERSON>", "failedToClear": "Bil<PERSON><PERSON><PERSON> temizlenemedi", "dismissed": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "<PERSON><PERSON><PERSON><PERSON>", "allClear": "<PERSON><PERSON><PERSON><PERSON>", "allDeleted": "<PERSON><PERSON><PERSON> bi<PERSON> silindi", "markedPresent": "✅ <PERSON><PERSON><PERSON> Olarak <PERSON>aretlendi", "markedAbsent": "❌ <PERSON><PERSON> Olarak İşaretlendi", "markedLate": "⏰ <PERSON><PERSON><PERSON>ak İşaretlendi", "markedExcused": "📝 Ma<PERSON><PERSON><PERSON> Olarak İşaretlendi", "presentMessage": "{{room}} odasında {{teacher}} tarafından mevcut olarak işaretlendiniz", "absentMessage": "{{room}} odasında {{teacher}} tarafından yok olarak işaretlendiniz. Bu yanlışsa, lütfen öğretmeninizle iletişime geçin.", "lateMessage": "{{room}} o<PERSON><PERSON>na {{teacher}} tarafından geç olarak işaretlendiniz", "excusedMessage": "{{room}} odasındaki yokluğunuz {{teacher}} tarafından mazeretli olarak işaretlendi", "showUnread": "<PERSON><PERSON><PERSON> Göster", "soundOn": "Ses Açık", "soundOff": "<PERSON><PERSON>", "audioEnabled": "<PERSON><PERSON><PERSON> Bildirimler Etkinleştirildi", "audioEnabledDescription": "Artık yeni bildirimler için ses duyacaksınız", "audioDisabled": "Se<PERSON>li Bildirimler Devre Dışı", "audioDisabledDescription": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> kapatıldı", "pushNotSupported": "Push Bildirimleri Desteklenmiyor", "pushNotSupportedDescription": "Tarayıcınız push bildirimleri desteklemiyor", "pushPermissionDenied": "<PERSON><PERSON>dedildi", "pushPermissionDeniedDescription": "Lütfen tarayıcı ayarlarından bildirimlere izin verin", "timeAgo": {"seconds": "{{count}} saniye <PERSON>nce", "minutes": "{{count}} <PERSON><PERSON><PERSON>", "hours": "{{count}} saat önce", "days": "{{count}} gün <PERSON><PERSON>", "months": "{{count}} ay önce", "years": "{{count}} yıl <PERSON><PERSON>"}}, "carousel": {"previousSlide": "<PERSON><PERSON><PERSON> slayt", "nextSlide": "<PERSON><PERSON><PERSON> slayt", "swipe": "Kaydır"}, "footer": {"legal": "<PERSON><PERSON>", "home": "<PERSON>", "privacyPolicy": "Gizlilik Politikası", "termsOfService": "Kullanım Şartları", "cookiePolicy": "Çerez Politikası", "contactUs": "Bize Ulaşın", "visitOurWebsite": "Web Sitemizi <PERSON>", "connectWithUs": "Bize Bağlanın", "noSocialMediaLinks": "Sosyal medya bağlantısı yapılandırılmamış.", "allRightsReserved": "<PERSON><PERSON>m hakları saklıdır.", "developedWith": "<PERSON><PERSON><PERSON><PERSON>", "by": "tarafından"}, "feedback": {"feedback": "<PERSON><PERSON>", "sendFeedback": "<PERSON><PERSON>", "feedbackDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, önerilerinizi paylaşın veya sorunları bildirin.", "yourName": "Adınız", "yourEmail": "E-posta adresiniz", "yourPhone": "Telefon numaranız", "message": "<PERSON><PERSON>", "yourFeedback": "<PERSON><PERSON>, önerileriniz veya sorunlarınız...", "emailDescription": "Gerekirse sizinle iletişim kurmak için kullanacağız.", "submitting": "Gönderiliyor...", "submitFeedback": "<PERSON><PERSON>", "thankYou": "Teşekkürler!", "feedbackSubmitted": "<PERSON><PERSON>ld<PERSON><PERSON> başar<PERSON><PERSON> gö<PERSON>ildi.", "feedbackSubmissionError": "<PERSON><PERSON> bildirim gönderilemedi. Lütfen tekrar deneyin.", "unexpectedError": "Beklenmeyen bir hata oluş<PERSON>. Lütfen tekrar deneyin."}, "excuses": {"attendanceExcuses": "Devamsızlık Mazeretleri", "requestExcusesDescription": "Devamsızlıklarınız için mazeret talep edin ve durumlarını görüntüleyin.", "newRequest": "<PERSON><PERSON>", "pending": "Beklemede", "approved": "Onaylandı", "rejected": "Reddedildi", "submissionNotAvailable": "<PERSON><PERSON><PERSON><PERSON>", "submissionTimeRestriction": "Mazeret talepleri sadece {{startTime}} ile {{endTime}} arasında gönderilebilir. Şu anki saat: {{currentTime}}.", "pendingExcuseExists": "<PERSON><PERSON><PERSON> Ma<PERSON>et Mevcut", "pendingExcuseMessage": "Zaten bekleyen bir mazeret talebiniz var. Yeni bir tane göndermeden önce mevcut olanı silmelisiniz. Mevcut talebinizi yönetmek için \"Beklemede\" sek<PERSON><PERSON> gidin.", "room": "<PERSON><PERSON>", "selectRoom": "<PERSON><PERSON> oda seçin", "selectRoomDescription": "Mazeret talep edeceğiniz odayı seçin", "startDate": "Başlangıç <PERSON>", "endDate": "Bitiş Tarihi", "startTime": "Başlangıç <PERSON>", "endTime": "Bitiş Saati", "reason": "Sebep", "submit": "<PERSON><PERSON><PERSON>", "delete": "Sil", "cancel": "İptal", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeleteDescription": "Bu mazeret talebini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "dateTooFarInAdvance": "<PERSON><PERSON>h <PERSON> İleride", "maxDaysInAdvance": "Mazeret talepleri en fazla {{days}} gün önceden gönderilebilir.", "durationTooLong": "Mazeret Süresi Çok Uzun", "maxExcuseDuration": "Ma<PERSON>et talepleri {{days}} g<PERSON><PERSON><PERSON>.", "failedToSubmit": "Ma<PERSON>et talebi gönderilemedi.", "noPendingExcuses": "<PERSON><PERSON><PERSON> mazeret yok", "noPendingExcusesDescription": "<PERSON><PERSON><PERSON> mazeret tale<PERSON> bulu<PERSON>maktadır", "noApprovedExcuses": "Onaylanmış mazeret yok", "noApprovedExcusesDescription": "Onaylanmış mazeret talebiniz bulunmamaktadır", "noRejectedExcuses": "Reddedilmiş mazeret yok", "noRejectedExcusesDescription": "Reddedilmiş mazeret talebiniz bulunmamaktadır", "duration": "<PERSON><PERSON><PERSON>", "teacherNotes": "Öğretmen Notları", "unknown": "Bilinmiyor", "deletePendingFirst": "Önce Bekleyen Mazereti Silin", "submitRequest": "Mazeret Tale<PERSON> Gö<PERSON>", "submitting": "Gönderiliyor...", "submitted": "<PERSON><PERSON>", "submittedDescription": "Mazeret talebiniz başarıyla gönderildi ve şu anda inceleme bekliyor", "submitError": "<PERSON><PERSON>et talebi gö<PERSON>medi", "deleted": "<PERSON><PERSON>", "deletedDescription": "<PERSON><PERSON><PERSON> talebiniz ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "deleteError": "<PERSON><PERSON><PERSON> talebi si<PERSON>i", "pickADate": "<PERSON><PERSON> tarih se<PERSON>", "firstDayOfAbsence": "İlk devamsızlık günü", "lastDayOfAbsence": "Son deva<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> günü", "startTimePlaceholder": "Başlangıç <PERSON>", "endTimePlaceholder": "Bitiş <PERSON>ati", "reasonPlaceholder": "Lütfen devamsızlığınızın sebebini açıklayın...", "reasonDescription": "Devamsızlığınız için detaylı bir açıklama sağlayın", "roomLabel": "<PERSON><PERSON>", "pleaseSelectRoom": "Lütfen bir oda seçin", "pleaseSelectStartDate": "Lütfen başlangıç tarihi se<PERSON>in", "pleaseSelectEndDate": "Lütfen bitiş tarihi se<PERSON>", "pleaseEnterStartTime": "Lütfen başlangıç saati girin", "pleaseEnterEndTime": "Lütfen bitiş saati girin", "pleaseProvideReason": "Lütfen bir sebep belirtin", "reasonMinLength": "Sebep en az 10 karakter olmalıdır", "reasonMaxLength": "Sebep 500 karakteri geçemez", "endDateBeforeStartDate": "Bitiş tarihi ba<PERSON>langıç tarihinden önce olamaz", "errorFetchingRooms": "<PERSON><PERSON>", "failedToLoadRooms": "Odalar yüklenemedi. Lütfen tekrar deneyin."}, "qrScanner": {"noRoomAvailable": "Sınıf Bulunamadı", "noRoomsSetup": "Okulunuz için henü<PERSON> sını<PERSON> oluşturulmamış. Yine de yoklama kaydedebilirsiniz.", "attendanceRecordingNotAvailable": "Yoklama Kaydı Mevcut Değil", "recordingTimeRestriction": "Yo<PERSON>ma sadece {{startTime}} ile {{endTime}} arasında kaydedilebilir. Şu anki saat: {{currentTime}}.", "biometricVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verifyIdentity": "Lütfen parmak izi veya yüz tanıma ile kimliğinizi doğrulayın", "userProfileNotFound": "Kullanıcı profili bulunamadı", "enterPin": "Lütfen PIN kodunuzu girin", "incorrectPin": "Yanlış PIN kodu. Lütfen tekrar deneyin.", "biometricNotSupported": "<PERSON><PERSON>yo<PERSON><PERSON> bu cihazda desteklenmiyor", "pleaseWait": "Lütfen Bekleyin", "loadingRoomData": "Oda bilgileriniz yü<PERSON>niyor...", "scanQrCode": "QR Kod <PERSON>", "scanQrDescription": "Odanızda gösterilen QR kodu tarayın", "scanQrDescriptionFull": "Odanızda gösterilen QR kodu tarayarak yoklamanızı kaydedin", "startScan": "Taramayı Başlat", "checkingCameraPermissions": "Kamera izinleri kontrol ediliyor...", "readyToScanFor": "<PERSON><PERSON><PERSON> hazı<PERSON>:", "scanningQrCode": "QR kodu taranıyor...", "verifyingLocation": "Konumunuz doğrulanıyor...", "processingAttendance": "Yoklama işleniyor...", "allowLocationAccess": "Lütfen istenirse konum erişimine izin verin", "verifyToRecord": "Yoklama kaydetmek için lütfen kimliğinizi doğrulayın", "enterYourPin": "PIN kodunuzu girin", "verifyWithPin": "PIN ile Doğrula", "useBiometricVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setupPinRequired": "Yoklama kaydetmeden önce bir PIN oluşturmanız gerekiyor. Lütfen profilinizi güncelleyerek bir PIN ekleyin.", "noRoomInfo": "Oda bilgisi bulunamadı. Lütfen profilinizin blok ve oda numarası ile tamamlandığından emin olun.", "biometricVerificationFailed": "Biyometrik doğrulama başarısız oldu. Lütfen tekrar deneyin veya PIN kullanın.", "geolocationNotSupported": "Konum hizmeti tarayıcınız tarafından desteklenmiyor", "verifyPin": "PIN Doğrula", "verify": "<PERSON><PERSON><PERSON><PERSON>", "useBiometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attendanceRecorded": "<PERSON><PERSON><PERSON>", "attendanceSuccess": "Yoklamanız başarıyla kaydedildi", "alreadyPresent": "Zaten Mevcut", "alreadyRecorded": "<PERSON>u <PERSON>s i<PERSON>in yoklamanız zaten kaydedilmiş", "viewHistory": "Geçmişi Görüntüle", "locationVerification": "Konum <PERSON>", "locationVerificationDisabled": "Bu oda için konum doğrulama devre dışı.", "blockVerificationDisabled": "Bu oda için blok seviyesi doğrulama devre dışı.", "verifyingRoomLevel": "Doğru odada olduğunuz doğrulanıyor...", "verifyingBlockLevel": "<PERSON><PERSON><PERSON> binada olduğunuz doğrulanıyor...", "locationAccessFailed": "Konumunuza erişilemedi. Lütfen cihaz ayarlarınızı kontrol edin.", "locationVerificationFailed": "Konum Doğrulama Başarısız", "mustBeInRoom": "Yoklama kaydetmek için odanızda veya yakınında olmalısınız.", "userNotAuthenticated": "Kullanıcı kimliği doğrulanmadı. Lütfen tekrar giriş yapın.", "failedToCheckAttendance": "Yoklama durumu kontrol edilemedi. Lütfen tekrar deneyin.", "attendanceRecordedSuccessfully": "Yoklamanız başarıyla kaydedildi.", "failedToRecordAttendance": "Yoklama kaydedilemedi. Lütfen tekrar deneyin.", "alreadyRecordedToday": "{{room}} i<PERSON><PERSON> {{time}} saatinde zaten kaydedilmiş.", "alreadyMarkedPresent": "Bugünkü dersiniz için {{time}} saatinde zaten mevcut olarak işaretlenmişsiniz", "attendanceSuccessfullyRecorded": "Yoklamanız başarıyla kaydedildi", "earlierToday": "<PERSON><PERSON><PERSON>", "scanAnotherCode": "Başka Kod Tara", "cameraPermissionRequired": "Kamera İzni Gerekli", "cameraPermissionDescription": "QR kodları taramak için kameranıza erişim izni gerekiyor. Lütfen izin vermek için aşağıdaki butona tıklayın.", "allowCameraAccess": "Ka<PERSON>a <PERSON>", "cameraAccessGranted": "Kamera eriş<PERSON>i verildi. QR kodları taramaya hazır!", "pointCameraAtQr": "Kamerayı QR koduna doğrultun", "recordingAttendance": "Yoklamanız kaydediliyor...", "requestingCameraPermission": "<PERSON><PERSON>...", "startingCamera": "Kamera başlatılıyor...", "requestingPermission": "<PERSON>zin isteniyor...", "chooseVerificationMethod": "Doğrulama yöntemini seçin:", "loadingVerificationSettings": "Doğrulama ayarları yükleniyor...", "locationPermissionRequired": "Konum Erişimi Gerekli", "locationPermissionRequiredDescription": "Bu oda için konum doğrulama etkinleştirilmiş. Yoklama kaydı için konum erişimine izin vermelisiniz.", "enableLocation": "Konum Erişimini <PERSON>ştir", "biometricAuthenticationRequired": "B<PERSON>yometrik <PERSON> Doğrulama Gerekli", "biometricOnlyRequiredDescription": "Bu okul sadece biyometrik kimlik doğrulama gerektiriyor. Biyometrik doğrulama tamamlamadan yoklama kaydı yapamazsınız.", "biometricRequiredForBothDescription": "Bu okul hem biyometrik hem de PIN doğrulama gerektiriyor. Önce biyometrik kimlik doğrulamayı tamamlamalısınız.", "retryBiometric": "Biyometrik <PERSON>lik Doğrulamayı Tekrar Dene", "authenticating": "Doğrulanıyor...", "biometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pin": "PIN", "back": "<PERSON><PERSON>", "tryAgain": "<PERSON><PERSON><PERSON>", "qr": {"pinNotSet": "PIN ayarlanmamış", "pinNotSetDescription": "Lütfen profil ayarlarınızdan PIN kodunuzu belirleyin", "invalidPin": "Geçersiz PIN", "invalidPinDescription": "Girdiğiniz PIN kodu yanlış", "alreadyRecorded": "<PERSON><PERSON><PERSON>", "alreadyRecordedDescription": "{{roomName}} i<PERSON><PERSON> {{time}} saatinde zaten kaydedilmiş", "processing": "İşleniyor", "processingDescription": "Yoklamanız kaydediliyor...", "pinProcessingError": "PIN kodunuz işlenirken hata oluştu. Lütfen tekrar deneyin.", "locationWarning": "Konum Uyarısı", "locationWarningDescription": "Odanızdan {{distance}} uzaktasınız, bu da {{allowedRadius}}m izin verilen yarıçapın {{excessDistance}}m dışında. Öğretmeniniz bilgilendirilecek.", "recordAttendanceError": "Yoklama kaydedilemedi. Lütfen tekrar deneyin.", "attendanceRecorded": "Yoklamanız başarıyla kaydedildi", "checkingLocation": "Konum Kontrol <PERSON>r", "checkingLocationDescription": "Konumunuz alınıyor... Bu birkaç saniye sürebilir", "locationFound": "Konum Bulundu", "concurrentSessionWarning": "Başka bir aktif oturum tespit ettik. Öğretmeniniz bu olağandışı aktivite hakkında bilgilendirilecek.", "deviceChangeWarning": "Bugün daha önce farklı bir cihaz kullanıyordunuz. Öğretmeniniz bilgilendirilecek.", "attendanceError": "Yo<PERSON>ma <PERSON>"}, "roomValidation": {"perfectMatch": "Hoş geldiniz! Atanmış odanızdan ({{roomName}}) tarama yapıyorsunuz.", "blockMatch": "{{blockName}} bloğundasınız ancak atanmış odanızda değilsiniz. Yoklama yine de kaydedilecek.", "wrongRoom": "Bu QR kod {{blockName}} - {{roomName}} için. Lütfen atanmış odanızdaki QR kodu tarayın."}, "errors": {"cameraPermissionRequired": "QR kod taramak için kamera izni gereklidir. Lütfen kamera erişimine izin verin ve tekrar deneyin.", "videoElementNotFound": "Video öğesi bulunamadı. Lütfen tekrar deneyin.", "cameraPermissionDenied": "Kamera izni reddedildi. Lütfen tarayıcı ayarlarınızda kamera erişimine izin verin ve tekrar deneyin.", "noCameraFound": "Kamera bulunamadı. Lütfen cihazınızda kamera olduğundan emin olun.", "cameraAccessDenied": "Kamera er<PERSON><PERSON><PERSON> red<PERSON>. Lütfen kamera izinlerine izin verin ve tekrar deneyin.", "noCameraDevice": "Bu cihazda kamera cihazı bulunamadı.", "cameraInUse": "Kamera zaten başka bir uygulama tarafından kullanılıyor.", "emptyQrCode": "Boş QR kod verisi", "urlQrCode": "Bu bir URL QR kodu gibi görü<PERSON>ü<PERSON>r, yoklama QR kodu değil.", "wifiQrCode": "Bu bir WiFi QR kodu gibi görü<PERSON>ü<PERSON>r, yoklama QR kodu değil.", "qrCodeTooShort": "QR kod verisi çok kısa. Lütfen geçerli bir yoklama QR kodu tarayın.", "qrCodeTooLong": "QR kod verisi çok uzun. Lütfen geçerli bir yoklama QR kodu tarayın.", "invalidQrFormat": "Geçersiz QR kod formatı. Lütfen sistem tarafından oluşturulan geçerli bir yoklama QR kodu tarayın.", "invalidQrStructure": "Geçersiz QR kod yapısı", "missingQrFields": "Geçersiz QR kod: eksik {{fields}}", "qrCodeExpired": "Bu QR kodun süresi dolmuş. Lütfen yeni bir kod tarayın.", "qrCodeTooOld": "Bu QR kod çok eski ve artık geçerli değil. Lütfen yeni bir kod tarayın.", "possibleScreenshotAttack": "Bu QR kod bir ekran görüntüsü veya fotoğraftan alınmış gibi görünüyor. Lütfen QR kodu doğrudan ekrandan tarayın.", "qrCodeFutureTimestamp": "Bu QR kodun geçersiz bir zaman damgası var. Lütfen yeni bir kod tarayın.", "userAuthRequired": "QR doğrulaması için kullanıcı kimlik doğrulaması gerekli", "qrCodeReplaced": "Bu QR kod artık aktif değil. Yeni bir QR kod oluşturulmuş olabilir. Lütfen tablette görüntülenen en son QR kodu taradığınızdan emin olun.", "biometricRequired": "Biyometrik kimlik doğrulama gerekli", "pinRequired": "PIN doğrulama gerekli", "bothMethodsRequired": "<PERSON><PERSON> biyomet<PERSON> de PIN doğrulama gerekli", "bothMethodsDescription": "Yoklama kaydı için hem biyometrik kimlik doğrulama hem de PIN doğrulama tamamlamanız gerekir.", "startWithBiometric": "Biyometrik kimlik doğrulama ile başlayın", "biometricCompleted": "Biyometrik kimlik doğrulama tamamlandı", "nowEnterPin": "Doğrulamayı tamamlamak için şimdi PIN'inizi girin", "completeVerification": "Doğrulamayı Tamamla", "differentSchool": "Bu QR kod farklı bir okul için.", "roomNotFound": "<PERSON><PERSON> bulunamadı", "roomValidationFailed": "Oda ataması doğrulanamadı", "rateLimited": "Çok fazla yoklama denemesi yaptınız. Lütfen birkaç dakika sonra tekrar deneyin.", "locationTooFar": "Odanızdan {{distance}} uzaktasınız, bu da {{allowedRadius}}m izin verilen yarıçapın {{excessDistance}}m dışında. Lütfen odaya yaklaşın ve tekrar deneyin.", "locationTimeout": "İstek zaman aşımına uğradı. Lütfen internet bağlantınızı kontrol edin.", "locationUnavailable": "Konum bilgisi mevcut değil. Lütfen GPS'in etkin olduğundan emin olun.", "locationPermissionDenied": "Konum izni reddedildi. Lütfen konum erişimini etkinleştirin.", "locationGenericError": "Lütfen tekrar deneyin."}, "toasts": {"locationAccessRequired": "Konum Erişimi Gerekli", "locationAccessDescription": "Yoklama kaydetmek için lütfen tarayıcı ayarlarınızda konum erişimini etkinleştirin.", "scanError": "<PERSON><PERSON>", "biometricNotSupported": "Bu cihazda biyometrik kimlik doğrulama desteklenmiyor veya HTTPS gerekiyor", "noBiometricCredentials": "Biyometrik kimlik bilgisi bulunamadı. Lütfen önce biyometriklerinizi kaydedin.", "biometricAuthentication": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biometricAuthDescription": "Parmak izi sensörünüze dokunun veya yüz tanımayı kullanın...", "biometricAuthSuccess": "Biyometrik <PERSON>lik Doğrulama Başarılı", "biometricAuthSuccessDescription": "Kimlik doğrulama başarıyla tamamlandı", "biometricAuthFailed": "Biyometrik <PERSON>lik Doğrulama Başarısız", "pinAlsoRequired": "PIN doğrulama da gerekli", "pinAlsoRequiredDescription": "Doğrulama sürecini tamamlamak için lütfen PIN'inizi girin.", "locationPermissionGranted": "<PERSON><PERSON> er<PERSON><PERSON> verildi", "locationPermissionGrantedDescription": "Artık yoklama kaydına devam edebilirsiniz.", "locationPermissionStillDenied": "<PERSON><PERSON> er<PERSON><PERSON><PERSON>i hala red<PERSON>", "locationPermissionStillDeniedDescription": "Yoklama kaydı için lütfen tarayıcı ayarlarınızdan konum erişimine izin verin.", "locationError": "Konum hatası", "locationErrorDescription": "Konumunuza erişirken bir hata oluştu. Lütfen tekrar deneyin.", "cameraPermissionGranted": "<PERSON><PERSON><PERSON> er<PERSON><PERSON> ve<PERSON>", "cameraPermissionGrantedDescription": "Artık QR kod taramaya başlayabilirsiniz.", "cameraPermissionStillDenied": "<PERSON><PERSON><PERSON> er<PERSON><PERSON> hala red<PERSON>", "cameraPermissionStillDeniedDescription": "QR kod taramak için lütfen tarayıcı ayarlarınızdan kamera erişimine izin verin.", "cameraError": "<PERSON><PERSON><PERSON> hatası", "cameraErrorDescription": "Kameranıza erişirken bir hata oluştu. Lütfen tekrar deneyin.", "biometricRequiredBlocked": "Biyometrik kimlik doğrulama gerekli", "biometricRequiredBlockedDescription": "Bu okul sadece biyometrik kimlik doğrulama gerektiriyor. Biyometrik doğrulama tamamlamadan yoklama kaydı yapamazsınız.", "biometricRequiredForBoth": "Biyometrik kimlik doğrulama gerekli", "biometricRequiredForBothDescription": "Bu okul hem biyometrik hem de PIN doğrulama gerektiriyor. Önce biyometrik kimlik doğrulamayı tamamlamalısınız.", "alreadyPresent": "Zaten Mevcut", "alreadyPresentDescription": "Yoklamanız {{time}} saatinde zaten kaydedildi", "attendanceRecorded": "<PERSON><PERSON><PERSON>", "attendanceRecordedDescription": "Yoklamanız başarıyla kaydedildi!", "error": "<PERSON><PERSON>", "errorDescription": "Yoklama kaydedilemedi. Lütfen tekrar deneyin.", "locationWarning": "Konum Uyarısı", "locationWarningDescription": "Odanızdan {{distance}} uzaktasınız, bu da {{allowedRadius}}m izin verilen yarıçapın {{excessDistance}}m dışında. Öğretmeniniz bilgilendirilecek."}, "validation": {"enterPin": "Lütfen PIN kodunuzu girin", "noPinSet": "Profilinizde PIN ayarlanmamış. Lütfen profilinizi güncelleyin.", "incorrectPin": "Yanlış PIN. Lütfen tekrar deneyin."}, "success": {"alreadyPresent": "Zaten Mevcut", "alreadyPresentDescription": "Yoklamanız bugün için zaten kaydedildi", "attendanceRecorded": "<PERSON><PERSON><PERSON>", "attendanceRecordedDescription": "Yoklamanız başarıyla kaydedildi", "scanAnotherCode": "Başka QR Kod Tara"}, "locationInstructions": {"chrome": {"step1": "<PERSON><PERSON>uğundaki kilit simgesine 🔒 tıklayın", "step2": "\"Site ayarları\"na tıklayın", "step3": "\"Konum\"u bulun ve \"İzin ver\" o<PERSON><PERSON>ğiştirin", "step4": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> yeni<PERSON>in"}, "edge": {"step1": "<PERSON><PERSON>uğundaki kilit simgesine 🔒 tıklayın", "step2": "\"Site izinleri\"ne tıklayın", "step3": "\"Konum\"u bulun ve \"İzin ver\" o<PERSON><PERSON>ğiştirin", "step4": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> yeni<PERSON>in"}, "firefox": {"step1": "<PERSON><PERSON>uğundaki kilit simgesine 🔒 tıklayın", "step2": "\"Daha Fazla Bilgi\"ye tıklayın", "step3": "\"İzinler\"e gidin", "step4": "\"Konumunuza Erişim\"i bulun ve ayarı kaldırın veya \"İzin ver\" olarak ayarlayın", "step5": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> yeni<PERSON>in"}, "safari": {"step1": "Safari menüsü → <PERSON><PERSON><PERSON><PERSON>'<PERSON> tıklayın", "step2": "\"Gizlilik ve Güvenlik\"e gidin", "step3": "\"Web sitesi takibi\" altında \"Web Sitesi Ayarlarını Yönet\"e tıklayın", "step4": "Bu web sitesini bulun ve Konum'u \"İzin ver\" olarak ayarlayın", "step5": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> yeni<PERSON>in"}, "generic": {"step1": "Tarayıcınızın adres çubuğunda kilit simgesi 🔒 veya site ayarlarını arayın", "step2": "Konum izni ayarlarını bulun", "step3": "Bu site için konum erişimine izin verin", "step4": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> yeni<PERSON>in"}}, "unknownRoom": "Bilinmeyen Oda", "defaultRoom": "Varsayılan Oda", "securityAlerts": {"multipleActiveSessions": "Çoklu Aktif <PERSON>", "multipleActiveSessionsDescription": "Öğrencinin aynı anda ç<PERSON>ışan birden fazla aktif yoklama oturumu var.", "suspiciousLocationData": "Şüpheli Konum Verisi", "suspiciousLocationDataDescription": "Öğrencinin konum verisi yapay olarak oluşturulmuş veya sahte görünüyor.", "alertTitle": "🚨 {{title}}", "alertMessage": "Güvenlik Uyarısı: {{studentName}} - {{message}}"}, "securityNotice": {"title": "Güvenlik Bildirimi:", "unusualLocationData": "Olağandışı konum verisi tespit edildi", "teacherNotified": "Öğretmeniniz bilgilendirildi."}, "notifications": {"attendanceRecorded": "✅ <PERSON><PERSON><PERSON>", "attendanceRecordedMessage": "{{roomName}} odasına {{time}} saatinde başarıyla giriş yaptınız."}, "distanceAlerts": {"title": "<PERSON>fe <PERSON>ı", "loading": "Uyarılar yükleniyor...", "noAlerts": "Gösterilecek mesafe uyarısı yok", "unread": "Okunmamış", "room": "<PERSON><PERSON>", "viewLocation": "<PERSON><PERSON><PERSON>", "markAsRead": "<PERSON><PERSON><PERSON>ak <PERSON>aretle", "newAlert": "Yeni Mesafe Uyarısı", "fetchFailed": "Mesafe uyarıları alınamadı", "markReadFailed": "Uyarı okundu olarak işaretlenemedi", "deleteSuccess": "Uyarı ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "deleteFailed": "Uyarı silinemedi"}}, "teacher": {"settings": {"locationSettings": "Konum <PERSON>ları", "locationSettingsDescription": "Yoklama doğrulaması için konum ayarlarını yapılandırın", "blockLocations": "Blok Konumları", "roomLocations": "Oda Konumları", "verificationSettings": "Doğrulama Ayarları", "selectBlock": "Blok Seçin", "selectBlockPlaceholder": "Bir blok seçin", "selectBlockFirst": "Önce bir blok seçin", "allBlocks": "<PERSON><PERSON><PERSON>", "noRoomsInBlock": "Bu blokta oda yok", "showingRoomsFromBlock": "{{blockName}} Bloğundan {{count}} oda gösteriliyor", "showingAllRooms": "Toplam {{count}} oda gösteriliyor", "blockLocationInfo": "Bir blok için konum ayarı, o bloktaki tüm odalara uygulanacaktır. Bu, tek tek oda konumlarını ayarlamaktan daha verimli olduğu için çoğu durumda önerilir.", "selectRoom": "<PERSON><PERSON>", "selectRoomPlaceholder": "<PERSON><PERSON> oda seçin", "unknownBuilding": "Bilinmeyen <PERSON>", "roomLocationInfo": "Odaya özel konumlar, blok konumlarını geçersiz kılar. Sadece blok varsayılanından farklı ayarlara ihtiyacınız varsa oda konumlarını ayarlayın.", "configureVerification": "Konum Doğrulamayı Yapılandır", "verificationDescription": "Öğrencilerin yoklama kaydederken konumlarını doğrulamaları gerekip gerekmediğini kontrol edin", "verificationMethodSettings": "Doğrulama Yöntemi Ayarları", "verificationMethodDescription": "Öğrencilerin yoklama kaydederken hangi kimlik doğrulama yöntemlerini kullanması gerektiğini yapılandırın.", "selectVerificationMethod": "Doğrulama Yöntemi Gereksinimini Seçin", "verificationEither": "Biyometrik veya PIN", "verificationEitherDescription": "Öğrenciler biyometrik kimlik doğrulama veya PIN doğrulama arasında seçim yapabilir. En esnek seçenek.", "verificationBiometricOnly": "<PERSON><PERSON><PERSON>", "verificationBiometricOnlyDescription": "Öğrenciler biyometrik kimlik doğrulama (parmak izi, yüz tanıma vb.) kullanmalıdır. En yüksek güvenlik ancak uyumlu cihazlar gerektirir.", "verificationPinOnly": "Sadece PIN Doğrulama", "verificationPinOnlyDescription": "Öğrenciler PIN doğrulama kullanmalıdır. Tüm cihazlarda çalışan en uyumlu seçenek.", "verificationBothRequired": "<PERSON><PERSON> Biyomet<PERSON> de PIN Gerekli", "verificationBothRequiredDescription": "Öğrenciler hem biyometrik kimlik doğrulama HEM de PIN doğrulama tamamlamalıdır. Maksimum güvenlik ancak yoklama sürecini yavaşlatabilir.", "recommended": "Önerilen", "highSecurity": "Yüksek Güvenlik", "compatible": "<PERSON><PERSON><PERSON><PERSON>", "maximumSecurity": "Ma<PERSON><PERSON>um Güvenlik", "biometricOptional": "Biyometrik İsteğe Bağlı", "pinOptional": "PIN İsteğe Bağlı", "biometricRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinRequired": "PIN <PERSON>", "verificationMethodInfo": "Bu ayarlar okulunuzdaki tüm öğrenciler için geçerlidir. Değişiklikler yeni yoklama kayıtları için hemen etkili olacaktır.", "verificationMethodSettingsSaved": "Doğrulama yöntemi ayarları başarıyla güncellendi.", "locationVerification": "<PERSON><PERSON>", "verificationMethods": "<PERSON><PERSON><PERSON><PERSON>", "verificationInfo": "Konum doğrulamayı ka<PERSON>mak, öğrencilerin fiziksel olarak orada olmadan yoklama kaydetmelerine olanak tanır. Bunu uzaktan dersler veya konum doğrulamasının gerekmediği durumlar için kullanın.", "roomLocationSettings": "Oda Konum Ayarları", "roomLocationDescription": "Konum tabanlı yoklama doğrulamasını etkinleştirmek için bu odanın konumunu ayarlayın", "blockLocationSettings": "Blok Konum Ayarları", "blockLocationDescription": "Konum tabanlı yoklama doğrulamasını etkinleştirmek için bu bloğun konumunu ayarlayın", "updateCurrentLocation": "<PERSON><PERSON><PERSON>", "setCurrentLocation": "<PERSON><PERSON><PERSON>", "latitude": "En<PERSON>", "longitude": "<PERSON><PERSON>", "attendanceRadius": "<PERSON><PERSON><PERSON> (metre)", "studentsWithinRadiusRoom": "Öğrenciler yoklamayı doğrulamak için bu yarıçap içinde olmalıdır", "studentsWithinRadiusBlock": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {{blockName}} bloğundaki herhangi bir oda için yoklamayı doğrulamak için bu yarıçap içinde olmalıdır", "locationPermissionDenied": "Konum erişimi reddedildi. Oda konumunu ayarlamak için lütfen tarayıcı ayarlarınızda konum erişimini etkinleştirin.", "locationUpdated": "<PERSON>num <PERSON>", "locationUpdatedMessage": "<PERSON>da konumu başarıyla ayarlandı", "blockLocationUpdatedMessage": "{{blockName}} b<PERSON><PERSON><PERSON> konumu başarıyla ayarlandı", "radiusUpdated": "Ya<PERSON>ı<PERSON><PERSON>", "radiusUpdatedMessage": "Yoklama yarıçapı başarıyla güncellendi", "blockRadiusUpdatedMessage": "{{blockName}} b<PERSON><PERSON><PERSON> i<PERSON>in yokla<PERSON> yarıçapı başarıyla güncellendi", "errorUpdatingRadius": "Yoklama yarıçapı güncellenirken hata oluştu", "locationVerificationSettings": "Konum Doğrulama Ayarları", "locationVerificationDescription": "Yoklama için konum doğrulama gereksinimlerini yapılandırın", "enableLocationVerification": "Konum Doğrulamayı Etkinleştir", "blockLevelVerification": "Blok Seviyesi Doğrulama", "roomLevelVerification": "Oda Seviyesi Doğrulama", "whenDisabled": "Devre dışı bı<PERSON><PERSON><PERSON><PERSON>, hiçbir yoklama için konum doğrulaması gerekmeyecektir", "whenBlockEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> bloğa {{distance}}m mesafede olmalıdır", "whenRoomEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, öğren<PERSON>ler odaya {{distance}}m mesafede olmalıdır", "adminOnlySettings": "Konum ayarları yalnızca yöneticiler tarafından değiştirilebilir. Ayarlarınız devre dışı bırakılmıştır. Konum doğrulama ayarlarını değiştirmeniz gerekiyorsa lütfen bir yöneticiyle iletişime geçin.", "saveSettings": "Ayarları Kaydet", "settingsSaved": "<PERSON><PERSON><PERSON>", "settingsSavedMessage": "Konum doğrulama ayarlarınız başarıyla kaydedildi.", "errorSavingSettings": "Ayarları Kaydetme Hatası", "errorSavingSettingsMessage": "Ayarlarınızı kaydederken bir hata oluştu. Lütfen tekrar deneyin.", "studentsWithinRadius": "Konum doğrulama etkinleştiril<PERSON>ğ<PERSON>e, öğrenciler yoklamayı doğrulamak için belirtilen yarıçap içinde olmalıdır.", "locationRetrieved": "Konum Alındı", "locationRetrievedDescription": "Mevcut konum yüklendi. Gerekirse koordinatları düzenleyebilir, ardından Kaydet'e tıklayabilirsiniz.", "saveLocation": "<PERSON><PERSON><PERSON>"}, "dashboard": {"title": "Öğretmen Paneli", "attendance": "<PERSON><PERSON><PERSON>", "excuses": "<PERSON><PERSON><PERSON><PERSON>", "statistics": "Bugünün <PERSON>", "statisticsDescription": "Bugünkü yoklamanın genel görünümü", "present": "Mevcut", "absent": "Yok", "late": "Geç", "excused": "<PERSON><PERSON><PERSON>", "studentsInClass": "<PERSON><PERSON><PERSON> öğrenci<PERSON>", "studentsMissing": "<PERSON><PERSON><PERSON>", "studentsLate": "Geç kalan öğrenciler", "excusedAbsences": "İzinli devamsızlıklar", "roomAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manageRoomAttendance": "<PERSON>rt odası yoklamasını yönetin", "sendVerificationReminders": "Doğrulama Hatırlatıcıları Gönder", "lastSent": "<PERSON> gönderim: {{time}}", "showAllStudents": "T<PERSON>m Öğrencileri Göster", "allStudents": "<PERSON><PERSON><PERSON>", "showOnlyAbsent": "Sadece Yok Olan Öğrencileri Göster", "addBlock": "Blok <PERSON>", "addRoom": "<PERSON><PERSON><PERSON><PERSON>", "remindersFailed": "Hatırlatıcılar Gönderilemedi", "remindersFailedDescription": "Yoklama hatırlatıcıları gönderilirken bir hata oluştu. Lütfen tekrar deneyin.", "failedToFetchBlocks": "Bloklar getirilemedi", "setupRequired": "<PERSON><PERSON><PERSON>", "setupRequiredDescription": "Sistem kurulumunu tama<PERSON>lamak için lütfen bir yöneticiyle iletişime geçin.", "failedToFetchRooms": "<PERSON><PERSON><PERSON>", "addBlockDescription": "Yeni bir blok eklemek için lütfen blok seçiciyi kullanın", "addRoomDescription": "Yeni bir oda eklemek için lütfen blok seçiciyi kullanın", "refresh": "<PERSON><PERSON><PERSON>", "options": "Seçenekler", "search": "Ara: {{query}}", "searchPlaceholder": "Öğrenci ara...", "gridView": "Izgara Görünümü", "listView": "Liste Görünümü", "studentDirectory": "<PERSON>ğ<PERSON><PERSON>", "studentsNeedVerification": "Yoklama doğrulaması gereken öğrenciler", "manageStudentAttendance": "Öğrenci yoklamasını ve kayıtlarını yönetin", "activeFilters": "Akt<PERSON> filt<PERSON>:", "block": "Blok: {{name}}", "room": "Oda: {{name}}", "absentOnly": "<PERSON><PERSON><PERSON> yok olan<PERSON>", "showingAll": "(tü<PERSON><PERSON><PERSON><PERSON> gösteriyor)", "noStudentsFound": "Öğrenci Bulunamadı", "showingAllStudents": "Filtrelenmiş {{type}} yerine tüm öğrenciler gösteriliyor", "noStudentsInRoom": "Seçilen odada veya blokta öğrenci bulunamadı. Bu, öğrencilerin profillerinde bu oda/bloğa atanmamış olmasından kaynaklanabilir.", "clearFilters": "<PERSON><PERSON><PERSON><PERSON>", "howToAssignRooms": "<PERSON><PERSON><PERSON>ı<PERSON>", "studentRoomAssignment": "Öğrencilerin profil ayarlarında oda ve bloklarını seçmeleri gerekir. Ayrıca Öğrenci Yönetimi bölümünde öğrenci profillerini güncelleyebilirsiniz.", "noStudentsToDisplay": "Gösterilecek öğrenci yok", "noAbsentStudents": "Kriterlerinize uyan yok olan öğrenci bulunmamaktadır.", "noStudentsMatchingSearch": "Aramanızla eşleşen öğrenci bulunamadı.", "loadingStudents": "Öğrenciler Yükleniyor", "pleaseWaitLoading": "Lütfen öğrenci verilerini yüklerken bekleyin..."}, "attendance": {"statusUpdated": "<PERSON><PERSON><PERSON>", "studentMarkedAs": "{{name}} {{status}} o<PERSON><PERSON> i<PERSON>aretlendi", "statusChangedTo": "{{name}} ö<PERSON><PERSON><PERSON><PERSON>n yoklama durumunu {{status}} olarak <PERSON>ğiştirdiniz", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {{time}}", "attendanceUpdated": "<PERSON><PERSON><PERSON>", "studentNowMarked": "{{name}} artık {{status}} olarak işaretlendi", "errorUpdatingStatus": "Durum Güncellenirken Hata", "failedToUpdate": "{{name}} durumu {{status}} olarak güncellenemedi.", "roomAssignmentIssue": "<PERSON>da atama sorunu: {{message}}. Lütfen önce {{name}} öğrencisini bir odaya atayın.", "studentVerifiedPresence": "{{name}} varlığını doğruladı", "noStudentsAvailable": "Öğrenci <PERSON>", "pleaseWaitForData": "Lütfen öğrenci verilerinin yüklenmesini bekleyin.", "todaysAttendance": "Bugünkü Yoklama", "noAbsentStudents": "Yo<PERSON> Olan Öğrenci Yok", "markPresent": "<PERSON><PERSON><PERSON>", "markAbsent": "<PERSON><PERSON>ak <PERSON>aretle", "noAbsentStudentsInTarget": "<PERSON><PERSON> haber! {{target}} içindeki tüm öğrenciler zaten mevcut - hatırlatma gerekmez.", "sendingReminders": "{{target}} içindeki {{count}} yok olan öğrenciye hatırlatma gönderiliyor", "noRemindersSent": "📋 Hatırlatma Gerekmiyor", "noAbsentStudentsFound": "Tüm öğrenciler zaten giriş yapmış - hatırlatılacak yok olan öğrenci bulunamadı.", "remindersSent": "Hatırlatmalar Başarıyla Gönderildi", "remindersPartiallySent": "⚠️ Bazı Hatırlatıcılar Gönderilemedi", "sentVerificationReminders": "{{count}} öğrenciye doğrulama hatırlatıcıları gönderildi", "failedToSend": "{{count}} g<PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "Hedef: {{name}} • <PERSON><PERSON>: {{time}}", "attendanceRemindersSent": "📱 Yoklama Hatırlatıcıları Başarıyla Gönderildi", "studentsNotified": "{{target}} içindeki {{count}} yok olan öğrenciye başarıyla yoklama hatırlatıcısı gönderildi", "failedToSendReminders": "Hatırlatma Gönderimi Başarısız", "couldNotSendReminders": "Yoklama doğrulama hatırlatıcıları gönderilemedi. Lütfen tekrar deneyin.", "remindersSentSuccessfully": "✅ Tüm Yoklama Hatırlatıcıları Teslim Edildi", "errorLoadingData": "<PERSON><PERSON>", "errorLoadingStudentData": "Öğrenci verilerini yüklerken bir hata oluştu. Lütfen sayfayı yenileyin.", "attendanceUIOnlyNoRoom": "Yoklama durumu sadece arayüzde güncellendi. Veritabanı kaydı için oda mevcut değil.", "failedToUpdateStatus": "Yoklama durumu güncellenemedi. Lütfen tekrar deneyin."}, "profile": {"teacherProfile": "Öğretmen Profili", "completeYourProfile": "Öğretmen Profilinizi <PERSON>", "updateProfileInfo": "Profil bilgiler<PERSON>zi gü<PERSON><PERSON>n", "viewAndManageProfile": "Öğretmen profilinizi görüntüleyin ve yönetin", "fillRequiredFields": "Profil kurulumunuzu tamamlamak için lütfen tüm gerekli alanları doldurun.", "profileSetupComplete": "<PERSON>il kuru<PERSON> ta<PERSON>ı", "profileSetupMessage": "Profiliniz başarıyla kuruldu. Artık tüm özelliklere erişebilirsiniz.", "department": "Bölüm", "position": "Pozisyon", "subject": "<PERSON><PERSON>", "teacherId": "<PERSON><PERSON><PERSON><PERSON>", "enterTeacherId": "Öğretmen kimliğinizi girin", "enterDepartment": "Bölümünüzü girin", "enterPosition": "Pozisyonunuzu girin", "enterSubject": "Dersinizi girin"}, "excuses": {"excusesManagement": "<PERSON><PERSON><PERSON>", "manageStudentExcuses": "Öğrenci de<PERSON>msızlık mazeret taleplerini inceleyin ve yönetin", "pending": "Beklemede", "approved": "Mazeret On<PERSON>landı", "approvedDescription": "Mazeret başarıyla onaylandı", "rejected": "Mazeret Reddedildi", "rejectedDescription": "Ma<PERSON><PERSON> reddedildi", "all": "Tümü", "student": "<PERSON><PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "reason": "Sebep", "actions": "İşlemler", "status": "Durum", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "addNote": "Not Ekle", "notes": "Notlar", "enterNotes": "<PERSON><PERSON> <PERSON>ret i<PERSON>in notlar girin", "saveNotes": "Notları Kaydet", "confirmApprove": "Onayı Doğrula", "confirmReject": "<PERSON><PERSON>", "approveExcuseQuestion": "<PERSON>u mazereti onay<PERSON>ak istediğinizden emin misiniz?", "rejectExcuseQuestion": "<PERSON>u mazereti reddetmek istediğinizden emin misiniz?", "excuseApproved": "Mazeret On<PERSON>landı", "excuseRejected": "Mazeret Reddedildi", "excuseUpdated": "Mazeret Güncellendi", "updateError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noPendingExcuses": "Bekleyen Mazeret Yok", "noApprovedExcuses": "Onaylanmış Mazeret Yok", "noRejectedExcuses": "Reddedilmiş Mazeret Yok", "noExcusesMessage": "Gösterilecek mazeret yok", "loadingExcuses": "Mazeretler yükleniyor...", "excuseDetails": "Mazeret Detayları", "reviewExcuse": "<PERSON>u mazeret talebini inceleyin ve yönetin", "confirmDeleteExcuse": "Bu mazeret talebini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "export": "Dışa Aktar", "exportAsHTML": "HTML olarak Dışa Aktar", "exportAsPDF": "PDF olarak Dışa Aktar", "exportAsCSV": "CSV olarak Dışa Aktar", "exportComplete": "Dışa Aktarma Tamamlandı", "excusesExported": "{{count}} mazeret {{format}} formatında dışa aktarıldı", "excusesReport": "Ma<PERSON><PERSON> Raporu", "comprehensiveReport": "Kapsamlı Öğretmen Mazeret Yönetimi Raporu", "generatedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "totalExcuses": "Toplam Mazeret", "dateRange": "<PERSON><PERSON><PERSON>", "notSpecified": "Belirtilmemiş", "unknownStudent": "Bilinmeyen Öğrenci", "unknownRoom": "Bilinmeyen Oda", "notApplicable": "Uygulanamaz", "reportContains": "<PERSON>u rapor {{date}} ta<PERSON><PERSON> itiba<PERSON> {{count}} mazeret kaydı içermektedir"}, "alerts": {"distanceAlerts": "<PERSON>fe <PERSON>ı", "studentDistanceVerificationAlerts": "Öğrenci mesafe doğrulama uyarıları", "noDistanceAlerts": "Mesafe uyarısı yok", "loadingAlerts": "Uyarılar yükleniyor...", "deleteAll": "Tümünü <PERSON>", "deleteAllTitle": "T<PERSON>m uyarıları sil", "alertDismissed": "Uyarı Kapatıldı", "alertDeleted": "<PERSON><PERSON><PERSON> silindi", "alertsCleared": "Uyarılar Temizlendi", "allAlertsDeleted": "<PERSON><PERSON><PERSON> si<PERSON>i", "errorDeletingAlert": "Uyarı silinemedi", "errorDeletingAlerts": "Uyarı<PERSON> si<PERSON>medi", "failedToLoad": "Uyarılar yüklenemedi", "distanceAlert": "Mesafe Uyarısı", "severity": "<PERSON>ne<PERSON>", "distance": "Mesafe", "room": "<PERSON><PERSON>", "deleteAlertTitle": "Uyarıyı sil", "securityLocationAlerts": "Güvenlik ve Konum Uyarıları", "securityLocationAlertsDescription": "Öğrenci güvenlik ve konum doğrulama uyarıları", "noSecurityLocationAlerts": "Güvenlik veya konum uyarısı yok", "securityAlert": "Güvenlik Uyarısı", "attendanceAlert": "Yoklama Uyarısı", "alert": "Uyarı", "outsideRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON> dı<PERSON>", "viewLocation": "<PERSON><PERSON><PERSON>", "getDirections": "<PERSON><PERSON>", "severityLow": "Düşük", "severityMedium": "Orta", "severityHigh": "<PERSON><PERSON><PERSON><PERSON>", "severityCritical": "<PERSON><PERSON><PERSON>"}}, "admin": {"attendanceManagement": {"title": "<PERSON><PERSON>", "description": "<PERSON>am <PERSON>larını ve okul yapısını yönetin", "reports": "<PERSON><PERSON>", "reportsShort": "<PERSON><PERSON><PERSON>", "structure": "Okul Yapısı", "structureShort": "Yap<PERSON>", "remindersTab": "<PERSON><PERSON>", "remindersShort": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dailyReports": "Günlük Devam Raporları", "dailyReportsDescription": "Son 7 günün devam verilerini dışa aktarın", "schoolStructure": "Blok ve Oda Yönetimi", "schoolStructureDescription": "Okulunuzun blok ve odalarını yönetin", "last7Days": "Son 7 Gün", "last7DaysDescription": "Geçen haftanın devam raporlarını görüntüleyin ve dışa aktarın", "exportDay": "<PERSON><PERSON><PERSON> Akt<PERSON>", "cleanup": "Eski Kayıtları Temizle", "loadingReports": "<PERSON><PERSON> yükleniyor...", "loadingStructure": "Okul yapısı yükleniyor...", "fetchError": "<PERSON><PERSON> ve<PERSON>", "fetchDataError": "Blok ve odalar yüklenemedi", "exportSuccess": "<PERSON>or başarıyla dışa aktarıldı", "exportSuccessDescription": "{{date}} tari<PERSON>i devam raporu indirildi", "exportError": "Rapor dışa aktarılamadı", "cleanupSuccess": "Eski kayıtlar temizlendi", "cleanupSuccessDescription": "7 günden eski kayıtlar kaldırıldı", "cleanupError": "Eski kayıtlar temizlenemedi", "csv": {"reportDate": "<PERSON><PERSON>", "totalStudents": "Toplam Öğrenci", "presentStudents": "<PERSON><PERSON><PERSON>", "absentStudents": "Devamsız <PERSON>", "attendanceRate": "<PERSON><PERSON>", "studentName": "Öğrenci <PERSON>ı", "studentId": "Öğrenci No", "email": "E-posta", "status": "Durum", "checkInTime": "<PERSON><PERSON><PERSON>", "method": "Doğrulama Yöntemi"}, "manageStructure": "Okul Yapısını Yönet", "manageStructureDescription": "Okulunuz için blok ve odaları ekleyin, d<PERSON><PERSON><PERSON>in ve organize edin", "blocks": "Bloklar", "addBlock": "Blok <PERSON>", "createFirstBlock": "İlk Bloğunuzu Oluşturun", "noBlocks": "Blok bulunamadı. Başlamak için ilk bloğunuzu oluşturun.", "blockName": "Blok Adı", "blockNamePlaceholder": "Blok adını girin (<PERSON><PERSON><PERSON> <PERSON>, <PERSON>)", "blockDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "blockDescriptionPlaceholder": "Bu blok için isteğe bağlı açıklama", "createBlock": "Blok Oluştur", "editBlock": "<PERSON><PERSON><PERSON><PERSON>", "createBlockDescription": "Okulunuzda odaları organize etmek için yeni bir blok ekleyin", "editBlockDescription": "Blok bilgilerini güncelleyin", "blockCreated": "Blok başarıyla oluşturuldu", "blockCreatedDescription": "{{name}} okulunuza eklendi", "blockUpdated": "Blok başarıyla güncellendi", "blockUpdatedDescription": "{{name}} g<PERSON><PERSON><PERSON><PERSON>", "blockDeleted": "Blok başar<PERSON><PERSON> si<PERSON>", "blockDeletedDescription": "{{name}} okulunuzdan kaldırıldı", "deleteBlockTitle": "<PERSON><PERSON><PERSON><PERSON>", "deleteBlockDescription": "{{name}} bloğ<PERSON>u silmek istediğinizden emin misiniz? Bu işlem aynı zamanda bu bloktaki tüm odaları silecek, tüm öğrencilerin oda atamalarını kaldıracak ve ilgili tüm verileri (devam kayıtları, konum ayarları, vb.) silecektir. Bu işlem geri alınamaz.", "cannotDeleteBlock": "Blok silinemiyor", "blockHasRooms": "Bu blok {{count}} oda içeriyor. Lütfen önce tüm odaları silin.", "saveBlockError": "Blok kaydedilemedi", "deleteBlockError": "Blok silinemedi", "roomCount": "{{count}} oda", "roomCount_plural": "{{count}} oda", "allRooms": "<PERSON><PERSON><PERSON>", "roomsInBlock": "{{blockName}} Blokundaki Odalar", "addRoom": "<PERSON><PERSON>", "createFirstRoom": "İlk Odanızı Oluşturun", "noRooms": "Oda bulunamadı. Başlamak için ilk odanızı oluşturun.", "noRoomsInBlock": "Bu blokta oda yok. Başlamak için bir oda e<PERSON>in.", "roomName": "<PERSON><PERSON> Adı", "roomNamePlaceholder": "<PERSON>da adını girin (örn. Oda 101, <PERSON><PERSON>atuvarı)", "selectBlock": "Blok Seçin", "selectBlockPlaceholder": "<PERSON>u oda i<PERSON>in bir blok seçin", "buildingPlaceholder": "<PERSON>a adı (isteğe bağlı)", "floorPlaceholder": "<PERSON>", "capacity": "Kapasite", "capacityPlaceholder": "<PERSON><PERSON><PERSON>um öğrenci sayısı", "floor": "<PERSON>", "building": "<PERSON><PERSON>", "createRoom": "Oda <PERSON>", "editRoom": "Odayı Düzenle", "createRoomDescription": "Seçilen bloğa yeni bir oda e<PERSON>in", "editRoomDescription": "<PERSON>da bilgi<PERSON>ini gü<PERSON>n", "roomCreated": "<PERSON><PERSON> başarıyla oluşturuldu", "roomCreatedDescription": "{{name}} okulunuza eklendi", "roomUpdated": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "roomUpdatedDescription": "{{name}} g<PERSON><PERSON><PERSON><PERSON>", "roomDeleted": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "roomDeletedDescription": "{{name}} okulunuzdan kaldırıldı", "deleteRoomTitle": "Odayı Sil", "deleteRoomDescription": "{{name}} odasını silmek istediğinizden emin misiniz? Bu işlem tüm öğrencilerin bu odadan atamalarını kaldıracak ve ilgili tüm verileri (devam kayıtları, konum ayarları, QR oturumları, vb.) silecektir. Bu işlem geri alınamaz.", "saveRoomError": "<PERSON><PERSON>", "deleteRoomError": "<PERSON><PERSON> si<PERSON>", "reminders": {"title": "Otomatik Devam Hatırlatmaları", "description": "Otomatik hatırlatmaları yapılandırın ve devamsız öğrencilere manuel bildirimler gönderin", "loading": "Hatırlatma ayarları yükleniyor...", "serviceStatus": "<PERSON><PERSON>", "serviceRunning": "Hatırlatma Servisi", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "sentToday": "Bugün Gönderilen Hatırlatmalar", "nextCheck": "<PERSON><PERSON><PERSON>", "automatedSettings": "Otomatik Hatırlatma Ayarları", "enableAutomation": "Otomatik Hatırlatmaları Etkinleştir", "enableDescription": "<PERSON><PERSON> oturumu bitmeden önce devamsız öğrencilere otomatik hatırlatma gönder", "minutesBefore": "Bitiş Öncesi Dakika", "minutesBeforeEnd": "oturum bitmeden dakika <PERSON>", "minutesDescription": "<PERSON><PERSON> oturumu bitmeden kaç dakika önce hatırlatma gönderilmeli", "reminderWillSend": "Hatırlatma Programı", "timePreview": "Hatırlatmalar {{time}} saatinde gönderilecek (devam {{endTime}} saatinde bitiyor, {{minutes}} dakika önce)", "lateMarkingTitle": "Akıllı Geç Kalma Tespiti", "lateMarkingDescription": "Otomatik hatırlatma gönderildikten sonra tarama/giri<PERSON> ya<PERSON> 'mevcut' yerine 'geç' olarak işaretlenecek. Hiç tarama yapmayan öğrenciler 'devamsız' olarak kalır.", "saveSettings": "Hatırlatma Ayarlarını Kaydet", "settingsSaved": "Hatırlatma Ayarları Kaydedildi", "automationEnabled": "Otomatik hatırlatmalar etkinleştirildi - oturum bitmeden {{minutes}} dakika önce gönderilecek", "automationDisabled": "Otomatik hatırlatmalar devre dışı bırakıldı", "loadError": "Hatırlatma ayarları yüklenemedi", "saveError": "Hatırlatma ayarları kaydedilemedi", "manualReminder": "<PERSON>", "manualDescription": "Tüm devamsız öğrencilere anında hatırlatma gönder (öğretmen işlevi gibi)", "sendNow": "Şimdi Hatırlatma Gönder", "sending": "Gönderiliyor...", "manualSent": "<PERSON>", "sentToStudents": "{{count}} devamsız öğrenciye hatırlatma gönderildi", "noAbsentStudents": "Devamsız Öğrenci Yok", "allStudentsPresent": "<PERSON>üm öğrenciler zaten giriş yapmış", "manualSendError": "<PERSON>"}}, "carousel": {"title": "Carousel Yönetimi", "description": "Öğrenci ve öğretmen panelleri için carousel içeriğini yönetin", "items": "Carousel Öğeleri", "addItem": "<PERSON><PERSON><PERSON>", "editItem": "Carousel Öğesini Düzenle", "noItems": "Henüz carousel öğesi yok. Başlamak için ilk öğenizi ekleyin.", "order": "<PERSON><PERSON><PERSON>", "image": "G<PERSON><PERSON><PERSON>", "audience": "<PERSON><PERSON><PERSON>", "status": "Durum", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teachers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemTitle": "Başlık", "itemDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "itemTitlePlaceholder": "<PERSON>u öğe için bir başl<PERSON>k girin", "itemDescriptionPlaceholder": "Bir açıklama girin (isteğe bağlı)", "imageUpload": "<PERSON><PERSON><PERSON><PERSON>", "targetAudience": "<PERSON><PERSON><PERSON>", "activeItem": "Aktif", "itemFormDescription": "Bu carousel öğesi için detayları doldurun.", "itemUpdated": "Carousel öğesi başarıyla güncellendi", "itemAdded": "Carousel öğesi başarıyla eklendi", "itemDeleted": "Carousel öğ<PERSON> ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "itemActivated": "Carousel öğesi aktifleştirildi", "itemDeactivated": "Carousel öğ<PERSON> p<PERSON>", "errorLoading": "Carousel öğeleri yükle<PERSON> hata oluştu", "errorSaving": "Carousel öğesi kaydedilirken hata oluştu", "errorDeleting": "Carousel öğesi silini<PERSON>en hata oluştu", "errorToggling": "Carousel öğesi durumu güncellenirken hata oluştu", "errorReordering": "Carousel öğeleri yeniden sıralanırken hata oluştu", "titleRequired": "Başlık gereklidir", "mediaRequired": "Lütfen bir görsel seçin", "audienceRequired": "Lütfen en az bir hedef kitle seçin", "invalidFileType": "Lütfen bir görsel dosyası seçin.", "fileTooLarge": "Görsel dosyası çok büyük. Maksimum boyut 5MB'dır.", "errorReadingFile": "Görsel dosyası okunurken hata oluştu. Lütfen tekrar deneyin.", "fileUploaded": "G<PERSON><PERSON>l başarıyla yüklendi", "fileUploadError": "G<PERSON><PERSON>l yüklenir<PERSON> hata oluş<PERSON>"}, "backToSystemAdmin": "Sistem Yöneticisine Dön", "systemAdminView": "Sistem Yönetici Görünümü", "tabs": {"users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schools": "<PERSON><PERSON><PERSON>", "qrCodes": "QR Kodları", "fraud": "Hile Tespiti", "excuses": "<PERSON><PERSON><PERSON><PERSON>", "parents": "<PERSON><PERSON><PERSON>", "alerts": "Uyarılar", "schoolSettings": "Okul Ayarları", "auditLogs": "Denetim <PERSON>ıtları", "settings": "<PERSON><PERSON><PERSON>", "security": "Güvenlik", "attendanceSettings": "<PERSON><PERSON><PERSON>"}, "qrCodes": {"generator": "Üretici", "tabletSetup": "Tablet Kurulumu", "tabletManagement": "Tablet Yönetimi", "automatic": "Otomatik"}, "dashboard": {"title": "Okul Yöneticisi Panosu", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "qrCodes": "QR Kodları", "fraud": "Dolandırıcılık Tespiti", "excuses": "<PERSON><PERSON><PERSON><PERSON>", "alerts": "Uyarılar", "settings": "<PERSON><PERSON><PERSON>", "schoolSettings": "Okul Ayarları", "auditLogs": "<PERSON><PERSON><PERSON> Gü<PERSON>", "present": "Mevcut", "absent": "Yok", "late": "Geç", "excused": "<PERSON><PERSON><PERSON><PERSON>"}, "attendance": {"statusUpdated": "<PERSON><PERSON>", "studentMarkedAs": "{{name}} {{status}} o<PERSON><PERSON> i<PERSON>aretlendi", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {{time}}", "attendanceUpdated": "<PERSON><PERSON><PERSON>", "studentNowMarked": "{{name}} artık {{status}} olarak işaretlendi", "errorUpdatingStatus": "Durum Güncellenirken Hata", "failedToUpdate": "{{name}} durumu {{status}} olarak güncellenemedi.", "roomAssignmentIssue": "<PERSON>da atama sorunu: {{message}}. Lütfen önce {{name}} öğrencisini bir odaya atayın.", "studentVerifiedPresence": "{{name}} varlığını doğruladı", "noStudentsAvailable": "Öğrenci <PERSON>", "pleaseWaitForData": "Lütfen öğrenci verilerinin yüklenmesini bekleyin.", "todaysAttendance": "Bugünkü Yoklama", "noAbsentStudents": "Yo<PERSON> Olan Öğrenci Yok"}, "userManagement": {"title": "Kullanıcı Yönetimi", "description": "Sistemdeki tüm kullanıcıları görüntüleyin ve yönetin", "searchPlaceholder": "İsim veya ID ile ara...", "addUser": "Kullanıcı Ekle", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teachers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "administrators": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalUsers": "Toplam Kullanıcılar", "user": "Kullanıcı", "role": "Rol", "id": "ID", "status": "Durum", "actions": "İşlemler", "noUsersFound": "Kullanıcı bulunamadı", "noUsersMatching": "Eşleşen kullanıcı bulunamadı", "allRoles": "<PERSON><PERSON><PERSON>", "resetPassword": "<PERSON><PERSON><PERSON> s<PERSON>", "blockUser": "Kullanıcıyı engelle", "unblockUser": "Kullanı<PERSON>ı engel<PERSON> kaldır", "enableMaintenanceMode": "Bakım modunu et<PERSON>ştir", "disableMaintenanceMode": "Bakım modunu devre dışı bırak", "blockUserConfirm": "{{name}} kullanıcısını engellemek istediğinizden emin misiniz? Bu, kullanıcının engeli kaldırılana kadar sisteme erişimini engelleyecektir.", "unblockUserConfirm": "{{name}} kullanıcısının engelini kaldırmak istediğinizden emin misiniz? Bu, kullanıcının sisteme tekrar erişmesine izin verecektir.", "enableMaintenanceModeConfirm": "{{name}} kullanıcısı için bakım modunu etkinleştirmek istediğinizden emin misiniz? Bu, bakım modu devre dışı bırakılana kadar kullanıcının sisteme erişimini engelleyecektir.", "disableMaintenanceModeConfirm": "{{name}} kullanıcısı için bakım modunu devre dışı bırakmak istediğinizden emin misiniz? Bu, kullanıcının sisteme tekrar erişmesine izin verecektir.", "deleteUserConfirm": "{{name}} kullanıcısını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve kullanıcı ile ilişkili tüm verileri sistemden kalıcı olarak kaldıracaktır.", "usersCreatedForYourSchool": "Kullanıcılar sizin okulunuz için oluşturulacak", "deleteUser": "Kullanıcıyı sil", "userActions": "Kullanıcı İşlemleri", "blocked": "<PERSON><PERSON><PERSON>", "maintenance": "Bakım", "addNewUser": "<PERSON><PERSON>", "addNewUserDescription": "Yeni bir kullanıcı hesabı oluşturun. <PERSON><PERSON><PERSON><PERSON><PERSON>, şifresini ayarlamak için bir e-posta alacaktır.", "fullName": "Tam Ad", "email": "E-posta", "temporaryPassword": "Geçici Şifre", "school": "<PERSON><PERSON>", "cancel": "İptal", "createUser": "Kullanıcı Oluştur", "creating": "Oluşturuluyor..."}, "userDetails": {"title": "Kullanıcı Detayları", "edit": "<PERSON><PERSON><PERSON><PERSON>", "name": "İsim", "email": "E-posta", "joined": "Katılım", "course": "<PERSON><PERSON>", "block": "Blok", "room": "<PERSON><PERSON>", "studentId": "<PERSON><PERSON><PERSON><PERSON>", "department": "Bölüm", "subject": "<PERSON><PERSON>", "teacherId": "<PERSON><PERSON><PERSON><PERSON>", "position": "Pozisyon", "notSet": "Ayarlanmamış"}, "userEditForm": {"title": "Kullanıcı Detaylarını Düzenle", "name": "İsim", "studentId": "<PERSON><PERSON><PERSON><PERSON>", "course": "<PERSON><PERSON>", "blockCorridor": "Blok/Koridor", "roomNumber": "<PERSON>da Numarası", "cancel": "İptal", "saveChanges": "Değişiklikleri Kaydet", "saving": "Kay<PERSON>ili<PERSON>r...", "success": "Başarılı", "successMessage": "Kullanıcı detayları başarıyla güncellendi.", "error": "<PERSON><PERSON>", "errorMessage": "Kullanıcı detayları güncellenemedi. Lütfen tekrar deneyin."}, "studentDirectory": {"title": "<PERSON>ğ<PERSON><PERSON>", "description": "Okulunuzdaki tüm öğrencileri görüntüleyin ve yönetin", "searchPlaceholder": "İsim veya kimlik ile ara...", "filterByBlock": "Bloğa göre filtrele", "filterByRoom": "Odaya g<PERSON>re filtrele", "profile": "Profil", "name": "İsim", "studentId": "<PERSON><PERSON><PERSON><PERSON>", "course": "<PERSON><PERSON>", "block": "Blok", "room": "<PERSON><PERSON>", "status": "Durum", "actions": "İşlemler", "notSet": "Ayarlanmamış", "notAssigned": "Atanmamış", "viewMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gridView": "Izgara Görünümü", "listView": "Liste Görünümü", "noStudentsFound": "Öğrenci bulunamadı", "loadingStudents": "Öğrenciler yükleniyor..."}, "settings": {"title": "Yönetici Ayarları", "description": "Okulunuz için sistem ayarlarını yapılandırın", "locationSettings": {"locationRetrieved": "Konum Alındı", "locationRetrievedDescription": "Mevcut konum yüklendi. Gerekirse koordinatları düzenleyebilir, ardından Kaydet'e tıklayabilirsiniz.", "saveLocation": "<PERSON><PERSON><PERSON>", "locationUpdated": "<PERSON>num <PERSON>", "locationUpdatedMessage": "<PERSON>da konumu başarıyla ayarlandı", "blockLocationUpdatedMessage": "{{blockName}} blok konumu başarıyla ayarlandı", "radiusUpdated": "Ya<PERSON>ı<PERSON><PERSON>", "radiusUpdatedMessage": "Yoklama yarıçapı başarıyla güncellendi", "blockRadiusUpdatedMessage": "{{blockName}} bloku için yoklama yarıçapı başarıyla güncellendi", "errorUpdatingRadius": "Yoklama yarıçapı güncellenemedi", "setLocationFirst": "Yarıçapı güncellemeden önce lütfen konum koordinatlarını ayarlayın"}, "blockLocations": "Blok Konumları", "roomLocations": "Oda Konumları", "verification": "Doğrulama", "biometricRequests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permissions": "<PERSON><PERSON><PERSON>", "excuseSettings": "Ma<PERSON><PERSON>ları", "attendanceSettings": "<PERSON><PERSON><PERSON>", "notificationSettings": "<PERSON><PERSON><PERSON><PERSON>", "systemSettings": "Sistem Ayarları", "saveSettings": "Ayarları Kaydet", "settingsSaved": "<PERSON><PERSON><PERSON>", "settingsSavedMessage": "Ayarlarınız başarıyla kaydedildi.", "errorSavingSettings": "Ayarları Kaydetme Hatası", "errorSavingSettingsMessage": "Ayarlarınızı kaydederken bir hata oluştu. Lütfen tekrar deneyin.", "systemLocationSettings": "Sistem Konum Ayarları", "systemLocationDescription": "Sistem genelinde yoklama doğrulaması için konum ayarlarını yapılandırın", "selectBlock": "Blok Seçin", "selectRoom": "<PERSON><PERSON>", "allBlocks": "<PERSON><PERSON><PERSON>", "selectBlockFirst": "Önce bir blok seçin", "noRoomsInBlock": "Bu blokta oda yok", "showingRoomsFromBlock": "{{blockName}} Bloğundan {{count}} oda gösteriliyor", "showingAllRooms": "Toplam {{count}} oda gösteriliyor", "blockLocationSettings": "Blok Konum Ayarları", "blockLocationDescription": "Konum tabanlı yoklama doğrulamasını etkinleştirmek için {blockName} bloğunun konumunu ayarlayın", "roomLocationSettings": "Oda Konum Ayarları", "roomLocationDescription": "Konum tabanlı yoklama doğrulamasını etkinleştirmek için bu odanın konumunu ayarlayın", "updateCurrentLocation": "<PERSON><PERSON><PERSON>", "setCurrentLocation": "<PERSON><PERSON><PERSON>", "latitude": "En<PERSON>", "longitude": "<PERSON><PERSON>", "attendanceRadius": "<PERSON><PERSON><PERSON> (metre)", "studentsWithinRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {blockName} Bloğundaki herhangi bir oda için yoklamayı doğrulamak üzere bu yarıçap içinde olmalıdır", "studentsWithinRadiusRoom": "Öğrenciler yoklamayı doğrulamak için bu yarıçap içinde olmalıdır", "locationPermissionDenied": "Konum erişimi reddedildi. Blok konumunu ayarlamak için lütfen tarayıcı ayarlarınızda konum erişimini etkinleştirin.", "roomLocationPermissionDenied": "Konum erişimi reddedildi. Oda konumunu ayarlamak için lütfen tarayıcı ayarlarınızda konum erişimini etkinleştirin.", "roomLocationInfo": "Odaya özgü konumlar, blok konumlarını geçersiz kılar. Yalnızca blok varsayılanından farklı ayarlara ihtiyacınız varsa oda konumlarını ayarlayın.", "blockLocationInfo": "Bir blok için konum ayarlamak, o bloktaki tüm odalara uygulanacaktır. <PERSON><PERSON>, çoğu durumda önerilir çünkü tek tek oda konumlarını ayarlamaktan daha verimlidir.", "verificationInfo": "Konum doğrulamayı ka<PERSON>mak, öğrencilerin fiziksel olarak mevcut olmadan yoklama kaydetmelerine olanak tanır. Bunu uzaktan dersler için veya konum doğrulaması gerekmediğinde kullanın.", "teacherPermissionsInfo": "Öğretmenlerin ayarları düzenlemesine izin verilmediğinde, tüm konum doğrulamaları için yalnızca yönetici ayarları kullanılacaktır. <PERSON><PERSON><PERSON><PERSON><PERSON>, konum ayarlarını düzenleyemediklerini belirten bir mesaj göreceklerdir.", "attendanceFooter": "Konum doğrulama etkinleştiril<PERSON>ğ<PERSON>e, öğrenciler yoklamayı doğrulamak için belirtilen yarıçap içinde olmalıdır.", "configureLocationVerification": "Konum Doğrulamayı Yapılandır", "controlLocationVerification": "Öğrencilerin yoklama kaydederken konumlarını doğrulamaları gerekip gerekmediğini kontrol edin", "verificationMethodSettings": "Doğrulama Yöntemi Ayarları", "verificationMethodDescription": "Öğrencilerin yoklama kaydederken hangi kimlik doğrulama yöntemlerini kullanması gerektiğini yapılandırın.", "selectVerificationMethod": "Doğrulama Yöntemi Gereksinimini Seçin", "verificationEither": "Biyometrik veya PIN", "verificationEitherDescription": "Öğrenciler biyometrik kimlik doğrulama veya PIN doğrulama arasında seçim yapabilir. En esnek seçenek.", "verificationBiometricOnly": "<PERSON><PERSON><PERSON>", "verificationBiometricOnlyDescription": "Öğrenciler biyometrik kimlik doğrulama (parmak izi, yüz tanıma vb.) kullanmalıdır. En yüksek güvenlik ancak uyumlu cihazlar gerektirir.", "verificationPinOnly": "Sadece PIN Doğrulama", "verificationPinOnlyDescription": "Öğrenciler PIN doğrulama kullanmalıdır. Tüm cihazlarda çalışan en uyumlu seçenek.", "verificationBothRequired": "<PERSON><PERSON> Biyomet<PERSON> de PIN Gerekli", "verificationBothRequiredDescription": "Öğrenciler hem biyometrik kimlik doğrulama HEM de PIN doğrulama tamamlamalıdır. Maksimum güvenlik ancak yoklama sürecini yavaşlatabilir.", "recommended": "Önerilen", "highSecurity": "Yüksek Güvenlik", "compatible": "<PERSON><PERSON><PERSON><PERSON>", "maximumSecurity": "Ma<PERSON><PERSON>um Güvenlik", "biometricOptional": "Biyometrik İsteğe Bağlı", "pinOptional": "PIN İsteğe Bağlı", "biometricRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinRequired": "PIN <PERSON>", "schoolWideVerificationInfo": "Bu ayarlar okulunuzdaki tüm öğrenciler için geçerlidir. Değişiklikler yeni yoklama kayıtları için hemen etkili olacaktır.", "verificationMethodSettingsSaved": "Doğrulama yöntemi ayarları başarıyla güncellendi.", "locationVerification": "<PERSON><PERSON>", "verificationMethods": "<PERSON><PERSON><PERSON><PERSON>", "locationVerificationSettings": "Konum Doğrulama Ayarları", "configureVerificationRequirements": "Yoklama için konum doğrulama gereksinimlerini yapılandırın", "enableLocationVerification": "Konum Doğrulamayı Etkinleştir", "whenEnabledVerifyLocation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, öğrenciler yoklama kaydetmek için konumlarını doğrulamalıdır", "blockLevelVerification": "Blok düzeyinde Doğrulama", "whenEnabledWithinBlockRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> bloğun {radius}m içinde olmalıdır", "roomLevelVerification": "Oda düzeyinde Doğrulama", "whenEnabledWithinRoomRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, öğren<PERSON><PERSON> odanın {radius}m içinde olmalıdır", "saving": "Kay<PERSON>ili<PERSON>r...", "locationSettingsSaved": "Konum doğrulama ayarları başarıyla kaydedildi", "locationSettingsSaveError": "Konum doğrulama ayarları kaydedilemedi", "locationSettingsLoadError": "Konum doğrulama ayarları yüklenemedi. Varsayılanlar kullanılıyor.", "teacherPermissions": "Öğretmen İzinleri", "controlTeacherPermissions": "Öğretmenlerin konum ayarlarını düzenleyip düzenleyemeyeceğini kontrol edin", "allowTeachersToEdit": "Öğretmenlerin Konum Ayarlarını Düzenlemesine İzin Ver", "teachersCanEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON>, odal<PERSON><PERSON> ve blokları için konum ayarlarını düzenleyebilir. Onların ayarları, yönetici ayarlarından öncelikli olacaktır.", "teachersCannotEdit": "Öğretmenler konum ayarlarını düzenleyemez. Tüm konum doğrulamaları için yalnızca yönetici ayarları kullanılacaktır.", "savePermissions": "İzinleri Kaydet", "teachersWillSeeAndModify": "Öğretmenler konum ayarlarını görebilecek ve değiştirebileceklerdir.", "teachersWillSeeMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>, konum ayarlarını düzenleyemediklerini belirten bir mesaj göreceklerdir.", "teacherPermissionsUpdated": "Öğretmen izinleri güncellendi. Öğretmenler konum ayarlarını {canEdit} düzenleyebilir.", "teacherPermissionsSaveError": "Öğretmen izinleri kaydedilemedi. Lütfen tekrar deneyin.", "teacherPermissionsLoadError": "Öğretmen izinleri yüklenemedi. Varsayılan ayarlar kullanılıyor.", "teacherPermissionsNotAllowed": "Öğretmenlerin ayarları düzenlenmesine izin verilmediğinde, tüm konum doğrulamaları için yalnızca yönetici ayarları kullanılacaktır. <PERSON><PERSON><PERSON><PERSON><PERSON>, konum ayarlarını düzenleyemediklerini belirten bir mesaj göreceklerdir.", "can": "ya<PERSON><PERSON><PERSON>", "cannot": "yapamaz", "excuses": "<PERSON><PERSON><PERSON><PERSON>", "attendance": "<PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socialMedia": "<PERSON><PERSON><PERSON>", "excuseSubmissionSettings": "Mazeret Gönderme Ayarları", "controlExcuseSubmission": "Öğrencilerin ne zaman mazeret talebi gönderebileceğini kontrol edin", "excuseTimeRestrictionInfo": "Zaman kısıtlamaları ayarlamak, gereksiz mazeret taleplerini önlemeye yardımcı olur ve öğrencilerin mazeretleri uygun saatlerde göndermelerini sağlar.", "submissionTimeRestrictions": "<PERSON><PERSON><PERSON><PERSON> Kısıtlamaları", "submissionTimeRestrictionsDescription": "Öğrenciler mazeret taleplerini yalnızca aşağıda belirttiğiniz zaman diliminde gönderebileceklerdir. Bu saatlerin dışında, gönderim formu devre dışı bırakılacaktır.", "submissionStartTime": "<PERSON><PERSON><PERSON><PERSON>", "submissionEndTime": "<PERSON><PERSON><PERSON><PERSON>", "maxDaysInAdvance": "<PERSON><PERSON><PERSON><PERSON> Gün <PERSON> (İleriye Dönük)", "maxDaysInAdvanceDescription": "Öğrencilerin mazeret talebinde bulunabileceği maksimum gün sayı<PERSON>ı", "maxExcuseDuration": "Maksimum Mazeret Süresi (Gün)", "maxExcuseDurationDescription": "Tek bir mazeret talebi için maksimum gün sayısı", "attendanceRecordingSettings": "Yo<PERSON>ma <PERSON> A<PERSON>ları", "controlAttendanceRecording": "Öğrencilerin yoklamalarını ne zaman kaydedebileceklerini kontrol edin", "attendanceTimeRestrictionInfo": "Yoklama kaydı için zaman kısıtlamaları ayarlamak, öğrencilerin yalnızca uygun ders saatlerinde yoklama kaydetmelerini sağlamaya ve ders saatleri dışında yetkisiz yoklama kaydını önlemeye yardımcı olur.", "parentNotificationSettings": "<PERSON><PERSON>", "configureParentNotifications": "Velilerin öğrenci mazeretleri hakkında nasıl ve ne zaman bilgilendirileceğini yapılandırın", "parentNotificationInfo": "<PERSON><PERSON>, a<PERSON><PERSON><PERSON>ının yoklamaları ve mazeret talepleri hakkında bilgi sahibi olmalarına yardımcı olarak okul ve veliler arasındaki iletişimi iyileştirir.", "systemAdminSettings": "Sistem Yöneticisi Ayarları", "manageSystemSettings": "Sistem genelindeki ayarları ve erişim kontrollerini yönetin", "systemAdminOnlyInfo": "Bu ayarlar yalnızca sistem yöneticileri tarafından kullanılabilir. Yeni sistem yöneticisi hesapları oluşturmak için sistem yönetici kodu gereklidir.", "notificationPreferences": "<PERSON><PERSON><PERSON><PERSON>", "configureGlobalParentNotifications": "Veli bildirimleri için genel ayarları yapılandırın", "enableParentNotifications": "<PERSON><PERSON>dirimlerini Etkinleştir", "whenEnabledParentsNotified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON>ının mazeret talepleri hakkında bilgilendirilecektir", "defaultNotificationMethod": "Varsayılan Bildirim <PERSON>", "emailOnly": "Sadece E-<PERSON>a", "smsOnly": "<PERSON><PERSON>e <PERSON>", "bothEmailAndSMS": "<PERSON><PERSON> E-posta hem de <PERSON>", "noNotificationsDisabled": "<PERSON><PERSON><PERSON><PERSON> (Devre Dışı)", "importantNote": "Önemli Not", "globalDefaultSettingsInfo": "Bunlar genel varsayılan ayarlardır. Bireysel veli iletişim tercihleri bu varsayılanları geçersiz kılacaktır.", "emailNotificationTemplates": "E-posta Bildirim <PERSON>", "customizeEmailTemplates": "Velilere gönderilen e-posta şablonlarını özelleştirin", "templateType": "Şablon Türü", "newRequest": "<PERSON><PERSON>", "approved": "Onaylandı", "rejected": "Reddedildi", "emailTemplate": "E-posta Şablonu", "availableVariables": "Kullan<PERSON>la<PERSON><PERSON>", "smsNotificationTemplates": "SMS Bildirim Ş<PERSON>ları", "customizeSmsTemplates": "Velilere gönderilen SMS şablonlarını özelleştirin", "smsTemplate": "SMS Şablonu", "keepSmsMessagesConcise": "SMS mesajlarını kısa tutun (standart SMS için 160 karakter veya daha az)", "characters": "{{count}} karakter", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parentNotificationSettingsUpdated": "<PERSON><PERSON> bildirim a<PERSON> başarıyla güncellendi", "excuseSubmissionSettingsUpdated": "Mazeret gönderme ayarları başarıyla güncellendi", "failedToSaveNotificationSettings": "<PERSON><PERSON><PERSON><PERSON> kaydedilemedi", "confirmResetNotificationSettings": "Tüm bildirim a<PERSON>larını varsayılana sıfırlamak istediğinizden emin misiniz?", "settingsReset": "Ayarlar Sıfırlandı", "parentNotificationSettingsReset": "Veli bildirim a<PERSON>ları varsayılan değerlere sıfırlandı", "failedToLoadNotificationSettings": "<PERSON><PERSON><PERSON><PERSON> yüklenemedi", "generalSettingsTab": "<PERSON><PERSON>", "templateVariablesTab": "Şablon <PERSON>ğişkenleri", "emailTemplatesTab": "E-posta Şablonları", "smsTemplatesTab": "SMS Şablonları", "templateVariablesConfiguration": "Şablon Değişkenleri Yapılandırması", "templateVariablesConfigurationDescription": "Bildirim mesajlarında kullanılan şablon değişkenlerinin değerlerini yapılandırın. Bu değişkenler şablonlarınızda {{schoolName}}, {{contactEmail}} ve {{schoolPolicy}} yerine geçecektir.", "schoolName": "Okul Adı", "schoolNamePlaceholder": "Okul adınızı girin", "schoolNameHelp": "<PERSON><PERSON>, şablonlarınızda {{schoolName}} yerine geçecektir", "contactEmail": "İletişim E-postası", "contactEmailPlaceholder": "<EMAIL>", "contactEmailHelp": "<PERSON><PERSON>, şablonlarınızda {{contactEmail}} yerine geçecektir", "schoolAttendancePolicy": "Okul Devam Politikası", "schoolAttendancePolicyPlaceholder": "Okulunuzun devam politikası bilgilerini girin", "schoolAttendancePolicyHelp": "<PERSON><PERSON>, şablonlarınızda {{schoolPolicy}} yerine geçecektir", "templateVariables": "Şablon <PERSON>ğişkenleri", "templateVariablesInfo": "Bu değişkenler e-posta ve SMS şablonlarınızda kullanılabilir. Bu değerleri güncelledikten sonra değişikliklerinizi kaydetmeyi unutmayın.", "templatesUpdated": "Şablonlar <PERSON>", "templatesUpdatedToCurrentLanguage": "E-posta ve SMS şablonları mevcut dile uyacak şekilde gü<PERSON>di.", "updateToCurrentLanguage": "<PERSON><PERSON><PERSON>"}, "biometricDeletionRequests": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Öğrencilerin biyometrik kimlik bilgilerini silme taleplerini yönetin", "loading": "Yükleniyor...", "pendingRequests": "<PERSON><PERSON><PERSON>", "processedRequests": "İşlenmiş Talepler", "noPendingRequests": "<PERSON><PERSON><PERSON>", "noPendingRequestsMessage": "Tüm biyometrik silme talepleri işlenmiştir.", "student": "<PERSON><PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "status": "Durum", "requestDate": "<PERSON><PERSON>", "processedDate": "<PERSON><PERSON><PERSON><PERSON>", "actions": "İşlemler", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "pending": "Beklemede", "approved": "Onaylandı", "rejected": "Reddedildi", "requestDetails": "<PERSON><PERSON>", "requestDetailsDescription": "Bu biyometrik silme talebi hakkında detaylı bilgileri görüntüleyin", "studentName": "Öğrenci <PERSON>ı", "studentEmail": "Öğrenci E-postası", "studentNumber": "Öğren<PERSON>", "requestReason": "<PERSON><PERSON>", "adminNotes": "Yönetici Notları", "approveRequest": "<PERSON><PERSON><PERSON>", "approveRequestDescription": "Bu, öğrencinin biyometrik kimlik bilgilerini kalıcı olarak silecektir. Bu işlem geri alınamaz.", "rejectRequest": "<PERSON><PERSON><PERSON>", "rejectRequestDescription": "Öğrencinin biyometrik kimlik bilgileri aktif kalacaktır.", "rejectionReason": "<PERSON>", "adminNotesPlaceholder": "Bu karar hakkında isteğe bağlı notlar...", "rejectionReasonPlaceholder": "Red nedeni (isteğe bağlı)...", "approving": "Onaylanıyor...", "rejecting": "Reddediliyor...", "requestApproved": "<PERSON><PERSON>", "credentialsDeleted": "Öğrencinin biyometrik kimlik bilgileri kalıcı olarak silindi.", "requestRejected": "<PERSON><PERSON>", "studentNotified": "Öğrenci karar hakkında bilgilendirildi.", "errorLoading": "<PERSON><PERSON>", "errorLoadingMessage": "Biyometrik silme talepleri yüklenemedi. Lütfen tekrar deneyin.", "errorApproving": "<PERSON><PERSON>", "errorApprovingMessage": "<PERSON><PERSON><PERSON> talebi onay<PERSON>. Lütfen tekrar deneyin.", "errorRejecting": "<PERSON><PERSON>", "errorRejectingMessage": "<PERSON><PERSON>e talebi reddedilemedi. Lütfen tekrar deneyin.", "clearProcessed": "İşlenenleri Temizle", "clear": "<PERSON><PERSON><PERSON>", "clearing": "Temizleniyor...", "clearAll": "<PERSON><PERSON><PERSON>", "clearProcessedRequests": "İşlenmiş Talepleri Temizle", "clearProcessedRequestsDescription": "{{count}} işlenmiş talebi kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "processedRequestsCleared": "İşlenmiş Talepler Temizlendi", "processedRequestsClearedMessage": "{{count}} işlenmiş talep başarıyla temizle<PERSON>.", "requestCleared": "<PERSON><PERSON>", "requestClearedMessage": "Talep başar<PERSON><PERSON> te<PERSON>.", "errorClearing": "Temizleme Hatası", "errorClearingMessage": "Talepler temizlenirken bir hata oluştu. Lütfen tekrar deneyin."}, "tablets": {"title": "Tablet Yönetimi", "subtitle": "Odalarınız için devam tabletlerini yapılandırın ve yönetin", "setupQR": {"title": "Tablet Kurulum QR Üretici", "generateTitle": "Kurulum QR Kodu Oluştur", "generateSubtitle": "Tabletlerin belirli bir oda ve okul için kendilerini otomatik olarak yapılandırmak üzere tarayabilecekleri bir QR kodu oluşturun.", "school": "<PERSON><PERSON>", "selectSchool": "Bir okul seçin", "block": "Blok (İsteğe Bağlı)", "selectBlock": "Bir blok seçin", "room": "<PERSON><PERSON>", "selectRoom": "<PERSON><PERSON> oda seçin", "deviceName": "C<PERSON>az Adı (İsteğe Bağlı)", "deviceNamePlaceholder": "ö<PERSON><PERSON>, Oda 101 Tablet", "setupQRCode": "Kurulum QR Kodu", "setupInstructions": "<PERSON><PERSON><PERSON>ları:", "instruction1": "Tabletin aynı WiFi ağına bağlı olduğundan emin olun", "instruction2": "Tabletin kamerasını veya QR tarayıcı uygulamasını açın", "instruction3": "Yukarıdaki QR kodunu tarayın", "instruction4": "Tablet otomatik olarak kurulum sayfasını açacak", "instruction5": "Otomatik kayıt ve yapılandırmayı bekleyin", "note": "Not:", "noteText": "<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> manuel kurulum URL'sini kopyalayın ve tabletin tarayıcısına yapıştırın.", "manualSetupURL": "<PERSON>RL'<PERSON>", "downloadQR": "QR İndir", "copySetupURL": "<PERSON><PERSON>lum URL'sini <PERSON>", "secureSetup": "<PERSON>ü<PERSON><PERSON>:", "secureSetupText": "Her tablet gü<PERSON><PERSON> kimlik doğrulama için benzersiz bir cihaz sertifikası ile kaydedilecektir."}, "display": {"setupRequired": "Tablet Kurulum<PERSON>", "setupRequiredDescription": "Bu tableti yoklama takibi için yapılandırmak üzere yöneticiniz tarafından sağlanan kurulum QR kodunu tarayın.", "retrySetup": "<PERSON><PERSON><PERSON><PERSON>", "scanQRInstruction": "📱 Yoklamaya katılmak için bu QR kodu telefonunuzla tarayın", "roomVerification": "Taramadan önce atanmış odanızda olduğunuzdan emin olun", "noActiveQR": "Aktif QR Kodu Yok", "waitingForSession": "Yo<PERSON><PERSON> otur<PERSON>un başlaması bekleniyor...", "attendanceQRCode": "Yoklama QR Kodu", "connected": "Bağlı", "disconnected": "Bağlantı Kesildi", "lastUpdate": "<PERSON> günce<PERSON><PERSON>:", "capacity": "Kapasite:", "recentCheckins": "<PERSON> <PERSON><PERSON>", "checkedIn": "giriş yaptı!", "timeRemaining": "<PERSON><PERSON>:", "active": "Aktif", "expiringSoon": "Ya<PERSON><PERSON>nda Sona Eriyor", "refreshing": "🔄 Yenileniyor...", "preparingQR": "QR Kodu Hazırlanıyor...", "attendanceClosed": "<PERSON><PERSON><PERSON>", "qrWillAppear": "QR kodu yoklama saatleri boyunca otomatik olarak görünecek", "opensAt": "Yoklama açılış saati", "waitingConfig": "Yoklama yapılandırması bekleniyor...", "checkUpdates": "Güncellemeleri Kontrol Et", "scanToMarkAttendance": "Yoklamaya Katılmak İçin <PERSON>n", "openCameraApp": "Kamera uygulamanızı açın ve QR koduna doğrultun", "clickMaximize": "Daha büyük QR kodu için büyütme düğmesine tıklayın", "todaysScans": "Bugünkü Taramalar", "scansToday": "<PERSON><PERSON><PERSON> ta<PERSON>", "authenticated": "Kimlik Doğrulandı", "excellent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "good": "İyi", "poor": "<PERSON><PERSON><PERSON><PERSON>", "offline": "Çevrimdışı", "closed": "KAPALI", "liveAttendance": "Canlı Yoklama", "block": "Blok", "floor": "<PERSON>", "loading": "Yükleniyor...", "testScan": "Test Tarama", "checkedInSuccessfully": "Başarıyla giriş yapıldı! ✨", "lateArrival": "Geç geliş kaydedildi 📝", "earlyBird": "Erken kuş! 🐦", "new": "YENİ", "lessThanMinuteAgo": "bir dakikadan az ö<PERSON>", "waitingForScans": "Taramalar bekleniyor...", "studentAttendanceRealTime": "Öğrenci yoklaması burada gerçek zamanlı olarak görünecek", "checkingUpdates": "Her 10 saniyede güncellemeler kontrol ediliyor", "unknownTablet": "Bilinmeyen Tablet", "tabletConfigured": "Tablet Yapılandırıldı! 🎉", "successfullyRegistered": "{{deviceName}} o<PERSON><PERSON> başarı<PERSON> ka<PERSON>il<PERSON>", "maximizeQRCode": "QR Kodunu Bü<PERSON>t", "refreshQRCode": "QR Kodunu <PERSON>"}, "manage": {"title": "Kayıtlı Tabletler", "subtitle": "Tüm kayıtlı tablet cihazlarını izleyin ve yönetin", "refresh": "<PERSON><PERSON><PERSON>", "loading": "Yükleniyor...", "noTablets": {"title": "Kayıtlı Tablet Yok", "subtitle": "İlk tabletinizi kaydetmek için kurulum QR kodları oluşturun", "setupFirst": "İlk Tableti Kur"}}}, "socialMedia": {"title": "<PERSON><PERSON><PERSON> Medya <PERSON>", "description": "Öğrenci panosunda güncellemeleri görüntülemek için okulunuzun sosyal medya kanallarını yapılandırın.", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "Devre Dışı", "settings": "<PERSON><PERSON><PERSON>", "setupGuide": "<PERSON><PERSON><PERSON>", "enableFeed": "Sosyal Medya Akışını Etkinleştir", "enableFeedDescription": "Öğrenci panosunda sosyal medya güncellemelerini göster", "globalSettings": "<PERSON><PERSON>", "refreshInterval": "<PERSON><PERSON><PERSON><PERSON> (dakika)", "refreshIntervalDescription": "<PERSON>ni gönderileri ne sıklıkla kontrol etmek (5-1440 dakika)", "showEngagementStats": "Etkileşim İstatistiklerini Göster", "platformConfiguration": "Platform Yapılandırması", "showEmbedCodes": "Gömme Kodlarını Göster", "hideEmbedCodes": "Gömme Kodlarını Gizle", "schoolWebsite": "Okul Web Sitesi", "platformIntegration": "{{platform}} entegrasyonu", "instagramUsername": "Instagram Kullanıcı Adı", "twitterUsername": "Twitter Kullanıcı Adı", "usernamePlaceholder": "@okulunuz", "settingsUpdatedSuccessfully": "Sosyal medya entegrasyon ayarları başarıyla güncellendi.", "failedToLoadSettings": "Sosyal medya a<PERSON> yüklenemedi", "failedToSaveSettings": "Sosyal medya a<PERSON>ı kaydedilemedi", "websiteUrl": "Web Site URL'si", "rssFeedUrl": "RSS Beslemesi URL'si (İsteğe Bağlı)", "youtubeChannelUrl": "YouTube Kanal URL'si", "youtubeChannelId": "YouTube Kanal ID'si", "facebookPageUrl": "Facebook Sayfa URL'si", "embedCode": "<PERSON><PERSON><PERSON> (Gerçek Gönderiler İçin)", "toShowRealPosts": "Gerçek gönderileri göstermek için:", "embedInstructions": {"facebook": {"step1": "Facebook Sayfa Eklentisi'ne gidin", "step2": "Facebook sayfa URL'nizi girin", "step3": "Ayarları yapılandırın (zaman çizelgesi göster, genişlik, yükseklik)", "step4": "Oluşturulan HTML kodunu kopyalayın ve buraya yapıştırın"}, "twitter": {"step1": "Twitter Yayınla'ya gidin", "step2": "Twitter profil URL'nizi girin", "step3": "\"Gömülü Zaman Çizelgesi\"ni seçin", "step4": "Oluşturulan HTML kodunu kopyalayın ve buraya yapıştırın"}, "instagram": {"step1": "Gömmek istediğiniz Instagram gönderisine gidin", "step2": "<PERSON>ç nokta (...) menüsüne tıklayın", "step3": "\"<PERSON>ömme\"yi seçin", "step4": "<PERSON><PERSON><PERSON> kodunu kopyalayın ve buraya yapıştırın", "note": "Not: Instagram besleme gömmelerine izin vermez, sadece tekil gö<PERSON>e"}, "youtube": {"title": "En Son 5 Videoyu Gösterir:", "step1": "YouTube Studio → Ayarlar → Kanal → Gelişmiş'e gidin", "step2": "Kanal ID'nizi <PERSON> (\"UC\" ile başlar)", "step3": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"YouTube Kanal ID'si\" alanına yapıştırın", "step4": "Duyarlı video oynatma listesi gömmelerini oluşturuyoruz!", "note": "Öğrenciler en son videolarınızı doğrudan izleyebilir!"}, "default": "Platformun gömme talimatlarını takip edin"}, "youtubeHelp": {"title": "En Son 6 Videoyu Gösterir:", "enterChannelId": "YouTube Kanal <PERSON>'nizi girin (\"UC\" ile ba<PERSON>)", "gridLayout": "6 ayrı video ile güzel ızgara düzeni oluşturur", "individualVideos": "<PERSON><PERSON><PERSON><PERSON><PERSON> her videoyu ayrı ayrı izleyebilir", "findChannelId": "Kanal ID'nizi bulun: YouTube Studio → Ayarlar → Kanal → Gelişmiş", "perfect": "Mükemmel: Her video uygun görüntü için indeks parametresi ile gösterilir"}, "embedPlaceholders": {"facebook": "Facebook Sayfa Eklentisi gömme kodunu buraya yapıştırın...", "twitter": "<a class=\"twitter-timeline\" href=\"https://twitter.com/kullaniciadi?ref_src=twsrc%5Etfw\">kullaniciadi tarafından tweetler</a> <script async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"></script>", "instagram": "Instagram'ın gömme özelliğinden gelen Instagram gömme kodu", "youtube": "✨ YENİ: <PERSON><PERSON><PERSON><PERSON><PERSON> \"YouTube Kanal URL'si\" alanını kullanın! Gömme kodu gerekmez. Özel gömmeler için iframe kodunu buraya yapıştırın.", "default": "Platformdan gömme kodunu yapıştırın..."}, "guide": {"title": "Gerçek Sosyal Medya Gönderilerini Nasıl Gösterilir", "subtitle": "Okulunuzun sosyal medya hesaplarından gerçek gönderileri görüntülemek için bu kılavuzları takip edin", "steps": "Adımlar:", "platformDescriptions": {"facebook": "Facebook say<PERSON><PERSON><PERSON><PERSON><PERSON> zaman çizelgesini son gö<PERSON><PERSON><PERSON> birlikte gö<PERSON>in", "twitter": "Twitter zaman çizelgenizi en son tweetlerle birlikte gömün", "instagram": "Tekil Instagram gönderilerini gömün (besleme mevcut değil)", "youtube": "✨ Güzel ızgara düzeninde en son 6 videoyu gösterir!"}, "difficulty": {"easy": "<PERSON><PERSON>", "medium": "Orta", "superEasy": "Çok <PERSON>"}, "badges": {"realTime": "Gerçek Zamanlı", "individualPosts": "<PERSON><PERSON><PERSON>"}, "buttons": {"openTool": "{{platform}} Aracını Aç", "copySampleCode": "Örnek Kodu Kopyala", "viewSampleCode": "Örnek Kodu Görüntüle", "copied": "Kopyalandı!"}, "importantNotes": "<PERSON><PERSON><PERSON><PERSON>", "notes": {"facebookTwitter": "Facebook ve Twitter: <PERSON><PERSON>le otomatik olarak güncellenen gerçek zamanlı beslemeler sağlar", "instagram": "Instagram: API kısıtlamaları nedeniyle sadece tekil gönderilerin gömülmesine izin verir, tam beslemelere değil", "youtube": "YouTube: Ka<PERSON> beslemelerini veya tekil videoları gömebilir", "demoMode": "Demo Modu: <PERSON><PERSON><PERSON> kodları <PERSON>ında, sistem gösteri için örnek gönderiler gösterir"}}, "copied": "Kopyalandı!", "codeCopied": "{{label}} örnek kodu panoya kopyalandı"}, "excuseCleanup": {"title": "Mazeret Süresi Dolma ve Temizlik", "description": "Süresi dolan mazeretleri otomatik olarak temizle ve yoklama durumunu geri dö<PERSON>r", "status": "Durum", "running": "Çalışıyor", "stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expiredExcuses": "<PERSON><PERSON><PERSON><PERSON>", "lastCleanup": "<PERSON>", "expiredExcusesFound": "Süresi Dolan Mazeretler Bulundu", "expiredExcusesFoundMessage": "Temizlenmesi gereken {{count}} süresi dolan mazeret var. Yoklama durumunu geri döndürmek ve süresi dolan kayıtları kaldırmak için temizlik işlemini çalıştırın.", "enableAutomaticCleanup": "Otomatik Temizliği Etkinleştir", "enableAutomaticCleanupDescription": "Süresi dolan mazeretleri otomatik olarak temizle ve yoklama durumunu geri dö<PERSON>r", "checkInterval": "<PERSON><PERSON><PERSON> (dakika)", "checkIntervalDescription": "Süresi dolan mazeretleri ne sıklıkla kontrol edeceği (5-1440 dakika)", "notifyStudents": "Öğrencileri Bilgilendir", "notifyStudentsDescription": "Mazeretlerinin süresi dolduğunda öğrencilere bildirim gönder", "saveSettings": "Ayarları Kaydet", "runCleanupNow": "Şimdi Temizlik Çalıştır", "checkExpired": "Süresi Dolanları Kontrol Et", "checking": "<PERSON><PERSON><PERSON>", "howItWorks": "Nasıl <PERSON>alışır", "automaticCleanup": "Otomatik Temizlik", "automaticCleanupDescription": "Belirtilen aralıklarla süresi dolan mazeretleri kontrol eder", "statusReversion": "Durum Geri Döndürme", "statusReversionDescription": "<PERSON><PERSON><PERSON> du<PERSON> '<PERSON><PERSON><PERSON>'den 'yok'a geri <PERSON>", "recordCleanup": "<PERSON><PERSON><PERSON>", "recordCleanupDescription": "Süresi dolan onaylanmış mazeretleri ve eski reddedilen mazeretleri (7+ gün) kaldırır", "studentNotifications": "Öğrenci Bildirimleri", "studentNotificationsDescription": "İsteğe bağlı olarak mazeretlerinin süresi dolduğunda öğrencileri bilgilendirir", "settingsSaved": "<PERSON><PERSON><PERSON>", "settingsSavedMessage": "Mazeret temizlik ayarları başarıyla güncellendi", "cleanupCompleted": "Temizlik <PERSON>amlandı", "cleanupCompletedMessage": "{{count}} s<PERSON><PERSON><PERSON> dolan mazeret başar<PERSON><PERSON> işlendi", "cleanupFailed": "Temizlik Başarısız", "cleanupFailedMessage": "Mazeret temizliği çalıştırılamadı", "checkComplete": "<PERSON><PERSON><PERSON>", "checkCompleteMessage": "{{count}} s<PERSON><PERSON><PERSON> dolan mazeret bulundu", "errorLoadingSettings": "Mazeret temizlik ayarları yüklenemedi", "errorSavingSettings": "Mazeret temizlik ayarları kaydedilemedi", "errorCheckingExpired": "<PERSON><PERSON><PERSON>i dolan mazeretler kontrol edilemedi"}, "schoolSettings": {"title": "Okul Ayarları", "description": "Okul ayarlarınızı ve görünümünüzü özelleştirin", "general": "<PERSON><PERSON>", "branding": "<PERSON><PERSON>", "messages": "<PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "security": "Güvenlik", "carousel": "Slayt Gösterisi", "carouselManagement": "Slayt Gösterisi Yönetimi", "carouselManagementDescription": "Öğrenci ve öğretmen panellerinde görüntülenecek slayt gösterisi içeriğini yükleyin ve yönetin.", "noCarouselContent": "Henüz slayt gösterisi içeriği yok", "addCarouselContent": "Slayt gösterisinde görüntülenecek resim ekleyin.", "addContent": "İçerik Ekle", "carouselSettings": {"title": "Başlık", "description": "Öğrenci ve öğretmen panelleri için slayt gösterisi içeriğini yönetin", "items": "<PERSON><PERSON><PERSON>", "addItem": "<PERSON><PERSON><PERSON>", "editItem": "Slayt Öğesini Düzenle", "noItems": "Henüz slayt öğesi yok. Başlamak için ilk öğenizi ekleyin.", "order": "<PERSON><PERSON><PERSON>", "image": "Resim", "audience": "<PERSON><PERSON><PERSON>", "status": "Durum", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "itemTitle": "Başlık", "itemDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "itemTitlePlaceholder": "<PERSON>u öğe için bir başl<PERSON>k girin", "itemDescriptionPlaceholder": "Bir açıklama girin (isteğe bağlı)", "imageUpload": "<PERSON><PERSON><PERSON>", "targetAudience": "<PERSON><PERSON><PERSON>", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teachers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admins": "Okul Yöneticileri", "adminAudienceHint": "Bu slayt öğesini kendi yönetici panelinizde gösterin", "activeItem": "Aktif", "itemFormDescription": "Bu slayt öğesi için ayrıntıları doldurun.", "titleRequired": "Başlık gereklidir", "mediaRequired": "Lütfen bir resim seçin", "audienceRequired": "Lütfen en az bir hedef kitle seçin", "itemUpdated": "<PERSON><PERSON><PERSON> öğ<PERSON> başarıyla güncellendi", "itemAdded": "Slayt öğesi başarıyla eklendi", "confirmDelete": "<PERSON>u öğeyi silmek istediğinizden emin misiniz?", "itemDeleted": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "invalidFileType": "Lütfen bir resim dosyası seçin.", "errorLoading": "Slayt öğeleri yü<PERSON> hata oluştu", "errorSaving": "<PERSON><PERSON>t öğesi kaydedilirken hata oluştu", "errorDeleting": "<PERSON><PERSON><PERSON> öğesi si<PERSON>en hata oluştu", "errorToggling": "<PERSON><PERSON><PERSON> öğesi durumu güncellenirken hata oluştu", "errorReordering": "<PERSON>layt öğeleri yeniden sıralanırken hata oluştu", "itemActivated": "<PERSON><PERSON><PERSON> öğ<PERSON>ştiril<PERSON>", "itemDeactivated": "Slayt öğesi devre dışı bırakıldı", "fileTooLarge": "Resim dosyası çok büyük. Maksimum boyut 5MB'dır.", "errorReadingFile": "Resim dosyası okunurken hata oluştu. Lütfen tekrar deneyin."}, "schoolName": "Okul Adı", "enterSchoolName": "Okul adını girin", "schoolLogo": "Okul Logosu", "uploadLogo": "<PERSON><PERSON>", "primaryColor": "<PERSON>", "secondaryColor": "İkincil Renk", "primaryElementsWillUseThisColor": "<PERSON> bu rengi kull<PERSON>k", "secondaryElementsWillUseThisColor": "İkincil elementler bu rengi kull<PERSON>k", "colorPreview": "Ren<PERSON>", "colorsArePreviewedInRealTime": "Renkler gerçek zamanlı olarak önizlenir. Bu değişiklikleri kalıcı olarak uygulamak için \"Markayı Kaydet\" düğmesine tıklayın.", "primaryButton": "<PERSON>", "secondaryButton": "<PERSON><PERSON><PERSON><PERSON>", "noLogoUploaded": "<PERSON><PERSON>", "changeLogo": "Logoyu <PERSON>", "chooseLogo": "Logo Seç", "remove": "Kaldır", "newLogoSelected": "<PERSON>ni <PERSON> Seçildi", "confirmRemoveLogo": "Okul logosunu kaldırmak istediğinizden emin misiniz? Bu işlem geri alınamaz.", "logoRemoved": "Logo Kaldırıldı", "logoRemovedSuccess": "Okul logonuz başarıyla kaldırıldı.", "logoRemoveError": "Logo kaldırılırken bir hata oluştu. Lütfen tekrar deneyin.", "uploadFailed": "Yükleme Başarısız", "uploadFailedDescription": "Logonuzu yüklerken bir hata oluştu. Lütfen tekrar deneyin.", "clickSaveBrandingToUpload": "Bu logoyu yüklemek ve uygulamak için \"Markayı Kaydet\" düğ<PERSON>ine tıklayın.", "yourSchoolColorsAndLogo": "Okul renkleriniz ve logonuz, tutarlı bir marka deneyimi oluşturmak için uygulama genelinde kullanılacaktır.", "savedSuccessfully": "Başarıyla kaydedildi!", "saving": "Kay<PERSON>ili<PERSON>r...", "uploading": "Yükleniyor...", "saved": "<PERSON><PERSON><PERSON><PERSON>", "saveBranding": "Markayı Kaydet", "emailNotifications": "E-posta Bildirimleri", "smsNotifications": "SMS Bildirimleri", "enableEmailNotificationsForStudentsAndParents": "Öğrenciler ve veliler için e-posta bildirimlerini etkinleştir", "enableSmsNotificationsForStudentsAndParents": "Öğrenciler ve veliler için SMS bildirimlerini etkinleştir", "notificationPreferencesDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, ok<PERSON><PERSON>z için varsayılan bildirim tercihlerini kontrol eder. Bireysel kullanıcılar bu ayarları profillerinde geçersiz kılabilir.", "saveNotificationSettings": "<PERSON><PERSON><PERSON>im <PERSON>larını Kaydet", "systemAdminOverride": "Sistem Yöneticisi Geçersiz Kılma", "settingsOverriddenBySystemAdmin": "Bazı ayarlar sistem yöneticisi tarafından geçersiz kılınmıştır ve değiştirilemez.", "settingControlledBySystemAdmin": "Bu ayar sistem yöneticisi tarafından kontrol edilmektedir", "overridden": "Geçersiz Kılındı", "schoolInvitationCode": "<PERSON><PERSON>", "missingInvitationCode": "<PERSON><PERSON>", "missingInvitationCodeDescription": "Okulunuzun bir davet kodu yok. Öğrenciler bu olmadan kayıt olamaz. Lütfen aşağıdaki düğmeyi kullanarak bir davet kodu oluşturun.", "noInvitationCodeGeneratedYet": "<PERSON>n<PERSON>z davet kodu oluşturulmadı", "show": "<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON>", "currentInvitationCode": "Mev<PERSON> <PERSON>:", "copiedToClipboard": "Panoya kopyalandı", "invitationCodeCopiedToClipboard": "Davet kodu panoya kopyalandı", "invitationCodeDescription": "<PERSON><PERSON> kod<PERSON>, yeni kullanı<PERSON>ıların okulunuza katılması için gereklidir. <PERSON><PERSON> kod, siz yeniden oluşturana kadar geçerli kalacaktır. Erişimi iptal etmeniz gerekirse istediğiniz zaman yeniden oluşturabilirsiniz.", "regenerating": "Yeniden oluşturuluyor...", "generateInvitationCode": "Davet Ko<PERSON> Oluştur", "regenerateInvitationCode": "<PERSON><PERSON>", "studentDashboardMessage": "Öğrenci Paneli Mesajı", "studentMessagePlaceholder": "Öğrenci panellerinde gösterilecek bir mesaj girin", "studentMessageDescription": "<PERSON><PERSON> mesaj, okulunuzdaki tüm öğrencilerin panelinin üst kısmında gösterilecektir.", "teacherDashboardMessage": "Öğretmen Paneli Mesajı", "teacherMessagePlaceholder": "Öğretmen panellerinde gösterilecek bir mesaj girin", "teacherMessageDescription": "<PERSON><PERSON> mesaj, okulunuzdaki tüm öğretmenlerin panelinin üst kısmında gösterilecektir.", "dashboardMessages": "Panel Mesajları", "dashboardMessagesInfo": "Panel mesajlarını öğrencilere ve öğretmenlere önemli bilgileri iletmek için kullanın. <PERSON><PERSON> mesaj<PERSON>, ilgili panellerin üst kısmında görünecektir.", "emptyMessageInfo": "Bir mesajı o kullanıcı tipinin panelinden kaldırmak için alanı boş bırakın.", "brandingSettingsSaved": "Marka A<PERSON>ları Kaydedildi", "brandingSettingsSavedMessage": "Okul marka ayarlarınız başarıyla güncellendi.", "dashboardMessagesSaved": "Panel Mesajları Kaydedildi", "dashboardMessagesSavedMessage": "Panel mesajlarınız başarıyla güncellendi.", "saveMessages": "Mesajları Kaydet"}, "profile": {"adminProfile": "Yönetici Profili", "completeYourProfile": "Yönetici Profilinizi <PERSON>n", "completeProfile": "<PERSON><PERSON>", "updateProfileInfo": "Profil bilgiler<PERSON>zi gü<PERSON><PERSON>n", "viewAndManageProfile": "Yönetici profilinizi görüntüleyin ve yönetin", "fillRequiredFields": "Profil kurulumunuzu tamamlamak için lütfen tüm gerekli alanları doldurun.", "profileSetupComplete": "<PERSON>il kuru<PERSON> ta<PERSON>ı", "profileSetupMessage": "Profiliniz başarıyla kuruldu. Artık tüm özelliklere erişebilirsiniz.", "position": "Pozisyon", "adminId": "Yönetici Kimliği", "enterAdminId": "Yönetici kimliğinizi girin", "enterPosition": "Pozisyonunuzu girin", "enterSchoolName": "Okul adınızı girin", "enterFullName": "Tam adınızı girin", "schoolName": "Okul Adı", "incompleteProfile": "Yönetici profiliniz eksik. Tüm özelliklere erişmek için lütfen profilinizi tamamlayın.", "goToProfileTab": "<PERSON><PERSON>"}, "alerts": {"title": "Yönetici Uyarı Paneli", "description": "<PERSON><PERSON><PERSON> o<PERSON>aki yoklama uyarılarını izleyin ve yönetin", "pending": "Beklemede", "resolved": "Çözüldü", "dismissed": "Kapatıldı", "noAlerts": "{{status}} uyarı yok", "student": "<PERSON><PERSON><PERSON><PERSON>", "room": "<PERSON><PERSON>", "teacher": "Öğretmen", "time": "Zaman", "distance": "Mesafe", "actions": "İşlemler", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "dismiss": "Ka<PERSON><PERSON>", "alertApproved": "Uyarı Onaylandı", "alertRejected": "Uyarı Reddedildi", "alertDismissed": "Uyarı Kapatıldı", "loadingAlerts": "Uyarılar yükleniyor...", "success": "Başarılı", "error": "<PERSON><PERSON>", "attemptedToMarkAttendance": "Yoklama kaydetmeye çalıştı", "distanceFromRoom": "Odaya uzaklık", "meters": "metre", "viewStudentLocation": "Öğrenci Konumunu Görü<PERSON>üle", "viewRoomLocation": "<PERSON><PERSON>"}, "qrGenerator": {"title": "QR Kod Üreteci", "description": "Yoklama için QR kodları oluşturun ve yönetin", "selectBlock": "Blok Seç", "selectRoom": "<PERSON><PERSON>", "chooseBlock": "Bir blok seçin...", "chooseRoom": "<PERSON>ir oda seçin...", "block": "Blok", "floor": "<PERSON>", "regenerateQRCode": "QR Kodu Yenile", "qrCodeData": "QR Kod Verisi", "qrCodeDataJSON": "QR <PERSON><PERSON> (JSON)", "roomCapacity": "<PERSON><PERSON>", "students": "ö<PERSON><PERSON><PERSON>", "qrCodeSecurityFeatures": "QR Kod Güvenlik Özellikleri", "timeLimitedExpiration": "Zaman sınırlı süre sonu", "minute": " <PERSON><PERSON><PERSON>", "minutePlural": "", "fullUUIDPreservation": "Tam UUID korunması (kesme yok)", "completeCryptographicSignatures": "<PERSON> krip<PERSON>grafi<PERSON> imzalar", "roomAndBlockValidation": "Oda ve blok doğrulaması", "sessionBasedTracking": "Oturum tabanlı takip", "encryptedRoomIdentifier": "Şifrelenmiş oda tanımlayıcı", "timestampsToPrevent": "Tekrar saldırılarını önlemek için zaman damgaları", "qrChallengeRotates": "QR <PERSON><PERSON><PERSON> her {{interval}} sani<PERSON><PERSON> d<PERSON>, toplam {{total}} saniye geçerli", "securityPriority": "Güvenlik Önceliği: Tam format kullanıldı - veri kesintisi yok", "roomInformation": "<PERSON><PERSON>", "roomID": "Oda ID", "capacity": "Kapasite", "sessionID": "Oturum ID", "liveAttendance": "Canlı Yoklama", "noAttendanceRecorded": "Henüz yoklama kaydı yok", "qrCodesExpireAfter": "QR kodları güvenlik için {{minutes}} dakika sonra otomatik olarak sona erer", "selectBlockAndRoom": "QR kodları oluşturmak için bir blok ve oda seçin", "expiresIn": "<PERSON><PERSON><PERSON> sonu:"}, "fraudDetection": {"title": "Gelişmiş Hile Tespiti", "description": "Potansiyel yoklama hilesinin gelişmiş analizi ve izlenmesi", "overview": "Genel Bakış", "alerts": "uyarı", "location": "<PERSON><PERSON>", "devices": "Cihazlar", "totalAlerts": "Toplam Uyarılar", "pending": "be<PERSON><PERSON>e", "resolved": "ç<PERSON>züldü", "investigating": "İnceleniyor", "highRisk": "<PERSON><PERSON><PERSON><PERSON>", "mediumRisk": "Orta Risk", "lowRisk": "Düşük Risk", "unknownStudent": "Bilinmeyen Öğrenci", "unknown": "Bilinmeyen", "ofTotalAlerts": "toplam uyarıların %'si", "locationFraud": "<PERSON><PERSON>", "deviceFraud": "<PERSON><PERSON><PERSON>", "fraudDistribution": "<PERSON><PERSON> Dağılımı", "locationBased": "Konum tabanlı", "deviceBased": "Cihaz tabanlı", "timeBased": "Zaman tabanlı", "patternBased": "Pattern-based", "riskAssessment": "<PERSON>", "unread": "Okunmamış", "fraudDistributionTitle": "<PERSON><PERSON> Dağılımı", "searchAlerts": "Uyarıları ara...", "filters": "<PERSON><PERSON><PERSON><PERSON>", "filterAlerts": "Uyarıları Filtrele", "severity": "<PERSON>ne<PERSON>", "allSeverities": "<PERSON>üm önem dereceleri", "type": "<PERSON><PERSON><PERSON>", "allTypes": "<PERSON><PERSON><PERSON>", "status": "Durum", "allStatuses": "<PERSON><PERSON><PERSON> du<PERSON>", "resetFilters": "Filtreleri Sıfırla", "showing": "Gösteriliyor", "of": "/", "noFraudAlertsDetected": "Hen<PERSON>z hile uyarısı tespit edilmedi", "noAlertsMatchFilters": "Mevcut filtrelerinizle eşleşen uyarı yok", "actions": "İşlemler", "markAsInvestigating": "İnceleniyo<PERSON>", "markAsResolved": "Çözüldü Olarak İşaretle", "deleteAlert": "Uyarıyı Sil", "room": "<PERSON><PERSON>", "distance": "Mesafe", "exceededBy": "Aşılan", "deviceChange": "Cihaz değişikliği", "viewLocation": "<PERSON><PERSON><PERSON>", "getDirections": "<PERSON><PERSON>", "locationBasedFraudDetection": "Konum Tabanlı Hile Tespiti", "monitorAttendanceVerification": "Şüpheli konumlardan yoklama doğrulama girişimlerini izleyin", "distanceViolations": "<PERSON><PERSON>", "studentsAttemptingOutsideRadius": "Öğrenciler izin verilen yarıçapın dışından yoklama kaydetmeye çalışıyor", "geofenceViolations": "Coğrafi <PERSON>", "studentsOutsideCampus": "Ka<PERSON><PERSON>s sınırları dışındaki öğrenciler", "locationSpoofing": "Konum Sahteciliği", "detectedGpsSpoofing": "Tespit edilen GPS/konum sahteciliği girişimleri", "locationFraudPreventionTips": "Konum Hilesi Önleme İpuçları", "adjustRoomGeofenceRadius": "Oda Coğrafi Sınır Yarıçapını Ayarlayın", "configureGeofenceRadius": "Her oda için bina büyüklüğü ve kampüs düzenine göre uygun coğrafi sınır yarıçapını yapılandırın. Mevcut ortalama yarıçap: 50m", "enableMultiFactorVerification": "Çok Faktörlü Doğrulamayı Etkinleştirin", "requireBothLocation": "Yüksek riskli alanlarda veya sınav dönemlerinde hem konum doğrulaması hem de biyometrik/PIN kimlik doğrulaması gerektirin.", "implementLocationConsistency": "Konum Tutarlılık Kontrollerini Uygulayın", "flagSuspiciousChanges": "Fiziksel olarak imkansız olacak şüpheli hızlı konum değişikliklerini işaretleyin.", "deviceBasedFraudDetection": "Cihaz Tabanlı Hile Tespiti", "monitorSuspiciousDevicePatterns": "Şüpheli cihaz modellerini ve tutarsızlıkları izleyin", "deviceInconsistencies": "Cihaz Tutarsızlıkları", "studentsUsingDifferentDevices": "Öğrenciler kısa zaman aralıklarında farklı cihazlar kullanıyor", "concurrentSessions": "Eşzamanlı Oturumlar", "multipleAttendanceRecords": "Farklı konumlardan birden fazla yoklama kaydı", "emulatorDetection": "Emülatör Tespiti", "detectedEmulatorUsage": "Tespit edilen emülatör veya sanal cihaz kullanımı", "deviceFraudPreventionTips": "Cihaz Hilesi Önleme İpuçları", "implementDeviceFingerprinting": "Cihaz Parmak İzi Uygulaması", "trackUniqueDeviceCharacteristics": "Öğrencilerin kimlik bilgilerini paylaştığını tespit etmek için benzersiz cihaz özelliklerini takip edin.", "enforceDeviceRegistration": "Cihaz Kaydını Zorunlu Kılın", "requireStudentsToRegister": "Öğrencilerin birincil cihazlarını kaydetmelerini ve yoklama işaretlemeyi kayıtlı cihazlarla sınırlandırın.", "detectEmulatorsAndRooted": "Emülatörleri ve Rootlu Cihazları Tespit Edin", "identifyAndBlockAttendance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> veya jailbreak ya<PERSON><PERSON><PERSON><PERSON><PERSON> cihazlardan yoklama doğrulamasını tespit edin ve engelleyin.", "newFraudAlert": "<PERSON>ni <PERSON> Uyarısı", "newFraudAttemptDetected": "<PERSON><PERSON> bir potansi<PERSON>l hile girişimi tespit edildi", "statusUpdated": "<PERSON><PERSON>", "alertMarkedAsInvestigating": "Uyarı inceleniyor olarak işaretlendi", "error": "<PERSON><PERSON>", "failedToUpdateStatus": "Uyarı durumu g<PERSON>i", "alertMarkedAsResolved": "Uyarı çözüldü olarak işaretlendi", "failedToResolveAlert": "Uyarı çözülemedi", "success": "Başarılı", "alertDeletedSuccessfully": "Uyarı ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "failedToDeleteAlert": "Uyarı silinemedi"}, "excusesManagement": {"title": "Öğrenci Mazeretleri Yönetimi", "description": "Öğrenci de<PERSON>msızlık mazeret taleplerini inceleyin ve yönetin", "management": "Y<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "submissionSettings": "Başvuru Ayarları", "cleanupSettings": "Temizlik Ayarları", "total": "Toplam", "pending": "Beklemede", "approved": "Onaylandı", "rejected": "Reddedildi", "searchPlaceholder": "Öğrenci adı veya sebep ile ara...", "allTeachers": "<PERSON><PERSON><PERSON>", "allStatuses": "<PERSON><PERSON><PERSON>", "export": "Dışa Aktar", "all": "Tümü", "noExcusesFound": "Ma<PERSON>et bulunamadı", "noExcuseRequestsMatching": "Filtrelerinizle eşleşen mazeret talebi yok", "noPendingExcuses": "<PERSON><PERSON><PERSON> mazeret yok", "noPendingExcuseRequests": "İncelenecek bekleyen mazeret talebi yok", "noApprovedExcuses": "Onaylanmış mazeret yok", "noApprovedExcuseRequests": "Onaylanmış mazeret talebi yok", "noRejectedExcuses": "Reddedilmiş mazeret yok", "noRejectedExcuseRequests": "Reddedilmiş mazeret talebi yok", "excuseRequestDetails": "Mazeret Talebi Detayları", "reviewStudentExcuse": "<PERSON><PERSON><PERSON><PERSON><PERSON> mazeret talebini in<PERSON>", "student": "<PERSON><PERSON><PERSON><PERSON>", "room": "<PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "time": "Saat", "reason": "Sebep", "status": "Durum", "adminNotes": "Yönetici Notları", "error": "<PERSON><PERSON>", "addNotesPlaceholder": "<PERSON>u <PERSON>ret hakkında notlar e<PERSON>in (isteğe bağlı)...", "teacherNotes": "Öğretmen Notları", "by": "tarafından", "reject": "<PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON>", "confirmDeletion": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeleteExcuse": "Bu mazeret talebini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "cancel": "İptal", "delete": "Sil", "excuseDeleted": "<PERSON><PERSON><PERSON>", "excuseDeletedSuccess": "<PERSON><PERSON><PERSON> talebi ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>.", "failedToDelete": "Mazeret silinirken hata oluştu. Lütfen tekrar deneyin.", "exportComplete": "Dışa Aktarma Tamamlandı", "excusesExported": "{count} mazeret CSV olarak dışa aktarıldı.", "unknownStudent": "Bilinmeyen Öğrenci", "unknownRoom": "Bilinmeyen", "filterByTeacher": "Öğ<PERSON><PERSON>e göre filtrele", "filterByStatus": "<PERSON><PERSON><PERSON> g<PERSON>re filtrele", "teacher": "Öğretmen", "notes": "Notlar", "notApplicable": "Uygulanamaz", "unknownTeacher": "Bilinmeyen Öğretmen", "excusesReport": "Ma<PERSON><PERSON> Raporu", "comprehensiveReport": "Kapsamlı Öğrenci Mazeret Yönetimi Raporu", "generatedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "totalExcuses": "Toplam Mazeret", "dateRange": "<PERSON><PERSON><PERSON>", "reviewedBy": "<PERSON><PERSON><PERSON><PERSON>", "notSpecified": "Belirtilmemiş", "exportAsHTML": "HTML olarak dışa aktar", "exportAsPDF": "PDF olarak dışa aktar", "exportAsCSV": "CSV olarak dışa aktar", "exportSuccess": "{{count}} mazeret ba<PERSON>ar<PERSON><PERSON> {{format}} formatında dışa aktarıldı", "reportContains": "<PERSON>u rapor {{date}} ta<PERSON><PERSON> itiba<PERSON> {{count}} mazeret kaydı içermektedir"}, "parentNotifications": {"title": "<PERSON><PERSON>", "description": "Veli iletişim bilgilerini ve bildirim ayarlarını yönetin", "aboutParentNotifications": "Veli Bildirimleri Hakkında", "aboutDescription": "<PERSON><PERSON> özellik, veli iletişim bilgilerini ve bildirim tercihlerini yönetmenizi sağlar. Çocukları mazeret talebi gönderdiğinde veya bu taleplerin durumu değiştiğinde veliler otomatik olarak bilgilendirilecektir. Belirli öğrenciler veya bireysel veli iletişimleri için bildirimleri etkinleştirebilir veya devre dışı bırakabilirsiniz.", "parentContacts": "Veli İletişimleri", "messageTemplates": "<PERSON>j <PERSON>", "emailSmsServices": "E-posta ve SMS Servisleri", "notificationServiceSettings": "Bil<PERSON>im Ser<PERSON>ları", "configureEmailSms": "Veli bildirimleri için e-posta ve SMS servislerini yapılandırın", "emailService": "E-posta Servisi", "smsService": "SMS Servisi", "sendgridApiKey": "SendGrid API Anahtarı", "enterSendgridApiKey": "SendGrid API anahtarınızı girin", "fromEmailAddress": "Gönderen E-posta Adresi", "enterFromEmail": "<EMAIL>", "emailVerificationRequired": "Bu e-posta adresi SendGrid hesabınızda doğrulanmış olmalıdır", "emailNotificationsEnabled": "E-posta bildirimleri etkinleştirildi", "emailNotificationsDisabled": "E-posta bildirimleri devre dışı bırakıldı", "saveEmailConfiguration": "E-posta Yapılandırmasını Kaydet", "testEmailService": "E-posta Servisini Test Et", "enterTestEmailAddress": "Test e-posta adresini girin", "test": "Test", "twilioAccountSid": "<PERSON><PERSON><PERSON>", "enterTwilioAccountSid": "<PERSON><PERSON><PERSON>'nizi girin", "twilioAuthToken": "<PERSON><PERSON><PERSON>", "enterTwilioAuthToken": "<PERSON><PERSON><PERSON> Jetonunuzu girin", "twilioPhoneNumber": "Twilio Telefon Numarası", "enterTwilioPhoneNumber": "<PERSON><PERSON><PERSON> koduyla Twilio telefon numaranızı girin", "smsNotificationsEnabled": "SMS bildirimleri etkinleştirildi", "smsNotificationsDisabled": "SMS bildirimleri devre dışı bırakıldı", "saveSmsConfiguration": "SMS Yapılandırmasını Kaydet", "testSmsService": "SMS Servisini Test Et", "enterTestPhoneNumber": "<PERSON>lke koduyla test telefon numarasını girin", "apiKeysSecurelyStored": "API anahtarları ve jetonlar veritabanında güvenli bir şekilde saklanır", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationHistory": "<PERSON><PERSON><PERSON><PERSON>", "notificationHistoryTitle": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "refreshing": "Yenileniyor...", "searchByStudentOrRecipient": "Öğrenci veya alıcı ile ara...", "status": "Durum", "allStatuses": "<PERSON><PERSON><PERSON>", "successful": "Başarılı", "failed": "Başarısız", "type": "<PERSON><PERSON><PERSON>", "allTypes": "<PERSON><PERSON><PERSON>", "email": "E-posta", "sms": "SMS", "noNotificationLogs": "<PERSON><PERSON><PERSON><PERSON>", "noLogsMatchCriteria": "Arama kriterlerinizle eşleşen kayıt yok. Filtrelerinizi ayarlamayı deneyin.", "noLogsRecorded": "Henüz bildirim kaydı kaydedilmedi.", "dateAndTime": "<PERSON><PERSON><PERSON> ve <PERSON>", "student": "<PERSON><PERSON><PERSON><PERSON>", "recipient": "Alıcı", "sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failedStatus": "Başarısız", "unknownStudent": "Bilinmeyen Öğrenci", "errorFetchingLogs": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>ıtları alınamadı: {message}", "studentName": "Öğrenci <PERSON>ı", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actions": "İşlemler", "searchPlaceholder": "Öğrenci ara", "noStudentsFound": "Öğrenci bulunamadı", "manageContacts": "İletişimleri Yönet", "noStudentSelected": "Öğrenci <PERSON>çilmedi", "selectStudentMessage": "Veli iletişimlerini yönetmek için lütfen Öğrenciler sekmesinden bir öğrenci seçin", "backToStudents": "Öğrencilere Dön", "parentContactsFor": "Veli İletişimleri:", "addParentContact": "<PERSON><PERSON>şimi <PERSON>", "addContactInfo": "Veli veya vasi için iletişim bilgisi ekleyin", "parentGuardianName": "Veli/Vasi Adı", "fullName": "Tam ad", "emailAddress": "E-posta Adresi", "emailPlaceholder": "<EMAIL>", "phoneNumber": "Telefon Numarası", "enterPhoneNumber": "Telefon numarası girin", "notificationMethod": "<PERSON><PERSON><PERSON><PERSON>", "selectNotificationMethod": "<PERSON><PERSON><PERSON><PERSON>", "emailOnly": "Sadece E-<PERSON>a", "smsOnly": "<PERSON><PERSON>e <PERSON>", "bothEmailSms": "E-posta ve SMS", "noNotifications": "<PERSON><PERSON><PERSON><PERSON> yok", "enableNotifications": "Bildirimleri Etkinleştir", "cancel": "İptal", "addContact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editParentContact": "Veli İletişimini <PERSON>", "updateContactInfo": "Bu veli veya vasi için iletişim bilgilerini güncelleyin", "saveChanges": "Değişiklikleri Kaydet", "noParentContacts": "Veli İletişimi Yok", "noContactsAdded": "Bu öğrenci için henüz veli iletişimi eklenmemiş", "addFirstContact": "İlk İletişimi <PERSON>", "parentName": "<PERSON><PERSON>", "contactInfo": "İletişim <PERSON>", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "Devre Dışı", "notificationsEnabled": "Bildir<PERSON><PERSON> E<PERSON>kinleştirildi", "notificationsDisabled": "Bildirimler Devre Dışı Bırakıldı", "notificationsEnabledMessage": "Bu öğrenci için veli bildirimleri etkinleştirildi.", "notificationsDisabledMessage": "Bu öğrenci için veli bildirimleri devre dışı bırakıldı.", "error": "<PERSON><PERSON>", "selectStudentError": "Lütfen önce bir öğrenci se<PERSON>.", "failedToLoadStudents": "Öğrenciler yüklenemedi. Lütfen tekrar deneyin.", "contactAdded": "<PERSON>let<PERSON><PERSON><PERSON>", "contactAddedSuccess": "Veli iletişim bilgileri başarıyla eklendi.", "contactUpdated": "İlet<PERSON><PERSON><PERSON>", "contactUpdatedSuccess": "Veli iletişim bilgileri başarıyla güncellendi.", "contactDeleted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contactDeletedSuccess": "Veli iletişimi başarıyla kaldırıldı.", "failedToDeleteContact": "<PERSON><PERSON>etişim<PERSON> si<PERSON>: {message}", "testNotification": "Test Bildirimi", "sendTestNotification": "Test Bildir<PERSON><PERSON>", "sendTestNotificationDescription": "Veli iletişim bilgilerini doğrulamak için test bildirimi g<PERSON>", "testNotificationSent": "Test Bildirimi Gönderildi", "testNotificationSentDescription": "{{studentName}} ö<PERSON><PERSON><PERSON><PERSON><PERSON> veli(ler)ine test bildirimi başarı<PERSON> gö<PERSON>ildi", "notificationFailed": "Bildirim <PERSON>arısız", "failedToSendTestNotification": "Test bildirimi g<PERSON>: {{error}}", "testNotificationSubject": "{{name}} Öğrencisinin Velisi İçin Test Bildirimi", "newAbsenceRequestSubject": "{{name}} Öğrencisinden Yeni Devamsızlık Talebi", "absenceRequestApprovedSubject": "{{name}} Öğrencisinin Devamsızlık Talebi Onaylandı", "absenceRequestRejectedSubject": "{{name}} Öğrencisinin Devamsızlık Talebi Reddedildi", "notificationSubject": "{{name}} Öğrencisinin Velisi İçin Bildirim", "testNotificationMessage": "<PERSON>u, {{name}} öğrencisinin velisi için bir test bildirimidir. Bu mesajı aldıysanız, bildirim sistemi düzgün çalışıyor demektir.", "newAbsenceRequestMessage": "Çocuğunuz {{name}}, okuldan devamsızlık talebi göndermiştir. Bu talep okul yönetiminin onayını beklemektedir.", "absenceRequestApprovedMessage": "Çocuğunuzun ({{name}}) devamsızlık talebi okul yönetimi tarafından ONAYLANMIŞTIR.", "absenceRequestRejectedMessage": "Çocuğunuzun ({{name}}) devamsızlık talebi okul yönetimi tarafından REDDEDİLMİŞTİR. Daha fazla bilgi için lütfen okulla iletişime geçin.", "notificationMessage": "<PERSON><PERSON>, çocuğunuz {{name}} ile ilgili bir bildirimdir.", "noParentContactsDescription": "Bu öğrenci için ya<PERSON>ılandırılmış veli iletişimi bulunmamaktadır. Test bildirimleri göndermeden önce lütfen en az bir veli iletişimi ekleyin.", "notificationType": "<PERSON><PERSON><PERSON><PERSON>", "selectNotificationType": "<PERSON><PERSON><PERSON><PERSON> tü<PERSON>", "testMessage": "Test Mesajı", "newExcuseRequest": "<PERSON><PERSON>", "excuseApproved": "Mazeret On<PERSON>landı", "excuseRejected": "Mazeret Reddedildi", "customMessageOptional": "<PERSON><PERSON> (İsteğe Bağlı)", "customMessagePlaceholder": "Özel bir mesaj girin veya varsayılan şablonu kullanmak için boş bırakın", "leaveBlankForDefault": "Seçilen bildirim türü için var<PERSON>ılan şablonu kullanmak için boş bırakın", "notificationRecipients": "<PERSON><PERSON><PERSON><PERSON>", "testWillBeSentTo": "Bu test aşağıdaki kişilere gönderilecektir:", "viaEmail": "E-posta ({{email}})", "viaSMS": "SMS ({{phone}})", "viaEmailAndSMS": "E-posta ve SMS ({{email}}, {{phone}})", "sending": "Gönderiliyor...", "sampleExcuseReason": "<PERSON><PERSON><PERSON> ne<PERSON>i"}, "automaticQR": {"title": "Otomatik QR Üretimi", "serviceStatus": "<PERSON><PERSON>", "initializing": "Başlatılıyor...", "error": "<PERSON><PERSON>", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startService": "<PERSON><PERSON><PERSON>", "generateNow": "Şimdi <PERSON>ret", "refreshStatus": "<PERSON><PERSON><PERSON>", "stopService": "<PERSON><PERSON><PERSON>", "activeSessions": "<PERSON><PERSON><PERSON>", "totalRooms": "Toplam Sınıf", "currentSchool": "<PERSON><PERSON><PERSON>", "yourSchoolSession": "Okulu<PERSON><PERSON><PERSON>", "status": "Durum", "rooms": "Sınıflar", "startTime": "Başlangıç <PERSON>", "endTime": "Bitiş Saati", "school": "<PERSON><PERSON>", "roomsCount": "{{count}} sınıf", "howItWorks": "Nasıl <PERSON>alışır", "currentConfiguration": "Mevcut Yapılandırma", "qrExpiryTime": "QR Sona Erme <PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "generationInterval": "<PERSON><PERSON><PERSON>", "seconds": "saniye", "transitionTiming": "Geçiş Zamanlaması", "oneSecondBeforeExpiry": "Sona ermeden 1 saniye önce", "source": "<PERSON><PERSON><PERSON>", "envConfiguration": ".env yapılandırması", "automaticStart": "Otomatik Başlatma", "automaticStartDescription": "QR üretimi yoklama zamanı başladığında otomatik olarak başlar", "smartTiming": "Akıllı Zamanlama", "smartTimingDescription": "Her {{interval}} saniyede yeni QR üretilir, 1 saniye kala geçiş yapar", "configurableExpiry": "Yapılandırılabilir Sona Erme", "configurableExpiryDescription": "QR kodları {{minutes}} dakika sonra sona erer (.env dos<PERSON><PERSON><PERSON> a<PERSON>lanı<PERSON>)", "allRooms": "<PERSON><PERSON><PERSON>", "allRoomsDescription": "QR kodları okulunuzdaki tabletli tüm odalar için üretilir", "automaticStop": "Otomatik Durdurma", "automaticStopDescription": "Yoklama zamanı sona erdiğinde üretim otomatik olarak durur", "serviceStarted": "Servis Başlatıldı", "serviceStartedDescription": "Otomatik QR üretim servisi başarıyla başlatıldı", "serviceStopped": "<PERSON><PERSON>", "serviceStoppedDescription": "Otomatik QR üretim servisi başarıyla durduruldu", "generationTriggered": "<PERSON><PERSON><PERSON>", "generationTriggeredDescription": "Okulunuz için QR kodları manuel olarak üretildi", "manualTriggerFailed": "<PERSON> teti<PERSON>me başar<PERSON>s<PERSON><PERSON>", "errorStoppingService": "<PERSON><PERSON> du<PERSON> hatası", "noSchoolId": "Mevcut kullanıcı için okul ID'si bulunamadı"}, "tabletSetup": {"title": "Tablet Kurulum Yardımcısı", "generateURLsForBlock": "Tüm blok için URL'ler oluştur", "generateURLForRoom": "Tek oda için URL oluştur", "selectBlock": "Bir blok seçin", "selectRoom": "<PERSON><PERSON> oda seçin", "block": "Blok", "floor": "<PERSON>", "generate": "Oluştur", "setupInstructions": "<PERSON><PERSON><PERSON>", "instruction1": "Tablet yerleştirmek istediğiniz odalar için URL'ler oluşturun", "instruction2": "Her URL'yi ilgili o<PERSON>ın tabletinde açın", "instruction3": "Tablet kendini o oda için otomatik olarak yapılandıracak", "instruction4": "QR kodları yönetici panelinden oluşturulduğunda otomatik olarak görünecek", "generatedTabletURLs": "Oluşturulan Tablet URL'leri", "room": "<PERSON><PERSON>", "tabletURL": "Tablet URL'si", "copyURL": "URL'y<PERSON>", "testTablet": "Tableti Test Et", "bulkActions": "Toplu İşlemler", "copyAllURLs": "Tüm URL'le<PERSON>", "openAllTablets": "Tüm Tabletleri Aç", "setupQRCode": "Kurulum QR Kodu", "scanQRCodeInstruction": "URL'yi otomatik olarak açmak için bu QR kodunu tabletle tarayın", "copied": "Kopyalandı!", "urlCopied": "URL panoya kopyalandı", "failedToCopy": "URL kopyalanamadı"}, "schoolContext": {"yourSchoolInformation": "Okul bilgileriniz", "address": "<PERSON><PERSON>", "phone": "Telefon", "email": "E-posta", "active": "Aktif", "totalUsers": "Toplam Kullanıcılar", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teachers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "administrators": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "auditLogs": {"title": "Denetim <PERSON>ıtları", "description": "Denetim kayıtlarını görü<PERSON>üle", "searchPlaceholder": "Kayıtları ara...", "filterByAction": "İşleme göre filtrele", "filterByEntity": "Varlığa göre filtrele", "allActions": "<PERSON><PERSON><PERSON>", "allEntities": "<PERSON><PERSON>m Varlıklar", "noAuditLogs": "Denetim Kaydı Yok", "noAuditLogsRecorded": "Henüz denetim kaydı kaydedilmedi.", "noLogsMatchCriteria": "Arama kriterlerinize uygun kayıt bulunamadı. Filtrelerinizi ayarlamayı deneyin.", "dateTime": "<PERSON><PERSON><PERSON> ve <PERSON>", "action": "İşlem", "entity": "Varlık", "entityType": "Varlık Türü", "entityId": "Varlık ID", "user": "Kullanıcı", "school": "<PERSON><PERSON>", "ipAddress": "IP Adresi", "details": "Detaylar", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showing": "{{total}} kaydın {{shown}} tanesi g<PERSON>ili<PERSON>r", "pagination": "Sayfa {{current}} / {{total}}", "auditLogDetails": "Denetim Kaydı Detayları", "detailedInformation": "Bu denetim kaydı girişi hakkında detaylı bilgi", "userAgent": "Kullanıcı Aracısı", "notAvailable": "<PERSON><PERSON><PERSON>", "system": "Sistem", "noDetailsAvailable": "Detay bilgisi mevcut değil", "actions": {"user_login": "Kullanıcı Girişi", "user_logout": "Kullanıcı Çıkışı", "user_created": "Kullanıcı Oluşturuldu", "user_updated": "Kullanıcı Güncellendi", "user_deleted": "Kullanıcı Silindi", "school_created": "Okul Oluşturuldu", "school_updated": "<PERSON><PERSON> Güncellendi", "school_deleted": "<PERSON><PERSON>", "school_settings_updated": "Okul Ayarları Güncellendi", "invitation_code_generated": "<PERSON><PERSON>", "invitation_code_used": "Davet Kodu Kullanıldı", "security_alert": "Güvenlik Uyarısı", "permission_changed": "<PERSON>zin Değiştirildi", "data_exported": "Veri Dışa Aktarıldı", "data_imported": "Veri İçe Aktarıldı", "attendance_recorded": "<PERSON><PERSON><PERSON>", "attendance_updated": "<PERSON><PERSON><PERSON>", "attendance_deleted": "<PERSON><PERSON><PERSON>", "delete": "Sil", "create": "Oluştur", "update": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON>"}, "entities": {"user": "Kullanıcı", "school": "<PERSON><PERSON>", "student": "<PERSON><PERSON><PERSON><PERSON>", "teacher": "Öğretmen", "admin": "Yönetici", "attendance": "<PERSON><PERSON><PERSON>", "room": "Sınıf", "block": "Blok", "excuse": "Mazeret", "notification": "<PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "system_school_settings_override": "Sistem Okul Ayarları Geçersiz Kılma", "invitation_code": "<PERSON><PERSON>", "attendance_record": "Yo<PERSON><PERSON>"}}}, "pwa": {"installTitle": "Uygulamayı Yükle", "installDescription": "Çevrimdışı erişim ve push bildirimleri ile en iyi deneyim için uygulamamızı yükleyin.", "installCompactDescription": "<PERSON>ha hızlı erişim ve çevrimdışı özellikler", "installButton": "Uygulamayı Yükle", "install": "<PERSON><PERSON><PERSON>", "installing": "Yükleniyor...", "installSuccess": "Uygulama Başarıyla Yüklendi!", "installSuccessDescription": "Artık uygulamaya ana ekranınızdan erişebilirsiniz.", "installFailed": "Yükleme başarısız. Lütfen tekrar deneyin.", "manualInstall": "<PERSON>", "updateTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateDescription": "Uygulamanın iyileştirmeler ve hata düzeltmeleri içeren yeni bir sürümü mevcut.", "updateCompactDescription": "Yeni özellikler ve iyileştirmeler hazır", "updateButton": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "updateLater": "Sonra", "later": "Sonra", "updating": "Güncelleniyor...", "updateApplied": "Güncelleme başarıyla uygulandı!", "updateFailed": "Güncelleme başarısız. Lütfen tekrar deneyin.", "remindLater": "<PERSON>ha sonra hatırlatacağız", "remindLaterDescription": "<PERSON><PERSON><PERSON><PERSON> istemi yarın tekrar görü<PERSON>k", "offline": "Çevrimdışı", "online": "Çevrimiçi", "benefit1": "Çevrimdışı çalışır - verilerinize her zaman erişin", "benefit2": "<PERSON>ha hızlı yükleme ve daha iyi performans", "benefit3": "Önemli güncellemeler için push bildirimleri"}, "aiAssistant": {"title": "ATS Asistanı", "subtitle": "Çevrimiçi • Yardıma hazır", "typing": "Yazıyor...", "thinking": "AI düşünüyor", "analyzing": "İsteğiniz analiz ediliyor...", "listening": "Dinliyor...", "voiceInput": "<PERSON><PERSON><PERSON>", "voiceInputActive": "Dinliyor...", "speakMessage": "<PERSON><PERSON><PERSON>", "stopSpeaking": "Konuşmayı durdur", "defaultUser": "Kullanıcı", "greeting": "<PERSON><PERSON><PERSON><PERSON>, {{userName}}! 👋 <PERSON> akıllı ATS Asistanınızım. Size ihtiyacınız olan her konuda yardımcı olmak için buradayım. Sistem hakkında bana istediğiniz soruyu sorabilirsiniz!", "quickStart": "Bana şunları sorabilirsiniz:", "introduction": {"greeting": "<PERSON><PERSON><PERSON><PERSON> {{userName}}! 👋 <PERSON> akıllı ATS Asistanınızım.", "roleSpecific": {"admin": "Sistem yönetimi ve kullanıcı kontrolünde yardımcı olabilirim! 😊", "teacher": "<PERSON><PERSON><PERSON><PERSON><PERSON> yönetimi ve devam takibinde yardımcı olabilirim! 😊", "student": "Devam kontrolü ve QR taramada yardımcı olabilirim! 😊"}, "pageContext": {"dashboard": "Kontrol panelinde gezinmenizde yardımcı olabilirim! 📊", "attendance": "Devam kayıtları hakkında sorularınızı yanıtlayabilirim! 📋", "qr": "QR tarama sürecinde size yardımcı olabilirim! 📱", "biometric": "Biyometrik ayarlar konusunda yardımcı olabilirim! 🔐", "profile": "Profil ayarları hakkında bilgi verebilirim! 👤", "admin": "Sistem yönetimi konusunda yardımcı olabilirim! ⚙️", "general": "Devam sistemi hakkında sorularınızı yanıtlayabilirim! 💡"}, "invitation": "Size nasıl yardımcı olabilirim? 😊✨"}, "quickActions": {"help": "💡 Yardım Al", "helpMessage": "<PERSON><PERSON> sistemi ile ilgili yardıma ihtiyacım var", "overview": "📊 Sistem Genel Bakış", "overviewMessage": "<PERSON>a sistemin genel bakışını göster", "attendance": "✅ <PERSON><PERSON>ımı", "attendanceMessage": "<PERSON><PERSON> takibi konusunda yardım et", "qr": "📱 QR Tarayıcı Rehberi", "qrMessage": "QR tarayıcıyı nasıl kullanırım?", "scanQR": "📱 QR Kod Tara", "checkAttendance": "📊 Devamı Kontrol Et", "biometricHelp": "🔐 <PERSON><PERSON><PERSON><PERSON><PERSON>", "appTour": "🎯 <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "floatingButtonText": "AI Asistanı - <PERSON>a herhang<PERSON> bir şey sorun!", "floatingButtonThinking": "AI Asistanı düşünüyor...", "welcome": "ATS Asistanına Hoş Geldiniz!", "welcomeDescription": "<PERSON><PERSON> sistemi ve okulunuzla ilgili her konuda size yardımcı olmak için buradayım.", "askAnything": "Bana herhangi bir <PERSON>ey sorun...", "tryAsking": "Bana şunları sorabilirsiniz:", "responses": {"qrScan": "QR kod tarama konusunda size yardımcı olacağım! 📱", "attendance": "Devam bilgileri konusunda size yardımcı olabilirim! 📊", "biometric": "Biyometrik kimlik doğrulama konusunda size rehberlik edeceğim! 🔐", "help": "Size yardımcı olmak için buradayım! 🤖 Şu konularda yardımcı olabilirim:", "profile": "Profilinizi yönetmenizde size yardımcı olabilirim! 👤", "notifications": "Bildirimler konusunda size yardımcı olabilirim! 🔔"}, "actions": {"openScanner": "📱 Tarayıcıyı Aç", "cameraHelp": "📷 <PERSON><PERSON><PERSON>", "viewAttendance": "📊 Devam<PERSON>ö<PERSON>", "attendanceStats": "📈 <PERSON><PERSON><PERSON><PERSON><PERSON>", "setupBiometric": "🔐 <PERSON><PERSON><PERSON><PERSON><PERSON>", "troubleshoot": "❓ <PERSON><PERSON>", "appTour": "🎯 <PERSON><PERSON><PERSON><PERSON><PERSON>", "commonIssues": "🔧 <PERSON><PERSON><PERSON><PERSON><PERSON>", "editProfile": "✏️ <PERSON><PERSON>", "privacySettings": "🔒 Gizlilik Ayarları", "notificationSettings": "⚙️ <PERSON><PERSON><PERSON><PERSON>", "testNotification": "🧪 Test Bildirimi", "qrScanningGuide": "📱 QR <PERSON><PERSON>", "appFeatures": "✨ <PERSON><PERSON><PERSON><PERSON><PERSON>", "askAnotherQuestion": "❓ Başka Soru Sor", "exploreFeatures": "✨ Özellikleri Keşfet", "qrScanning": "📱 QR Tarama", "biometricAuth": "🔐 <PERSON><PERSON><PERSON><PERSON><PERSON>", "reportsAnalytics": "📊 <PERSON><PERSON><PERSON> ve <PERSON>lit<PERSON>", "attendance": "📊 Devam", "technicalIssues": "🔧 Tek<PERSON>", "qrSteps": "📋 <PERSON><PERSON><PERSON>", "qrProblems": "🔧 QR Sorunları", "cameraAccess": "📷 <PERSON><PERSON><PERSON>", "viewMyAttendance": "📈 Devamımı Görüntüle", "markAttendance": "✅ <PERSON><PERSON>", "attendanceProblems": "❓ <PERSON><PERSON>", "setupBiometrics": "⚙️ <PERSON><PERSON><PERSON><PERSON><PERSON>", "fixBiometricIssues": "🔧 <PERSON><PERSON><PERSON><PERSON><PERSON>ı Çöz", "securityInfo": "🛡️ Güvenlik Bilgisi", "enableLocation": "📍 <PERSON><PERSON><PERSON>", "locationProblems": "🔧 Konum Sorunları", "privacyInfo": "🔒 Gizlilik Bilgisi", "setupNotifications": "🔔 Bildirimleri Kur", "notReceivingNotifications": "🔧 <PERSON><PERSON><PERSON><PERSON>", "attendanceRecords": "📊 <PERSON><PERSON>", "biometricSetup": "🔐 <PERSON><PERSON><PERSON><PERSON><PERSON>", "troubleshooting": "🔧 <PERSON><PERSON>", "cameraIssues": "📷 <PERSON><PERSON><PERSON>", "biometricIssues": "🔐 <PERSON><PERSON><PERSON><PERSON><PERSON>", "locationIssues": "📍 Konum Sorunları", "describeIssue": "💬 <PERSON><PERSON>u Açıkla"}, "actionMessages": {"qrScanningHelp": "QR kodlarını nasıl tararım?", "checkAttendance": "Devamımı nasıl kontrol ederim?", "showAppFeatures": "Bu uygulamanın hangi özellikleri var?", "qrScanningInstructions": "<PERSON><PERSON>m adım QR tarama talimatlarını göster", "anotherQuestion": "Başka bir sorum var", "exploreMoreFeatures": "Bu uygulama başka neler yapa<PERSON>ir?", "helpWithQrScanning": "QR kod tarama konusunda yardım", "helpWithAttendance": "<PERSON><PERSON> kayıtları konusunda yardım", "technicalProblem": "Teknik bir sorunum var", "showFeatures": "Uygulama özelliklerini göster", "detailedQrSteps": "Detaylı QR tarama adımlarını göster", "qrNotWorking": "QR tarama çalışmıyor", "cameraAccess": "<PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON>ine nasıl izin veririm?", "viewMyAttendance": "<PERSON><PERSON> kayıtlarımı nasıl kontrol ederim?", "markAttendance": "Devamımı nasıl işaretlerim?", "attendanceIssues": "<PERSON><PERSON> ka<PERSON>ı<PERSON>ımla ilgili sorun<PERSON>ım var", "setupBiometric": "Biyometrik kimlik doğrulamayı nasıl kurarım?", "biometricNotWorking": "Biyometrik kimlik doğrulama çalışmıyor", "biometricSecurity": "Biyometrik kimlik doğrulama güvenli mi?", "enableLocation": "Konum servislerini nasıl etkinleştiririm?", "locationNotWorking": "Konum doğrulama çalışmıyor", "locationPrivacy": "Konum verilerim nasıl kullanılıyor?", "setupNotifications": "Bildirimler nasıl etkinleştirilir?", "notificationSettings": "Bildirim tercihlerini nasıl özelleştiririm?", "notReceivingNotifications": "<PERSON><PERSON><PERSON><PERSON>", "qrCodeFeatures": "QR kod özellikleri hakkında bilgi ver", "biometricAuthFeatures": "Biyometrik kimlik doğrulamayı açıkla", "reportingFeatures": "Hangi raporlama özellikleri mevcut?", "appInstallation": "Bu uygulamayı yükleyebilir miyim?", "cameraIssues": "QR tarama için kamera çalışmıyor", "biometricIssues": "Biyometrik kimlik doğrulama başarısız oluyor", "locationIssues": "Konum doğrulama çalışmıyor", "describeIssue": "Spesifik sorunumu açıklayayım"}, "fallbackResponses": {"qrRelated": "QR kod tarama konusunda size yardımcı olacağım! 📱\n\nQR kod tarama hakkında özel olarak bilmek istediğiniz bir şey var mı?", "attendanceRelated": "Devam takibi konusunda size yardımcı olacağım! 📊\n\nDevamın hangi yönü hakkında bilgi almak istiyorsunuz?", "biometricRelated": "Biyometrik kimlik doğrulama konusunda size yardımcı olacağım! 🔐\n\nBiyometrik kimlik doğrulama ile ilgili neye yardıma ihtiyacınız var?", "locationRelated": "Konum tabanlı devam konusunda size yardımcı olacağım! 📍\n\nKonum doğrulama hakkında neyi bilmek istiyorsunuz?", "notificationRelated": "Bildirimler konusunda size yardımcı olacağım! 🔔\n\nBildirimler hakkında neyi öğrenmek istiyorsunuz?", "featureInquiry": "Uygulama özelliklerini açıklamaktan mutluluk duyarım! ✨\n\nHangi özellik hakkında daha fazla bilgi edinmek istiyorsunuz?", "helpRequest": "Size yardımcı olmak için buradayım! 🤖\n\nNeye yardıma ihtiyacınız var?", "problemReport": "Sorununuzu çözmenize yardımcı olacağım! 🔧\n\nKarşılaştığınız spesifik sorun hakkında daha fazla ayrıntı verebilir misiniz? Bu, size en doğru çözümü vermeme yardımcı olacaktır.", "gratitudeResponses": ["Rica ederim! 😊 <PERSON><PERSON> sistemi ile ilgili her zaman size yardımcı olmak için buradayım!", "Yardımcı olabildiğim için mutluyum! 🌟 Uygulama hakkında başka sorularınız varsa çekinmeyin!", "Memnuniyetle! 🤝 Bugün size yardımcı olabileceğim başka bir şey var mı?"], "generalInquiry": "Size yardımcı olmayı çok isterim! 🤔 Neye yardıma ihtiyacınız olduğu konusunda daha spesifik olabilir misiniz?\n\nSize yardımcı olabileceğim konular:\n• QR kod tarama ve devam işaretleme\n• Biyometrik kimlik doğrulama kurulumu\n• Devam kayıtlarını ve istatistiklerini kontrol etme\n• Uygulama özellikleri ve işlevselliği\n• Teknik sorunları giderme\n• Profil ve ayarlar yönetimi\n\nHangi konuda daha fazla bilgi almak istiyorsunuz?"}, "greetings": {"welcomeBack": "Hoş geldiniz! 👋", "welcomeBackDescription": "Size yardımcı olmak için buradayım! 😊", "firstTimeWelcome": "Hoş geldiniz! 🎉", "firstTimeDescription": "Devam sisteminde size yardımcı olacağım!", "newUserWelcome": "Merhaba! ✨", "newUserDescription": "<PERSON>am takibi konusunda size yardımcı olabilirim!", "student": {"welcomeBack": "Hoş geldin! 📚", "welcomeBackDescription": "Devam kontrolü ve QR taramada yardımcı olabilirim! 😊", "firstTime": "Merhaba! 🎓", "firstTimeDescription": "Devam sistemi ve QR tarama konusunda size yardımcı olacağım!", "newUser": "Hoş geldin! 🌟", "newUserDescription": "<PERSON>am sistemi ve QR tarama hakkında bilgi verebilirim!"}, "teacher": {"welcomeBack": "Hoş geldiniz! 👨‍🏫", "welcomeBackDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> yönetimi ve devam takibinde yardımcı olabilirim! 😊", "firstTime": "Merhaba! 📋", "firstTimeDescription": "Devam sistemi ve öğrenci takibinde size yardımcı olacağım!", "newUser": "Hoş geldiniz! 🍎", "newUserDescription": "Devam sistemi ve öğretmen araçları hakkında bilgi verebilirim!"}, "admin": {"welcomeBack": "Hoş geldiniz! 🛡️", "welcomeBackDescription": "Sistem yönetimi ve kullanıcı kontrolünde yardımcı olabilirim! 😊", "firstTime": "Merhaba! ⚙️", "firstTimeDescription": "Sistem yönetimi ve kullanıcı kontrolünde size yardımcı olacağım!", "newUser": "Hoş geldiniz! 👑", "newUserDescription": "Sistem yönetimi ve admin araçları hakkında bilgi verebilirim!"}, "pageReload": {"dashboard": {"title": "Kontrol paneline geri <PERSON>, {{userName}}! 📊", "description": "<PERSON><PERSON><PERSON>, son aktivite<PERSON><PERSON> kontrol etmene veya sorularını yanıtlamana yardımcı olmak i<PERSON><PERSON> b<PERSON>."}, "attendance": {"title": "<PERSON><PERSON> takibi yapmaya hazır mı<PERSON>ın, {{userName}}? 📝", "description": "<PERSON><PERSON> ka<PERSON>ı<PERSON>ı<PERSON><PERSON><PERSON><PERSON><PERSON>, verileri anlama veya devamla ilgili görevlerde yardımcı olabilirim."}, "qrScanner": {"title": "QR kod tarama zamanı, {{userName}}! 📱", "description": "QR tarama konusunda yardıma ihtiyacın var mı? Süreç boyunca rehberlik edebilir veya sorunları çözebilirim."}, "dormitory": {"title": "<PERSON>rt giri<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>lar<PERSON><PERSON><PERSON> yönetiyor musun, {{userName}}? 🏠", "description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prosedü<PERSON>ri veya yurtla ilgili sorular konusunda yardımcı olmak için buradayım."}, "socialMedia": {"title": "Sosyal akışı kontrol ediyor musun, {{userName}}? 📱", "description": "Sosyal özellikleri anlamana, gönderileri yönetmene veya topluluk etkileşimlerinde yardımcı olabilirim."}, "biometric": {"title": "Biyometrik veriler<PERSON> musun, {{userName}}? 👆", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, silme istekleri veya biyometrik özelliklerin sorun giderme konusunda yardıma ihtiyacın var mı?"}, "profile": {"title": "<PERSON><PERSON><PERSON> musun, {{userName}}? 👤", "description": "Profil a<PERSON>larını yö<PERSON>e, bilgileri güncelleme veya profil özelliklerini açıklamana yardımcı olabilirim."}, "settings": {"title": "Ayarlarını düzen<PERSON><PERSON>r musun, {{userName}}? ⚙️", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>, a<PERSON><PERSON><PERSON> anlama veya hesabında değişiklik yapma konusunda yardıma ihtiyacın var mı?"}, "admin": {"title": "<PERSON><PERSON><PERSON> musun, {{userName}}? 🛡️", "description": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, siste<PERSON> ya<PERSON>landırması veya politika ayarları konusunda yardımcı olmak için buradayım."}, "teacher": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON><PERSON><PERSON>r musun, {{userName}}? 👨‍🏫", "description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, QR kod oluşturma veya öğretim araçları konusunda yardımcı olabilirim."}, "student": {"title": "<PERSON><PERSON><PERSON> hazır mı<PERSON>n, {{userName}}? 🎓", "description": "<PERSON><PERSON>, <PERSON><PERSON>rama, yurt özellikleri veya öğrenciyle ilgili sorular konusunda yardımcı olmak için buradayım."}, "general": {"title": "<PERSON><PERSON><PERSON>, {{userName}}! 👋", "description": "İhtiyacın olan her konuda yardımcı olmak için bura<PERSON>ım. Sistem hakkında bana istediğin soruyu sorabilirsin!"}}}, "pageContext": {"dashboard": "Kontrol panelinde olduğunuzu görüyorum - genel bakış için mükemmel bir yer!", "attendance": "Devam kayıtlarına mı bakıyorsunuz? Gördüklerinizi açıklamaya yardımcı olabilirim.", "qr": "QR kod taramaya hazır mısınız? Süreç boyunca size rehberlik etmek için buradayım!", "profile": "Profilinizi mi güncelliyorsunuz? Ayarlar hakkındaki sorularınızda yardımcı olabilirim.", "settings": "<PERSON>stemi mi yapılandırıyorsunuz? Herhangi bir yönetim görevinde yardımcı olmak için buradayım."}, "capabilities": ["QR kod tarama", "<PERSON><PERSON> ta<PERSON>bi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Okul bilgileri"], "errors": {"sendFailed": "Mesaj gönderilemedi. Lütfen tekrar deneyin.", "generalError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir hat<PERSON>la ka<PERSON>ılaştım. Lütfen tekrar deneyin veya sorun devam ederse destek ile iletişime geçin."}}, "social": {"updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schoolUpdates": "<PERSON><PERSON> Güncellemeleri", "stayConnected": "En son haberler ve duyurularla bağlantıda kalın", "comingSoon": "Sosyal Medya Akışı Yakında Geliyor!", "notConfigured": "Okulunuzun sosyal medya entegrasyonu henüz yapılandırılmamış. Bu özelliği etkinleştirmek ve okul güncellemeleriyle bağlantıda kalmak için yöneticinizle iletişime geçin!", "noPostsYet": "Henüz gönderi yok", "checkBackLater": "Okulunuzdan en son gü<PERSON><PERSON><PERSON><PERSON> i<PERSON>in daha sonra tekrar kontrol edin!", "refresh": "<PERSON><PERSON><PERSON>", "viewPost": "Gönderiyi <PERSON>ö<PERSON>", "justNow": "<PERSON>z önce", "hoursAgo": "{{count}} saat önce", "daysAgo": "{{count}} gün <PERSON><PERSON>", "all": "Tümü", "liveFromPage": "Resmi {{platform}} sayfamızdan canlı", "autoRefreshing": "Otomatik yenileniyor", "auto": "Otomatik", "platforms": {"instagram": "Instagram", "twitter": "Twitter", "facebook": "Facebook", "youtube": "YouTube", "website": "Web Sitesi"}}, "landing": {"getStarted": "Başlayın", "keyFeatures": "<PERSON><PERSON>", "hero": {"badge": "🚀 <PERSON><PERSON>", "title1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title2": "Yönetimini Dönüştürün", "subtitle": "Modern eğitim kurumları için biyometrik kimlik doğrulama, ger<PERSON>ek zamanlı analitik ve kapsamlı sahtekarlık önleme özelliklerine sahip güvenli, akıllı ve kullanıcı dostu devam takip sistemi.", "features": {"qr": "QR <PERSON><PERSON>", "biometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "GPS Doğrulama", "multilingual": "Ç<PERSON>"}, "learnMore": "<PERSON><PERSON>"}, "stats": {"accuracy": "Doğruluk Oranı", "speed": "<PERSON><PERSON><PERSON>", "availability": "Sistem Çalışma Süresi", "schools": "Ayrı Özellikler Bir Arada"}, "features": {"badge": "🎯 <PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Modern Devam Yönetimi <PERSON>ız Olan Her Şey", "subtitle": "Ka<PERSON><PERSON>lı platformumuz, mevcut en gelişmiş devam takip çözümünü sunmak için son teknoloji ile sezgisel tasarımı birleştiriyor.", "qr": {"title": "QR Kod <PERSON>", "description": "Güvenli QR kodları ile yıldırım hızında devam işaretleme. Öğrenciler sadece tarayarak giriş yapar, otomatik zaman tabanlı kod üretimi ve doğrulama."}, "biometric": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Gelişmiş parmak izi ve yüz tanıma teknolojisi kusursuz kimlik doğrulaması sağlar, vekil devam ve sahtekarlığı ortadan kaldırır."}, "location": {"title": "GPS Konum Doğrulama", "description": "Hassas konum takibi öğrencilerin kampüste fiziksel olarak bulunmasını sağlar. Özelleştirilebilir yarıçap ayarları ile yapılandırılabilir coğrafi sınırlama."}, "analytics": {"title": "Gerçek Zamanlı Analitik ve Raporlar", "description": "<PERSON><PERSON><PERSON> de<PERSON><PERSON> ve<PERSON>, de<PERSON><PERSON><PERSON>, trend analizi ve yöneticiler ile öğretmenler için otomatik uyarılar içeren kapsamlı paneller."}, "multilingual": {"title": "Çok Dilli Destek", "description": "İngilizce ve Türkçe desteği ile tam uluslararasılaştırma. Otomatik dil algılama ve küresel erişilebilirlik için sorunsuz geçiş."}, "excuses": {"title": "Dijital Mazeret Y<PERSON>imi", "description": "Akıcı mazeret gönderimi ve onay iş akışı. Öğrenciler fotoğraf kanıtı ile dijital mazeret gönderir, öğretmenler anında inceler ve onaylar."}, "advanced": {"title": "Gelişmiş Güvenlik ve Özelleştirme", "security": "Kurumsal Güvenlik", "securityDesc": "Banka seviyesi şifreleme ve güvenlik", "notifications": "Akıllı Bildirimler", "notificationsDesc": "Gerçek zamanlı uyarılar ve güncellemeler", "branding": "<PERSON><PERSON>", "brandingDesc": "Kişiselleştirilmiş okul temaları", "cloud": "Bulut Altyapısı", "cloudDesc": "%99.9 çalışma süresi garantisi"}}, "howItWorks": {"badge": "📋 <PERSON><PERSON><PERSON>", "title": "4 Kolay Adımda Nasıl Çalışır", "subtitle": "Akıc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ilk kurulumdan günlük operasyonlara kadar, da<PERSON> olan herkes için devam takibini zahmetsiz hale getirir.", "step1": {"title": "<PERSON><PERSON>", "description": "Okul ayarlarınızı yapılandırın, kullanıcıları ekleyin ve doğrulama yöntemlerini özelleştirin. Tek seferlik kurulum sadece dakikalar sürer."}, "step2": {"title": "Tabletleri Yerleştirin", "description": "Güvenli kiosk modumuzla sınıflara tabletler kurun. QR kodları devam saatleri boyunca otomatik olarak oluşturulur."}, "step3": {"title": "Öğrenciler G<PERSON>", "description": "Öğrenciler QR kodları tarar ve biyometrik veya PIN ile kimliklerini doğrular. Konum otomatik olarak doğrulanır."}, "step4": {"title": "Gerçek Zamanlı Analitik", "description": "Öğretmenler ve yöneticiler anında devam verilerini gö<PERSON>ür, uyarılar alır ve kapsamlı raporlara erişir."}, "process": {"title": "Sorunsuz Veri Akışı", "student": "<PERSON><PERSON><PERSON><PERSON>", "studentDesc": "Tara ve kimliği doğrula", "system": "Sistem İşleme", "systemDesc": "Doğrula ve veriyi kaydet", "teachers": "Öğretmen Paneli", "teachersDesc": "Gerçek zamanlı güncellemeleri görüntüle"}}, "userTypes": {"badge": "👥 Kullanıcı Odaklı Tasarım", "title": "Her Kullanıcı Türü İçin Tasarlandı", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, y<PERSON><PERSON><PERSON>ler ve sistem yöneticileri için özel deneyimler ve güçlü araçlar.", "students": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON>, hızlı ve güvenli devam işaretleme", "feature1": "Anında geri bildirimli yıldırım hızında QR kod tarama", "feature2": "Gelişmiş güvenlik için biyometrik kimlik doğrulama", "feature3": "<PERSON><PERSON><PERSON><PERSON> devam geçmişi ve istatistikleri", "feature4": "Fotoğraf kanıtı ile dijital mazeret gönderimi", "feature5": "Çok dilli arayüz desteği"}, "teachers": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Kapsamlı sınıf yönetimi araçları", "feature1": "Canlı güncellemelerle gerçek zamanlı devam paneli", "feature2": "Anında devamsızlık uyarıları ve bildirimleri", "feature3": "Detaylı öğrenci devam desenleri ve analitiği", "feature4": "Mazeret inceleme ve onay iş akışı", "feature5": "Sahtekarlık tespit uyarıları ve konum doğrulama"}, "administrators": {"title": "Okul Yöneticileri", "subtitle": "Tam okul çapında gözetim ve kontrol", "feature1": "Sistem geneli devam analitiği ve raporlama", "feature2": "Kullanıcı yönetimi ve rol atama", "feature3": "Özel marka ve okul yapılandırması", "feature4": "Gelişmiş sahtekarlık tespiti ve güvenlik izleme", "feature5": "Otomatik bildirimler ve veli iletişimi"}, "systemAdmin": {"title": "Sistem Yöneticileri", "subtitle": "Kurumsal seviye sistem yönetimi ve kontrolü", "feature1": "Küresel Veritabanı Yönetimi", "feature1Desc": "Çok okul veri gözetimi ve temizliği", "feature2": "Sistem Bakımı", "feature2Desc": "Otomatik güncellemeler ve sağlık izleme", "feature3": "Güvenlik Yönetimi", "feature3Desc": "Gelişmiş güvenlik politikaları ve erişim kontrolü", "feature4": "Platform Yapılandırması", "feature4Desc": "Sistem geneli ayarlar ve özelleştirme"}}, "cta": {"badge": "🚀 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON> mısınız?", "title": "Dijital Dönüşümünüzü Bugün Başlatın", "description": "Son teknoloji platformumuzla devam yönetimlerini devrimleştiren dünya çapındaki yüzlerce eğitim kurumuna katılın.", "benefit1": "Dakikalar İçinde Kurulum", "benefit2": "Kurumsal Güvenlik", "benefit3": "7/24 Destek", "startNow": "<PERSON><PERSON><PERSON>", "createAccount": "<PERSON><PERSON><PERSON>", "trustIndicator": "Dünya çapında eğitim kurumları için güvenilir bir sistem", "secure": "Banka Seviyesi Güvenlik", "realtime": "Gerçek Zamanlı Güncellemeler", "multilingual": "Ç<PERSON>", "cloud": "Bulut Tabanlı"}}, "loading": {"authenticating": "Kimlik doğrulanıyor...", "teacherDashboard": "Öğretmen Paneli yükleniyor...", "adminDashboard": "Yönetici Paneli yükleniyor...", "studentDashboard": "Öğrenci Paneli yükleniyor...", "roomData": "Oda bilgileri yükleniyor...", "recordingAttendance": "Yoklamanız kaydediliyor...", "students": "Öğrenciler yükleniyor...", "schoolInformation": "Okul bilgileri yükleniyor...", "blocksAndRooms": "Bloklar ve odalar yükleniyor...", "fraudDetectionDashboard": "Sahtekarlık tespit paneli yükleniyor...", "failedToLoadInitialData": "İlk veriler yüklenemedi. Lütfen sayfayı yenileyin.", "pleaseWait": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>in...", "processing": "İşleniyor...", "saving": "Kay<PERSON>ili<PERSON>r...", "loading": "Yükleniyor...", "fetchingData": "Veriler getiriliyor...", "initializing": "Başlatılıyor...", "verifying": "Doğrulanıyor...", "connecting": "Bağlanıyor...", "synchronizing": "Senkronize ediliyor...", "excuses": "Mazeretler yükleniyor...", "alerts": "Uyarılar yükleniyor...", "roomSettings": "Oda ayarları yükleniyor...", "verificationSettings": "Doğrulama ayarları yükleniyor...", "checkingMaintenanceStatus": "Bakım durumu kontrol ediliyor...", "checkingUserStatus": "Kullanıcı durumu kontrol ediliyor..."}, "cleanup": {"title": "Veritabanı Temizliği", "description": "Otomatik veritabanı temizliği ve veri saklama süresini yönetin", "retentionPolicy": "Veri Saklama Politikası", "retentionPolicyDescription": "Otomatik temizlik, bu saklama sürelerine göre eski verileri kaldırır", "currentStatistics": "<PERSON><PERSON><PERSON>", "currentStatisticsDescription": "Veritabanındaki mevcut kayıt sayıları", "manualCleanup": "<PERSON>", "manualCleanupDescription": "Anında temizlik işlemi başlatın", "automaticCleanup": "Otomatik Temizlik", "lastCleanup": "<PERSON>", "never": "<PERSON><PERSON><PERSON>", "runCleanupNow": "<PERSON><PERSON><PERSON>", "runCleanupNowDescription": "<PERSON><PERSON>, saklama a<PERSON>na göre eski kayıtları hemen temizleyecektir", "runCleanup": "Temizliği Çalıştır", "running": "Çalışıyor...", "cleanupCompleted": "Temizlik <PERSON>amlandı", "cleanupCompletedDescription": "{{total}} ka<PERSON><PERSON><PERSON> silindi: {{attendance}} devam, {{notifications}} bildirim, {{excuses}} mazeret, {{alerts}} uyarı", "cleanupFailed": "Temizlik başarısız oldu. Lütfen tekrar deneyin.", "noDataAvailable": "<PERSON><PERSON> mev<PERSON> de<PERSON>", "automaticCleanupEnabled": "Otomatik temizlik etkin", "automaticCleanupSchedule": "Her gün saat 02:00'da çalışır", "automaticCleanupBenefits": "Veritabanı boyutunu minimal tutar ve performansı artırır", "attendanceRecords": "<PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "excuses": "<PERSON><PERSON><PERSON><PERSON>", "locationAlerts": "Konum Uyarıları", "biometricCredentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feedbackSubmissions": "<PERSON><PERSON><PERSON><PERSON>", "systemLogs": "Sistem Günlükleri", "userActivityLogs": "Kullanıcı Etkinlik Günlükleri", "qrSessions": "QR Oturumları"}, "tablet": {"studentKioskMode": "Öğrenci Kiosk Modu", "qrDisplayMode": "QR Görüntüleme Modu", "kioskModeDescription": "Öğrenciler e-posta + biyometrik ile giriş ya<PERSON>bilir", "qrModeDescription": "Telefon taraması için QR kod görüntüle"}, "kiosk": {"title": "Öğ<PERSON><PERSON>", "enterEmailDescription": "<PERSON><PERSON><PERSON> ya<PERSON>ak için e-posta adresinizi girin", "verifyBiometricDescription": "Kimliğinizi biyometrik doğrulama ile onaylayın", "registerBiometricDescription": "<PERSON><PERSON><PERSON> bu cihazda biyometriklerinizi kaydedin", "successDescription": "<PERSON><PERSON><PERSON> başarıyla tamamlandı!", "emailLabel": "E-posta Adresi", "emailPlaceholder": "Okul e-posta adresinizi girin", "invalidEmail": "Lütfen geçerli bir e-posta adresi girin", "studentNotFound": "Öğrenci bulunamadı veya bu okula kayıtlı değil", "biometricsNotRegistered": "Biyometrik doğrulama kaydetmediniz. Lütfen önce profilinizde biyometrik kayıt yapın.", "noStudentSelected": "Doğrulama için <PERSON>çilmedi", "biometricNotSupported": "Bu cihaz biyometrik doğrulamayı desteklemiyor", "biometricVerificationFailed": "Biyometrik doğrulama başarısız. Lütfen tekrar deneyin.", "biometricRegistrationFailed": "Biyometrik kayıt başarısız. Lütfen tekrar deneyin.", "needToRegisterOnDevice": "Önce bu cihazda biyometriklerinizi kaydetmeniz gerekiyor. Yukarıdaki 'Biyometrik Ku<PERSON>' but<PERSON>una tıklayın.", "biometricRegistrationSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>şarılı", "biometricRegistrationSuccessDescription": "Artık bu cihazda biyometrik doğrulama kullanabilirsiniz", "biometricsRegisteredOnThisDevice": "Bu kiosk cihazında biyometrik kayıtlı", "biometricsNotRegisteredOnThisDevice": "Bu kiosk cihazında biyometrik kayıtlı değil", "deviceSpecificNote": "Biyometrik kayıt cihaza özeldir", "setupBiometrics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reregisterBiometrics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setupRequired": "<PERSON><PERSON><PERSON>", "setupRequiredDescription": "Biyometriklerinizi kaydetmek için yukarıdaki kurulum butonuna tıklayın", "setupFirstMessage": "Önce biyometrik kurun", "reregisterSetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reregisterSetupDescription": "Bu özel kiosk cihazında biyometriklerinizi tekrar kaydedin", "deviceSpecificSetupDescription": "Biyometrikler cihaza özeldir. Kullanmak istediğiniz her kiosk cihazında kayıt yapmanız gerekir.", "touchFingerprint": "Parmak izi sensörüne dokunun veya yüz tanımayı kullanın", "registerFingerprint": "Biyometriklerini<PERSON>", "registerFingerprintDescription": "Bu cihaz için tek seferlik kurulum", "firstTimeSetup": "İlk Ku<PERSON> Gerekli", "firstTimeSetupDescription": "G<PERSON><PERSON> yapmadan önce bu cihazda biyometriklerinizi kaydetmeniz gerekiyor. Bu tek seferlik bir kurulum.", "checkInSuccessful": "Giriş Başarılı!", "redirectingShortly": "Ana ekrana kısa süre içinde dönülüyor...", "checkInAnother": "Başka Öğrenci Girişi Yap", "verifying": "Doğrulanıyor...", "verifyNow": "Şimdi <PERSON>", "registering": "Kay<PERSON>ili<PERSON>r...", "registerNow": "<PERSON><PERSON>", "alreadyCheckedIn": "Zaten Giriş Yapıldı", "alreadyCheckedInDescription": "Bu oda için bugün zaten giriş yaptınız", "attendanceRecorded": "<PERSON><PERSON>", "attendanceRecordedDescription": "Devamınız başarı<PERSON> kaydedildi", "attendanceRecordingFailed": "<PERSON><PERSON> kaydedilemedi. Lütfen tekrar deneyin."}}