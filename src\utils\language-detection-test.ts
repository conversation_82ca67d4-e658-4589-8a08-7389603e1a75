/**
 * Test utility to verify language detection logic
 * This file can be used to test the language detection behavior
 */

export const testLanguageDetection = () => {
  console.log("=== Language Detection Test ===");
  
  // Test 1: Turkish browser language
  const mockNavigatorTurkish = {
    language: "tr-TR",
    languages: ["tr-TR", "tr", "en-US", "en"]
  };
  
  console.log("Test 1 - Turkish browser:");
  console.log("Navigator language:", mockNavigatorTurkish.language);
  console.log("Should detect as Turkish:", mockNavigatorTurkish.language.toLowerCase().startsWith("tr"));
  
  // Test 2: English browser language
  const mockNavigatorEnglish = {
    language: "en-US",
    languages: ["en-US", "en"]
  };
  
  console.log("\nTest 2 - English browser:");
  console.log("Navigator language:", mockNavigatorEnglish.language);
  console.log("Should detect as English:", !mockNavigatorEnglish.language.toLowerCase().startsWith("tr"));
  
  // Test 3: Other language (should default to English)
  const mockNavigatorOther = {
    language: "fr-FR",
    languages: ["fr-FR", "fr"]
  };
  
  console.log("\nTest 3 - French browser:");
  console.log("Navigator language:", mockNavigatorOther.language);
  console.log("Should default to English:", !mockNavigatorOther.language.toLowerCase().startsWith("tr"));
  
  // Test 4: Turkish with different locale
  const mockNavigatorTurkishCY = {
    language: "tr-CY",
    languages: ["tr-CY", "tr", "en"]
  };
  
  console.log("\nTest 4 - Turkish Cyprus browser:");
  console.log("Navigator language:", mockNavigatorTurkishCY.language);
  console.log("Should detect as Turkish:", mockNavigatorTurkishCY.language.toLowerCase().startsWith("tr"));
  
  console.log("\n=== Test Complete ===");
};

/**
 * Simulate the language detection logic used in SimpleLanguageContext
 */
export const simulateLanguageDetection = (
  storedLanguage: string | null,
  browserLanguage: string,
  profileLanguage?: string
): "en" | "tr" => {
  // If user has a profile language preference, use it
  if (profileLanguage && (profileLanguage === "tr" || profileLanguage === "en")) {
    return profileLanguage as "en" | "tr";
  }
  
  // If user has a stored preference, use it
  if (storedLanguage && (storedLanguage === "tr" || storedLanguage === "en")) {
    return storedLanguage as "en" | "tr";
  }
  
  // First-time user: detect from browser language
  if (browserLanguage.toLowerCase().startsWith("tr")) {
    return "tr";
  }
  
  return "en";
};

// Test scenarios
export const runLanguageDetectionTests = () => {
  console.log("=== Language Detection Logic Tests ===");
  
  const testCases = [
    {
      name: "First-time Turkish user",
      stored: null,
      browser: "tr-TR",
      profile: undefined,
      expected: "tr"
    },
    {
      name: "First-time English user",
      stored: null,
      browser: "en-US",
      profile: undefined,
      expected: "en"
    },
    {
      name: "First-time French user (should default to English)",
      stored: null,
      browser: "fr-FR",
      profile: undefined,
      expected: "en"
    },
    {
      name: "Returning user with Turkish preference",
      stored: "tr",
      browser: "en-US",
      profile: undefined,
      expected: "tr"
    },
    {
      name: "User with profile preference (Turkish)",
      stored: "en",
      browser: "en-US",
      profile: "tr",
      expected: "tr"
    },
    {
      name: "Turkish Cyprus user",
      stored: null,
      browser: "tr-CY",
      profile: undefined,
      expected: "tr"
    }
  ];
  
  testCases.forEach((testCase, index) => {
    const result = simulateLanguageDetection(
      testCase.stored,
      testCase.browser,
      testCase.profile
    );
    
    const passed = result === testCase.expected;
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log(`  Expected: ${testCase.expected}, Got: ${result} ${passed ? "✅" : "❌"}`);
  });
  
  console.log("=== Tests Complete ===");
};
