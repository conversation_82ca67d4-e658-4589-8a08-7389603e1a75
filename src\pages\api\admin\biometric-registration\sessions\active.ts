import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { deviceId } = req.body;

    if (!deviceId) {
      return res.status(400).json({ error: 'Device ID is required' });
    }

    // Get active registration sessions for this device
    const { data: sessions, error } = await supabase
      .from('biometric_registration_sessions')
      .select(`
        id,
        session_name,
        device_id,
        device_name,
        status,
        created_at,
        updated_at,
        total_students,
        registered_students,
        failed_students,
        biometric_registration_students (
          id,
          student_email,
          status,
          registration_attempts,
          registered_at
        )
      `)
      .eq('device_id', deviceId)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching active registration sessions:', error);
      return res.status(500).json({ error: 'Failed to fetch registration sessions' });
    }

    // Calculate progress for each session
    const sessionsWithProgress = sessions?.map(session => {
      const students = session.biometric_registration_students || [];
      const registered = students.filter(s => s.status === 'registered').length;
      const failed = students.filter(s => s.status === 'failed').length;
      const pending = students.filter(s => s.status === 'pending').length;
      
      return {
        ...session,
        progress: {
          total: students.length,
          registered,
          failed,
          pending,
          percentage: students.length > 0 ? Math.round((registered / students.length) * 100) : 0
        }
      };
    }) || [];

    res.status(200).json(sessionsWithProgress);
  } catch (error) {
    console.error('Error in active sessions API:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
